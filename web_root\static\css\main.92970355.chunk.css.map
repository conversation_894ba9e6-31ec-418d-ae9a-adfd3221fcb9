{"version": 3, "sources": ["material-dashboard-react.css"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;EAcE,CACF,SACE,yBAAgC,CAChC,gBAAiB,CACjB,oBACF,CAEA,+FAIE,yBACF,CAEA,+BAIE,sBAAuB,CAIvB,0BAA2B,CAC3B,eAAgB,CAChB,iBACF,CAEA,UACE,wBACF,CAEA,yEAGE,aAAc,CAKd,YACF,CAEA,UACE,mBAAwB,CACxB,aACF,CACA,OACE,kCAAmC,CACnC,iCACF,CACA,KAGE,QAAS,CACT,6CAAiD,CACjD,eAAgB,CAChB,iBACF,CAEA,iDAEE,qBACF,CAEA,MACE,aACF,CAEA,GACE,aAAc,CACd,kBACF,CAEA,GACE,eACF,CAEA,GACE,iBAAkB,CAElB,kBACF,CAEA,MAJE,iBAOF,CAHA,GACE,eAEF,CAEA,GACE,gBAAiB,CACjB,iBAAkB,CAClB,kBACF,CAEA,GACE,aAAc,CACd,wBAAyB,CACzB,eACF,CAEA,KACE,qBAAyB,CACzB,aACF,CAEA,aACE,iBACF,CAEA,uBAOE,mDAAuD,CACvD,eAAgB,CAChB,iBACF,CAEA,EACE,aAEF,CAEA,kBAHE,oBAOF,CAJA,gBAEE,aAEF,CAEA,OACE,eACF,CAEA,EACE,+CAAmD,CACnD,uCACF,CAEA,OACE,SACF,CAEA,+QAWE,mBACF,CAEA,OACE,kBAAmB,CACnB,cACF,CAEA,OACE,eAGF,CAEA,aAJE,cAAe,CACf,mBAQF,CALA,MAGE,UAAc,CACd,eACF,CAEA,OACE,cACF,CAEA,UACE,eAAgB,CAChB,SAAU,CACV,eACF,CAEA,aACE,oBACF,CAEA,eACE,aAAc,CACd,YAAa,CACb,eAAgB,CAChB,cAAe,CACf,wBAAyB,CACzB,iBAAkB,CAElB,iBAAkB,CAClB,aACF,CAEA,oCALE,oBAOF,CAEA,yBACE,UAEE,iBAAkB,CAClB,iBACF,CAEA,WACE,WAAY,CACZ,UAAW,CACX,cAAe,CACf,SAAU,CACV,KAAM,CACN,SAAU,CACV,WAAY,CACZ,UAAW,CACX,YAAa,CACb,iBACF,CACF,CACA,cACE,mDAAuD,CACvD,eAAgB,CAChB,iBAAkB,CAClB,cAAe,CACf,SAAU,CACV,OAAQ,CACR,UAAW,CACX,yBAA8B,CAC9B,YAAa,CACb,yBAA0B,CAC1B,iBAAkB,CAClB,SAAU,CACV,uCACF,CAEA,gEAEE,oBACF,CAEA,wCAEE,mBAAqB,CACrB,2BAA6B,CAC7B,wBAA0B,CAC1B,oBACF,CAEA,sBACE,UAAc,CACd,YAAa,CACb,yBAA0B,CAC1B,UACF,CAEA,6BACE,UAAW,CACX,SAAU,CACV,WAAY,CACZ,sBAAwB,CACxB,cAAe,CACf,iBAAkB,CAClB,qBAA0B,CAC1B,oBAAqB,CACrB,sCAA2C,CAC3C,eAAgB,CAChB,iBACF,CAEA,8BACE,UACF,CAEA,sCACE,UACF,CAEA,iIAGE,UAAc,CACd,iBACF,CAEA,kBACE,eAAgB,CAChB,UAAW,CACX,YAAa,CACb,aACF,CAEA,gFAEE,eACF,CACA,qBACE,qBAAyB,CACzB,iBAAkB,CAClB,cAAe,CACf,oBAAqB,CACrB,WAAY,CACZ,gBAAiB,CACjB,iBAAkB,CAClB,UAAW,CACX,mCACF,CAEA,uDAEE,iBACF,CAEA,4BACE,wBACF,CAEA,0BACE,wBACF,CAEA,2BACE,wBACF,CAEA,4BACE,wBACF,CAEA,yBACE,wBACF,CAEA,iBACE,cAAe,CACf,WACF,CACA,gCACE,aAAc,CACd,aAAgB,CAChB,SAAU,CACV,UACF,CAEA,kGAGE,UAAW,CACX,WAAY,CACZ,kBAAmB,CACnB,SAAY,CACZ,iBACF,CAEA,oCACE,QACF,CAEA,8GAGE,iBACF,CACA,8BACE,WAAY,CACZ,gBAAiB,CACjB,cAAe,CACf,eAAgB,CAChB,iBAAkB,CAClB,wBACF,CAEA,kCACE,UAAW,CACX,oBAAqB,CACrB,eAAgB,CAChB,aAAc,CACd,aACF,CAEA,kCACE,iBACF,CAEA,gDACE,iBAAkB,CAClB,QACF,CAEA,oFAEE,iBACF,CACA,qEACE,eAAgB,CAChB,cAAe,CACf,4BAA6B,CAC7B,eAAgB,CAChB,QACF,CAEA,6CACE,cAAe,CACf,iBAAkB,CAClB,kBAAmB,CACnB,qBAAsB,CACtB,qBAAsB,CAGtB,SAAU,CACV,cAAe,CACf,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAChB,SACF,CAEA,iDACE,eACF,CACA,sGAEE,gCACF,CAEA,kDAEE,iBAAqB,CACrB,qBACF,CACA,uCAKE,0BAA2B,CAC3B,QAAS,CACT,SAAU,CACV,oBAAqB,CACrB,YACF,CAEA,8CACE,oCAAsC,CACtC,qCAA2C,CAC3C,iCAAmC,CACnC,WAAY,CACZ,QACF,CACA,6CACE,oCAAsC,CACtC,2BAAgC,CAChC,iCAAmC,CACnC,WACF,CAEA,2FAEE,UAAW,CACX,oBAAqB,CACrB,iBAAkB,CAClB,QAAS,CACT,UAAW,CACX,0BAA2B,CAC3B,kCAAmC,CACnC,+BACF,CAEA,4CACE,aAAc,CACd,kBAAmB,CACnB,SAAU,CAKV,0BAA2B,CAC3B,oBACF,CACA,+BACE,UAAW,CACX,MAAS,CACT,yBACF,CACA,8CACE,UAAW,CACX,SACF,CACA,EACE,+BACF", "file": "main.92970355.chunk.css", "sourcesContent": ["/*!\r\n\r\n =========================================================\r\n * Material Dashboard React - v1.8.0 based on Material Dashboard - v1.2.0\r\n =========================================================\r\n\r\n * Product Page: http://www.creative-tim.com/product/material-dashboard-react\r\n * Copyright 2019 Creative Tim (http://www.creative-tim.com)\r\n * Licensed under MIT (https://github.com/creativetimofficial/material-dashboard-react/blob/master/LICENSE.md)\r\n\r\n =========================================================\r\n\r\n * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\r\n\r\n */\r\n.ct-grid {\r\n  stroke: rgba(255, 255, 255, 0.2);\r\n  stroke-width: 1px;\r\n  stroke-dasharray: 2px;\r\n}\r\n\r\n.ct-series-a .ct-point,\r\n.ct-series-a .ct-line,\r\n.ct-series-a .ct-bar,\r\n.ct-series-a .ct-slice-donut {\r\n  stroke: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.ct-label.ct-horizontal.ct-end {\r\n  -webkit-box-align: flex-start;\r\n  -webkit-align-items: flex-start;\r\n  -ms-flex-align: flex-start;\r\n  align-items: flex-start;\r\n  -webkit-box-pack: flex-start;\r\n  -webkit-justify-content: flex-start;\r\n  -ms-flex-pack: flex-start;\r\n  justify-content: flex-start;\r\n  text-align: left;\r\n  text-anchor: start;\r\n}\r\n\r\n.ct-label {\r\n  color: rgba(255, 255, 255, 0.7);\r\n}\r\n\r\n.ct-chart-line .ct-label,\r\n.ct-chart-bar .ct-label,\r\n.ct-chart-pie .ct-label {\r\n  display: block;\r\n  display: -webkit-box;\r\n  display: -moz-box;\r\n  display: -ms-flexbox;\r\n  display: -webkit-flex;\r\n  display: flex;\r\n}\r\n\r\n.ct-label {\r\n  fill: rgba(0, 0, 0, 0.4);\r\n  line-height: 1;\r\n}\r\nhtml * {\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n}\r\nbody {\r\n  background-color: #eeeeee;\r\n  color: #3c4858;\r\n  margin: 0;\r\n  font-family: Roboto, Helvetica, Arial, sans-serif;\r\n  font-weight: 300;\r\n  line-height: 1.5em;\r\n}\r\n\r\nblockquote footer:before,\r\nblockquote small:before {\r\n  content: \"\\2014 \\00A0\";\r\n}\r\n\r\nsmall {\r\n  font-size: 80%;\r\n}\r\n\r\nh1 {\r\n  font-size: 3em;\r\n  line-height: 1.15em;\r\n}\r\n\r\nh2 {\r\n  font-size: 2.4em;\r\n}\r\n\r\nh3 {\r\n  font-size: 1.825em;\r\n  line-height: 1.4em;\r\n  margin: 20px 0 10px;\r\n}\r\n\r\nh4 {\r\n  font-size: 1.3em;\r\n  line-height: 1.4em;\r\n}\r\n\r\nh5 {\r\n  font-size: 1.25em;\r\n  line-height: 1.4em;\r\n  margin-bottom: 15px;\r\n}\r\n\r\nh6 {\r\n  font-size: 1em;\r\n  text-transform: uppercase;\r\n  font-weight: 500;\r\n}\r\n\r\nbody {\r\n  background-color: #eeeeee;\r\n  color: #3c4858;\r\n}\r\n\r\nblockquote p {\r\n  font-style: italic;\r\n}\r\n\r\nbody,\r\nh1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6 {\r\n  font-family: \"Roboto\", \"Helvetica\", \"Arial\", sans-serif;\r\n  font-weight: 300;\r\n  line-height: 1.5em;\r\n}\r\n\r\na {\r\n  color: #9c27b0;\r\n  text-decoration: none;\r\n}\r\n\r\na:hover,\r\na:focus {\r\n  color: #89229b;\r\n  text-decoration: none;\r\n}\r\n\r\nlegend {\r\n  border-bottom: 0;\r\n}\r\n\r\n* {\r\n  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);\r\n  -webkit-tap-highlight-color: transparent;\r\n}\r\n\r\n*:focus {\r\n  outline: 0;\r\n}\r\n\r\na:focus,\r\na:active,\r\nbutton:active,\r\nbutton:focus,\r\nbutton:hover,\r\nbutton::-moz-focus-inner,\r\ninput[type=\"reset\"]::-moz-focus-inner,\r\ninput[type=\"button\"]::-moz-focus-inner,\r\ninput[type=\"submit\"]::-moz-focus-inner,\r\nselect::-moz-focus-inner,\r\ninput[type=\"file\"] > input[type=\"button\"]::-moz-focus-inner {\r\n  outline: 0 !important;\r\n}\r\n\r\nlegend {\r\n  margin-bottom: 20px;\r\n  font-size: 21px;\r\n}\r\n\r\noutput {\r\n  padding-top: 8px;\r\n  font-size: 14px;\r\n  line-height: 1.42857;\r\n}\r\n\r\nlabel {\r\n  font-size: 14px;\r\n  line-height: 1.42857;\r\n  color: #aaaaaa;\r\n  font-weight: 400;\r\n}\r\n\r\nfooter {\r\n  padding: 15px 0;\r\n}\r\n\r\nfooter ul {\r\n  margin-bottom: 0;\r\n  padding: 0;\r\n  list-style: none;\r\n}\r\n\r\nfooter ul li {\r\n  display: inline-block;\r\n}\r\n\r\nfooter ul li a {\r\n  color: inherit;\r\n  padding: 15px;\r\n  font-weight: 500;\r\n  font-size: 12px;\r\n  text-transform: uppercase;\r\n  border-radius: 3px;\r\n  text-decoration: none;\r\n  position: relative;\r\n  display: block;\r\n}\r\n\r\nfooter ul li a:hover {\r\n  text-decoration: none;\r\n}\r\n\r\n@media (max-width: 991px) {\r\n  body,\r\n  html {\r\n    position: relative;\r\n    overflow-x: hidden;\r\n  }\r\n\r\n  #bodyClick {\r\n    height: 100%;\r\n    width: 100%;\r\n    position: fixed;\r\n    opacity: 0;\r\n    top: 0;\r\n    left: auto;\r\n    right: 260px;\r\n    content: \"\";\r\n    z-index: 9999;\r\n    overflow-x: hidden;\r\n  }\r\n}\r\n.fixed-plugin {\r\n  font-family: \"Roboto\", \"Helvetica\", \"Arial\", sans-serif;\r\n  font-weight: 300;\r\n  line-height: 1.5em;\r\n  position: fixed;\r\n  top: 180px;\r\n  right: 0;\r\n  width: 64px;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  z-index: 1031;\r\n  border-radius: 8px 0 0 8px;\r\n  text-align: center;\r\n  top: 120px;\r\n  .badge-primary-background-color: #9c27b0;\r\n}\r\n\r\n.fixed-plugin .SocialMediaShareButton,\r\n.fixed-plugin .github-btn {\r\n  display: inline-block;\r\n}\r\n\r\n.fixed-plugin li > a,\r\n.fixed-plugin .badge {\r\n  transition: all 0.34s;\r\n  -webkit-transition: all 0.34s;\r\n  -moz-transition: all 0.34s;\r\n  text-decoration: none;\r\n}\r\n\r\n.fixed-plugin .fa-cog {\r\n  color: #ffffff;\r\n  padding: 10px;\r\n  border-radius: 0 0 6px 6px;\r\n  width: auto;\r\n}\r\n\r\n.fixed-plugin .dropdown-menu {\r\n  right: 80px;\r\n  left: auto;\r\n  width: 290px;\r\n  border-radius: 0.1875rem;\r\n  padding: 0 10px;\r\n  position: absolute;\r\n  color: rgba(0, 0, 0, 0.87);\r\n  display: inline-block;\r\n  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.14);\r\n  background: #fff;\r\n  border-radius: 3px;\r\n}\r\n\r\n.fixed-plugin .fa-circle-thin {\r\n  color: #ffffff;\r\n}\r\n\r\n.fixed-plugin .active .fa-circle-thin {\r\n  color: #00bbff;\r\n}\r\n\r\n.fixed-plugin .dropdown-menu > .active > a,\r\n.fixed-plugin .dropdown-menu > .active > a:hover,\r\n.fixed-plugin .dropdown-menu > .active > a:focus {\r\n  color: #777777;\r\n  text-align: center;\r\n}\r\n\r\n.fixed-plugin img {\r\n  border-radius: 0;\r\n  width: 100%;\r\n  height: 100px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.fixed-plugin .dropdown-menu li > a:hover,\r\n.fixed-plugin .dropdown-menu li > a:focus {\r\n  box-shadow: none;\r\n}\r\n.fixed-plugin .badge {\r\n  border: 3px solid #ffffff;\r\n  border-radius: 50%;\r\n  cursor: pointer;\r\n  display: inline-block;\r\n  height: 23px;\r\n  margin-right: 5px;\r\n  position: relative;\r\n  width: 23px;\r\n  background-color: rgba(30, 30, 30, 0.97);\r\n}\r\n\r\n.fixed-plugin .badge.active,\r\n.fixed-plugin .badge:hover {\r\n  border-color: #00bbff;\r\n}\r\n\r\n.fixed-plugin .badge-purple {\r\n  background-color: #9c27b0;\r\n}\r\n\r\n.fixed-plugin .badge-blue {\r\n  background-color: #00bcd4;\r\n}\r\n\r\n.fixed-plugin .badge-green {\r\n  background-color: #4caf50;\r\n}\r\n\r\n.fixed-plugin .badge-orange {\r\n  background-color: #ff9800;\r\n}\r\n\r\n.fixed-plugin .badge-red {\r\n  background-color: #f44336;\r\n}\r\n\r\n.fixed-plugin h5 {\r\n  font-size: 14px;\r\n  margin: 10px;\r\n}\r\n.fixed-plugin .dropdown-menu li {\r\n  display: block;\r\n  padding: 4px 0px;\r\n  width: 25%;\r\n  float: left;\r\n}\r\n\r\n.fixed-plugin li.adjustments-line,\r\n.fixed-plugin li.header-title,\r\n.fixed-plugin li.button-container {\r\n  width: 100%;\r\n  height: 50px;\r\n  min-height: inherit;\r\n  padding: 0px;\r\n  text-align: center;\r\n}\r\n\r\n.fixed-plugin li.adjustments-line p {\r\n  margin: 0;\r\n}\r\n\r\n.fixed-plugin li.adjustments-line div,\r\n.fixed-plugin li.header-title div,\r\n.fixed-plugin li.button-container div {\r\n  margin-bottom: 5px;\r\n}\r\n.fixed-plugin li.header-title {\r\n  height: 30px;\r\n  line-height: 25px;\r\n  font-size: 12px;\r\n  font-weight: 600;\r\n  text-align: center;\r\n  text-transform: uppercase;\r\n}\r\n\r\n.fixed-plugin .adjustments-line p {\r\n  float: left;\r\n  display: inline-block;\r\n  margin-bottom: 0;\r\n  font-size: 1em;\r\n  color: #3c4858;\r\n}\r\n\r\n.fixed-plugin .adjustments-line a {\r\n  color: transparent;\r\n}\r\n\r\n.fixed-plugin .adjustments-line a .badge-colors {\r\n  position: relative;\r\n  top: -2px;\r\n}\r\n\r\n.fixed-plugin .adjustments-line a a:hover,\r\n.fixed-plugin .adjustments-line a a:focus {\r\n  color: transparent;\r\n}\r\n.fixed-plugin .adjustments-line .dropdown-menu > li.adjustments-line > a {\r\n  padding-right: 0;\r\n  padding-left: 0;\r\n  border-bottom: 1px solid #ddd;\r\n  border-radius: 0;\r\n  margin: 0;\r\n}\r\n\r\n.fixed-plugin .dropdown-menu > li > a.img-holder {\r\n  font-size: 16px;\r\n  text-align: center;\r\n  border-radius: 10px;\r\n  background-color: #fff;\r\n  border: 3px solid #fff;\r\n  padding-left: 0;\r\n  padding-right: 0;\r\n  opacity: 1;\r\n  cursor: pointer;\r\n  display: block;\r\n  max-height: 100px;\r\n  overflow: hidden;\r\n  padding: 0;\r\n}\r\n\r\n.fixed-plugin .dropdown-menu > li > a.img-holder img {\r\n  margin-top: auto;\r\n}\r\n.fixed-plugin .dropdown-menu > li:hover > a.img-holder,\r\n.fixed-plugin .dropdown-menu > li:focus > a.img-holder {\r\n  border-color: rgba(0, 187, 255, 0.53);\r\n}\r\n\r\n.fixed-plugin .dropdown-menu > .active > a.img-holder,\r\n.fixed-plugin .dropdown-menu > .active > a.img-holder {\r\n  border-color: #00bbff;\r\n  background-color: #ffffff;\r\n}\r\n.fixed-plugin .dropdown .dropdown-menu {\r\n  -webkit-transform: translateY(-15%);\r\n  -moz-transform: translateY(-15%);\r\n  -o-transform: translateY(-15%);\r\n  -ms-transform: translateY(-15%);\r\n  transform: translateY(-15%);\r\n  top: 27px;\r\n  opacity: 0;\r\n  transform-origin: 0 0;\r\n  display: none;\r\n}\r\n\r\n.fixed-plugin .dropdown .dropdown-menu:before {\r\n  border-bottom: 0.4em solid transparent;\r\n  border-left: 0.4em solid rgba(0, 0, 0, 0.2);\r\n  border-top: 0.4em solid transparent;\r\n  right: -16px;\r\n  top: 46px;\r\n}\r\n.fixed-plugin .dropdown .dropdown-menu:after {\r\n  border-bottom: 0.4em solid transparent;\r\n  border-left: 0.4em solid #ffffff;\r\n  border-top: 0.4em solid transparent;\r\n  right: -16px;\r\n}\r\n\r\n.fixed-plugin .dropdown .dropdown-menu:before,\r\n.fixed-plugin .dropdown .dropdown-menu:after {\r\n  content: \"\";\r\n  display: inline-block;\r\n  position: absolute;\r\n  top: 46px;\r\n  width: 16px;\r\n  transform: translateY(-50%);\r\n  -webkit-transform: translateY(-50%);\r\n  -moz-transform: translateY(-50%);\r\n}\r\n\r\n.fixed-plugin .dropdown.show .dropdown-menu {\r\n  display: block;\r\n  visibility: visible;\r\n  opacity: 1;\r\n  -webkit-transform: translateY(-13%);\r\n  -moz-transform: translateY(-13%);\r\n  -o-transform: translateY(-13%);\r\n  -ms-transform: translateY(-13%);\r\n  transform: translateY(-13%);\r\n  transform-origin: 0 0;\r\n}\r\n.fixed-plugin.rtl-fixed-plugin {\r\n  right: auto;\r\n  left: 0px;\r\n  border-radius: 0 8px 8px 0;\r\n}\r\n.fixed-plugin.rtl-fixed-plugin .dropdown-menu {\r\n  right: auto;\r\n  left: 80px;\r\n}\r\n* {\r\n  letter-spacing: normal !important;\r\n}\r\n"]}