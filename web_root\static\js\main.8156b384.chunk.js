/*! For license information please see main.8156b384.chunk.js.LICENSE.txt */
(this["webpackJsonpnano-portal"]=this["webpackJsonpnano-portal"]||[]).push([[0],{203:function(e,a,t){e.exports=t.p+"static/media/login_background.327add31.jpg"},315:function(e,a,t){e.exports=t.p+"static/media/sidebar.18c01f03.jpg"},316:function(e,a,t){e.exports=t.p+"static/media/nano_white.a92c198a.svg"},346:function(e,a,t){e.exports=t(523)},522:function(e,a,t){},523:function(e,a,t){"use strict";t.r(a);var n=t(1),o=t(22),l=t(23),r=t(31),c=t(30),i=t(0),s=t.n(i),u=t(27),m=t.n(u),d=t(320),p=t(606),f=t(319),b=t(42),g=t(565),E=t(2),h=function(e){e=(e+="").replace("#","");if(!/[0-9A-Fa-f]/g.test(e)||3!==e.length&&6!==e.length)throw new Error("input is not a valid hex color.");if(3===e.length){var a=e[0],t=e[1],n=e[2];e=a+a+t+t+n+n}var o=(e=e.toUpperCase(e))[0]+e[1],l=e[2]+e[3],r=e[4]+e[5];return parseInt(o,16)+", "+parseInt(l,16)+", "+parseInt(r,16)},v={transition:"all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1)"},y={paddingRight:"15px",paddingLeft:"15px",marginRight:"auto",marginLeft:"auto"},x={fontFamily:'"Roboto", "Helvetica", "Arial", sans-serif',fontWeight:"300",lineHeight:"1.5em"},k=["#9c27b0","#ab47bc","#8e24aa","#af2cc5"],C=["#ff9800","#ffa726","#fb8c00","#ffa21a"],S=["#f44336","#ef5350","#e53935","#f55a4e"],O=["#4caf50","#66bb6a","#43a047","#5cb860"],j=["#00acc1","#26c6da","#00acc1","#00d3ee"],w=["#e91e63","#ec407a","#d81b60","#eb3573"],_=["#999","#777","#3C4858","#AAAAAA","#D2D2D2","#DDD","#b4b4b4","#555555","#333","#a9afbb","#eee","#e7e7e7"],N={boxShadow:"0 10px 30px -12px rgba("+h("#000")+", 0.42), 0 4px 25px 0px rgba("+h("#000")+", 0.12), 0 8px 10px -5px rgba("+h("#000")+", 0.2)"},R={boxShadow:"0 4px 20px 0 rgba("+h("#000")+",.14), 0 7px 10px -5px rgba("+h(k[0])+",.4)"},T={boxShadow:"0 4px 20px 0 rgba("+h("#000")+",.14), 0 7px 10px -5px rgba("+h(j[0])+",.4)"},I={boxShadow:"0 4px 20px 0 rgba("+h("#000")+",.14), 0 7px 10px -5px rgba("+h(O[0])+",.4)"},D={boxShadow:"0 4px 20px 0 rgba("+h("#000")+",.14), 0 7px 10px -5px rgba("+h(C[0])+",.4)"},A={boxShadow:"0 4px 20px 0 rgba("+h("#000")+",.14), 0 7px 10px -5px rgba("+h(S[0])+",.4)"},P={boxShadow:"0 4px 20px 0 rgba("+h("#000")+",.14), 0 7px 10px -5px rgba("+h(w[0])+",.4)"},F=Object(E.a)({background:"linear-gradient(60deg, "+C[1]+", "+C[2]+")"},D),B=Object(E.a)({background:"linear-gradient(60deg, "+O[1]+", "+O[2]+")"},I),M=Object(E.a)({background:"linear-gradient(60deg, "+S[1]+", "+S[2]+")"},A),z=Object(E.a)({background:"linear-gradient(60deg, "+j[1]+", "+j[2]+")"},T),W=Object(E.a)({background:"linear-gradient(60deg, "+k[1]+", "+k[2]+")"},R),q=Object(E.a)({background:"linear-gradient(60deg, "+w[1]+", "+w[2]+")"},P),H=(Object(E.a)({margin:"0 20px 10px",paddingTop:"10px",borderTop:"1px solid "+_[10],height:"auto"},x),h("#000"),h("#000"),{border:"0",borderRadius:"3px",boxShadow:"0 10px 20px -12px rgba("+h("#000")+", 0.42), 0 3px 20px 0px rgba("+h("#000")+", 0.12), 0 8px 10px -5px rgba("+h("#000")+", 0.2)",padding:"10px 0",transition:"all 150ms ease 0s"}),L={color:_[2],textDecoration:"none",fontWeight:"300",marginTop:"30px",marginBottom:"25px",minHeight:"32px",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif","& small":{color:_[1],fontWeight:"400",lineHeight:"1"}},U=(Object(E.a)(Object(E.a)({},L),{},{marginTop:"0",marginBottom:"3px",minHeight:"auto","& a":Object(E.a)(Object(E.a)({},L),{},{marginTop:".625rem",marginBottom:"0.75rem",minHeight:"auto"})}),{defaultFontStyle:Object(E.a)(Object(E.a)({},x),{},{fontSize:"14px"}),defaultHeaderMargins:{marginTop:"20px",marginBottom:"10px"},quote:{padding:"10px 20px",margin:"0 0 20px",fontSize:"17.5px",borderLeft:"5px solid "+_[10]},quoteText:{margin:"0 0 10px",fontStyle:"italic"},quoteAuthor:{display:"block",fontSize:"80%",lineHeight:"1.42857143",color:_[1]},mutedText:{color:_[1]},primaryText:{color:k[0]},infoText:{color:j[0]},successText:{color:O[0]},warningText:{color:C[0]},dangerText:{color:S[0]}}),V=Object(g.a)(U);function G(e){var a=V(),t=e.children;return s.a.createElement("div",{className:a.defaultFontStyle+" "+a.dangerText},t)}var $=t(39),Z=t(283),Y=(t(351),t(4)),J=t(33),Q=t.n(J),K=t(614),X=t(587),ee=t(588),ae=t(586),te=t(585),ne=t(323),oe=t(99),le=t(287),re=t.n(le),ce=t(609),ie=t(288),se=t.n(ie),ue=t(567),me={button:{minHeight:"auto",minWidth:"auto",backgroundColor:_[0],color:"#FFF",boxShadow:"0 2px 2px 0 rgba("+h(_[0])+", 0.14), 0 3px 1px -2px rgba("+h(_[0])+", 0.2), 0 1px 5px 0 rgba("+h(_[0])+", 0.12)",border:"none",borderRadius:"3px",position:"relative",padding:"12px 30px",margin:".3125rem 1px",fontSize:"12px",fontWeight:"400",textTransform:"uppercase",letterSpacing:"0",willChange:"box-shadow, transform",transition:"box-shadow 0.2s cubic-bezier(0.4, 0, 1, 1), background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1)",lineHeight:"1.42857143",textAlign:"center",whiteSpace:"nowrap",verticalAlign:"middle",touchAction:"manipulation",cursor:"pointer","&:hover,&:focus":{color:"#FFF",backgroundColor:_[0],boxShadow:"0 14px 26px -12px rgba("+h(_[0])+", 0.42), 0 4px 23px 0px rgba("+h("#000")+", 0.12), 0 8px 10px -5px rgba("+h(_[0])+", 0.2)"},"& .fab,& .fas,& .far,& .fal, &.material-icons":{position:"relative",display:"inline-block",top:"0",marginTop:"-1em",marginBottom:"-1em",fontSize:"1.1rem",marginRight:"4px",verticalAlign:"middle"},"& svg":{position:"relative",display:"inline-block",top:"0",width:"18px",height:"18px",marginRight:"4px",verticalAlign:"middle"},"&$justIcon":{"& .fab,& .fas,& .far,& .fal,& .material-icons":{marginTop:"0px",position:"absolute",width:"100%",transform:"none",left:"0px",top:"0px",height:"100%",lineHeight:"41px",fontSize:"20px"}}},white:{"&,&:focus,&:hover":{backgroundColor:"#FFF",color:_[0]}},rose:{backgroundColor:w[0],boxShadow:"0 2px 2px 0 rgba("+h(w[0])+", 0.14), 0 3px 1px -2px rgba("+h(w[0])+", 0.2), 0 1px 5px 0 rgba("+h(w[0])+", 0.12)","&:hover,&:focus":{backgroundColor:w[0],boxShadow:"0 14px 26px -12px rgba("+h(w[0])+", 0.42), 0 4px 23px 0px rgba("+h("#000")+", 0.12), 0 8px 10px -5px rgba("+h(w[0])+", 0.2)"}},primary:{backgroundColor:k[0],boxShadow:"0 2px 2px 0 rgba("+h(k[0])+", 0.14), 0 3px 1px -2px rgba("+h(k[0])+", 0.2), 0 1px 5px 0 rgba("+h(k[0])+", 0.12)","&:hover,&:focus":{backgroundColor:k[0],boxShadow:"0 14px 26px -12px rgba("+h(k[0])+", 0.42), 0 4px 23px 0px rgba("+h("#000")+", 0.12), 0 8px 10px -5px rgba("+h(k[0])+", 0.2)"}},info:{backgroundColor:j[0],boxShadow:"0 2px 2px 0 rgba("+h(j[0])+", 0.14), 0 3px 1px -2px rgba("+h(j[0])+", 0.2), 0 1px 5px 0 rgba("+h(j[0])+", 0.12)","&:hover,&:focus":{backgroundColor:j[0],boxShadow:"0 14px 26px -12px rgba("+h(j[0])+", 0.42), 0 4px 23px 0px rgba("+h("#000")+", 0.12), 0 8px 10px -5px rgba("+h(j[0])+", 0.2)"}},success:{backgroundColor:O[0],boxShadow:"0 2px 2px 0 rgba("+h(O[0])+", 0.14), 0 3px 1px -2px rgba("+h(O[0])+", 0.2), 0 1px 5px 0 rgba("+h(O[0])+", 0.12)","&:hover,&:focus":{backgroundColor:O[0],boxShadow:"0 14px 26px -12px rgba("+h(O[0])+", 0.42), 0 4px 23px 0px rgba("+h("#000")+", 0.12), 0 8px 10px -5px rgba("+h(O[0])+", 0.2)"}},warning:{backgroundColor:C[0],boxShadow:"0 2px 2px 0 rgba("+h(C[0])+", 0.14), 0 3px 1px -2px rgba("+h(C[0])+", 0.2), 0 1px 5px 0 rgba("+h(C[0])+", 0.12)","&:hover,&:focus":{backgroundColor:C[0],boxShadow:"0 14px 26px -12px rgba("+h(C[0])+", 0.42), 0 4px 23px 0px rgba("+h("#000")+", 0.12), 0 8px 10px -5px rgba("+h(C[0])+", 0.2)"}},danger:{backgroundColor:S[0],boxShadow:"0 2px 2px 0 rgba("+h(S[0])+", 0.14), 0 3px 1px -2px rgba("+h(S[0])+", 0.2), 0 1px 5px 0 rgba("+h(S[0])+", 0.12)","&:hover,&:focus":{backgroundColor:S[0],boxShadow:"0 14px 26px -12px rgba("+h(S[0])+", 0.42), 0 4px 23px 0px rgba("+h("#000")+", 0.12), 0 8px 10px -5px rgba("+h(S[0])+", 0.2)"}},simple:{"&,&:focus,&:hover":{color:"#FFF",background:"transparent",boxShadow:"none"},"&$rose":{"&,&:focus,&:hover,&:visited":{color:w[0]}},"&$primary":{"&,&:focus,&:hover,&:visited":{color:k[0]}},"&$info":{"&,&:focus,&:hover,&:visited":{color:j[0]}},"&$success":{"&,&:focus,&:hover,&:visited":{color:O[0]}},"&$warning":{"&,&:focus,&:hover,&:visited":{color:C[0]}},"&$danger":{"&,&:focus,&:hover,&:visited":{color:S[0]}}},transparent:{"&,&:focus,&:hover":{color:"inherit",background:"transparent",boxShadow:"none"}},disabled:{opacity:"0.65",pointerEvents:"none"},lg:{padding:"1.125rem 2.25rem",fontSize:"0.875rem",lineHeight:"1.333333",borderRadius:"0.2rem"},sm:{padding:"0.40625rem 1.25rem",fontSize:"0.6875rem",lineHeight:"1.5",borderRadius:"0.2rem"},round:{borderRadius:"30px"},block:{width:"100% !important"},link:{"&,&:hover,&:focus":{backgroundColor:"transparent",color:_[0],boxShadow:"none"}},justIcon:{paddingLeft:"12px",paddingRight:"12px",fontSize:"20px",height:"41px",minWidth:"41px",width:"41px","& .fab,& .fas,& .far,& .fal,& svg,& .material-icons":{marginRight:"0px"},"&$lg":{height:"57px",minWidth:"57px",width:"57px",lineHeight:"56px","& .fab,& .fas,& .far,& .fal,& .material-icons":{fontSize:"32px",lineHeight:"56px"},"& svg":{width:"32px",height:"32px"}},"&$sm":{height:"30px",minWidth:"30px",width:"30px","& .fab,& .fas,& .far,& .fal,& .material-icons":{fontSize:"17px",lineHeight:"29px"},"& svg":{width:"17px",height:"17px"}}}},de=["color","round","children","disabled","simple","size","block","link","justIcon","className","muiClasses"],pe=Object(g.a)(me);function fe(e){var a,t=pe(),n=e.color,o=e.round,l=e.children,r=e.disabled,c=e.simple,i=e.size,u=e.block,m=e.link,d=e.justIcon,p=e.className,f=e.muiClasses,b=Object($.a)(e,de),g=Q()((a={},Object(Y.a)(a,t.button,!0),Object(Y.a)(a,t[i],i),Object(Y.a)(a,t[n],n),Object(Y.a)(a,t.round,o),Object(Y.a)(a,t.disabled,r),Object(Y.a)(a,t.simple,c),Object(Y.a)(a,t.block,u),Object(Y.a)(a,t.link,m),Object(Y.a)(a,t.justIcon,d),Object(Y.a)(a,p,p),a));return s.a.createElement(ue.a,Object.assign({},b,{classes:f,className:g}),l)}var be=t(179),ge=t.n(be),Ee=t(574),he=t(573),ve=t(322),ye=t(232),xe=t(536),ke=t(535),Ce=t(584),Se=t(610),Oe=t(578),je=t(572),we=t(615),_e=t(611),Ne=t(575),Re=t(576),Te=t(571),Ie=t(528),De=t(577),Ae=t(532),Pe=t(612),Fe=t(618),Be=["type","label","value","onChange","required","oneRow","disabled","options","on","off","rows","step","maxStep","minStep","marks","helper"];function Me(e){var a,t=e.type,n=e.label,o=e.value,l=e.onChange,r=e.required,c=e.oneRow,i=e.disabled,u=e.options,m=e.on,d=e.off,p=e.rows,f=e.step,b=e.maxStep,g=e.minStep,E=e.marks,h=e.helper,v=Object($.a)(e,Be);switch(t){case"text":a=s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:n,onChange:l,value:o,margin:"normal",required:r,disabled:i,helperText:h,fullWidth:!0}));break;case"password":a=s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:n,onChange:l,value:o,type:"password",margin:"normal",required:r,disabled:i,fullWidth:!0}));break;case"file":a=s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:n,onChange:l,type:"file",margin:"normal",required:r,disabled:i,fullWidth:!0}));break;case"textarea":p<2&&(p=3),a=s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:n,onChange:l,value:o,margin:"normal",required:r,disabled:i,rowsMax:p,multiline:!0,fullWidth:!0}));break;case"select":a=s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(je.a,null,n),s.a.createElement(Se.a,{value:o,onChange:l,required:r,disabled:i,fullWidth:!0},u.map((function(e,a){return s.a.createElement(Ee.a,{value:e.value,key:a,disabled:e.disabled},e.label)}))));break;case"radio":a=s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(Te.a,{component:"fieldset",fullWidth:!0,disabled:i},s.a.createElement(Ie.a,{component:"legend"},n),s.a.createElement(Ne.a,{name:"type",value:o,onChange:l,row:!0},s.a.createElement(K.a,{display:"flex",alignItems:"center"},u.map((function(e,a){return s.a.createElement(K.a,{key:a},s.a.createElement(Re.a,{value:e.value,control:s.a.createElement(_e.a,null),label:e.label}))}))))));break;case"switch":a=s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(je.a,null,n),s.a.createElement(De.a,{item:!0},d,s.a.createElement(Oe.a,{checked:o,onChange:l,color:"primary"}),m));break;case"checkbox":a=s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(Te.a,{component:"fieldset",fullWidth:!0},s.a.createElement(Ie.a,{component:"legend"},n),s.a.createElement(Ae.a,null,s.a.createElement(De.a,{container:!0},u.map((function(e,a){var t,n=e.value,r=e.label;return t=!!o.has(n)&&o.get(n),s.a.createElement(De.a,{item:!0,xs:6,sm:3,key:a},s.a.createElement(Re.a,{control:s.a.createElement(Pe.a,{checked:t,onChange:l(n),value:n}),label:r}))}))))));break;case"slider":a=s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(Ie.a,{component:"legend"},n),s.a.createElement(Fe.a,{color:"secondary",value:o,max:b,min:g,step:f,valueLabelDisplay:"auto",marks:E,onChange:l}));break;default:return s.a.createElement("div",null)}var y=s.a.createElement(De.a,Object.assign({item:!0},v),a);return c?s.a.createElement(De.a,{item:!0,xs:12},y):y}function ze(e){var a=e.inputs;return s.a.createElement(De.a,{container:!0},a.map((function(e,a){return s.a.createElement(Me,Object.assign({key:a},e))})))}var We=t(579),qe=t(583),He=t(581),Le=t(580),Ue=t(582),Ve=t(533),Ge=["children"],$e=Object(g.a)((function(e){return{backdrop:{zIndex:e.zIndex.drawer+1,color:"#fff"}}}));function Ze(e){var a=$e(),t=e.children,n=Object($.a)(e,Ge);return s.a.createElement(Ve.a,Object.assign({className:a.backdrop},n),t)}var Ye=["children"],Je=Object(g.a)({grid:{padding:"0 15px !important"}});function Qe(e){var a=Je(),t=e.children,n=Object($.a)(e,Ye);return s.a.createElement(De.a,Object.assign({item:!0},n,{className:a.grid}),t)}var Ke=t(534),Xe=t(176),ea=t.n(Xe),aa={root:Object(E.a)(Object(E.a)({},x),{},{flexWrap:"unset",position:"relative",padding:"20px 15px",lineHeight:"20px",marginBottom:"20px",fontSize:"14px",backgroundColor:"#FFF",color:_[7],borderRadius:"3px",minWidth:"unset",maxWidth:"unset",boxShadow:"0 12px 20px -10px rgba("+h("#FFF")+", 0.28), 0 4px 20px 0px rgba("+h("#000")+", 0.12), 0 7px 8px -5px rgba("+h("#FFF")+", 0.2)"}),top20:{top:"20px"},top40:{top:"40px"},info:Object(E.a)({backgroundColor:j[3],color:"#FFF"},T),success:Object(E.a)({backgroundColor:O[3],color:"#FFF"},I),warning:Object(E.a)({backgroundColor:C[3],color:"#FFF"},D),danger:Object(E.a)({backgroundColor:S[3],color:"#FFF"},A),primary:Object(E.a)({backgroundColor:k[3],color:"#FFF"},R),rose:Object(E.a)({backgroundColor:w[3],color:"#FFF"},P),message:{padding:"0",display:"block",maxWidth:"89%"},close:{width:"11px",height:"11px"},iconButton:{width:"24px",height:"24px",padding:"0px"},icon:{display:"block",left:"15px",position:"absolute",top:"50%",marginTop:"-15px",width:"30px",height:"30px"},infoIcon:{color:j[3]},successIcon:{color:O[3]},warningIcon:{color:C[3]},dangerIcon:{color:S[3]},primaryIcon:{color:k[3]},roseIcon:{color:w[3]},iconMessage:{paddingLeft:"50px",display:"block"},actionRTL:{marginLeft:"-8px",marginRight:"auto"}},ta=Object(g.a)(aa);function na(e){var a=ta(),t=e.message,n=e.color,o=e.close,l=e.icon,r=e.rtlActive,c=[],i=Q()(Object(Y.a)({},a.iconMessage,void 0!==l));return void 0!==o&&(c=[s.a.createElement(ne.a,{className:a.iconButton,key:"close","aria-label":"Close",color:"inherit"},s.a.createElement(ea.a,{className:a.close}))]),s.a.createElement(Ke.a,{message:s.a.createElement("div",null,void 0!==l?s.a.createElement(e.icon,{className:a.icon}):null,s.a.createElement("span",{className:i},t)),classes:{root:a.root+" "+a[n],message:a.message,action:Q()(Object(Y.a)({},a.actionRTL,r))},action:c})}function oa(e){var a,t,n=e.open,o=e.size,l=e.operatable,r=e.promptPosition,c=e.prompt,i=e.title,u=e.content,m=e.buttons,d=e.hideBackdrop;a=c?s.a.createElement(Qe,{xs:12},s.a.createElement(na,{message:c,color:"warning"})):s.a.createElement(Qe,{xs:12}),t="top"===r?s.a.createElement(De.a,{container:!0},a,s.a.createElement(Qe,{xs:12},u)):s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},u),a);var p=[];return l&&m.forEach((function(e,a){p.push(s.a.createElement(fe,{onClick:e.onClick,color:e.color,key:a},e.label))})),s.a.createElement(We.a,{open:n,maxWidth:o,fullWidth:!0},s.a.createElement(Le.a,null,i),s.a.createElement(He.a,null,s.a.createElement(Ze,{open:!d&&!l},s.a.createElement(Ue.a,{color:"inherit"})),t),s.a.createElement(qe.a,null,s.a.createElement(K.a,{display:"block",displayPrint:"none"},p)))}var la=t(123),ra=t.n(la);function ca(){var e=localStorage.getItem("nano-session-data");if(!e||0===e.length)return null;var a=JSON.parse(e);return a.id?a:null}function ia(){localStorage.setItem("nano-session-data","")}function sa(){return s.a.createElement(b.a,{to:"/login/?previous="+encodeURIComponent(window.location.pathname+window.location.search)})}function ua(e){var a=function(e,a,t){return 0===e%a?(e/a).toString()+" "+t:(e/a).toFixed(2)+" "+t};return e>=1<<30?a(e,1<<30,"GB"):e>=1<<20?a(e,1<<20,"MB"):e>=1024?a(e,1024,"KB"):e.toString()+" Bytes"}function ma(e){var a=function(e,a,t){return 0===e%a?(e/a).toString()+" "+t:(e/a).toFixed(2)+" "+t};return e>=1<<27?a(e,1<<27,"Gb/s"):e>=1<<17?a(e,1<<17,"Mb/s"):e>=128?a(e,128,"Kb/s"):e.toString()+" Bits/s"}function da(e,a){var t=Math.pow(10,a);return Math.round(e*t)/t}function pa(e){var a={en:{dashboard:"Dashboard",computePool:"Compute Pools",addressPool:"Address Pools",storagePool:"Storage Pools",instance:"Instances",diskImage:"Disk Image",mediaImage:"Media Image",systemTempaltes:"System Templates",securityPolicies:"Security Policies",user:"Users",log:"Logs",visibility:"Resource Visibility"},cn:{dashboard:"\u4eea\u8868\u76d8",computePool:"\u8ba1\u7b97\u8d44\u6e90\u6c60",addressPool:"\u5730\u5740\u8d44\u6e90\u6c60",storagePool:"\u5b58\u50a8\u8d44\u6e90\u6c60",instance:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",diskImage:"\u78c1\u76d8\u955c\u50cf",mediaImage:"\u5149\u76d8\u955c\u50cf",systemTempaltes:"\u7cfb\u7edf\u6a21\u677f",securityPolicies:"\u5b89\u5168\u7b56\u7565\u7ec4",user:"\u7528\u6237",log:"\u65e5\u5fd7",visibility:"\u8d44\u6e90\u53ef\u89c1\u6027"}}[e];return[{value:"dashboard",label:a.dashboard},{value:"compute_pool",label:a.computePool},{value:"address_pool",label:a.addressPool},{value:"storage_pool",label:a.storagePool},{value:"instance",label:a.instance},{value:"image",label:a.diskImage},{value:"media",label:a.mediaImage},{value:"templates",label:a.systemTempaltes},{value:"policies",label:a.securityPolicies},{value:"user",label:a.user},{value:"log",label:a.log},{value:"visibility",label:a.visibility}]}function fa(e,a){Ha("/compute_pools/",e,a)}function ba(e,a,t){Ha("/compute_pool_cells/"+e,a,t)}function ga(e,a,t,n,o){$a("/compute_pool_cells/"+e+"/"+a,{enable:t},(function(){n(e,a,t)}),o)}function Ea(e,a){Ha("/storage_pools/",e,a)}function ha(e,a){Ha("/address_pools/",e,a)}function va(e,a,t){Ha("/address_pools/"+e,a,t)}function ya(e,a,t,n){if(e){var o="/guest_search/?pool="+e;a&&(o+="&cell="+a),Ha(o,t,n)}else n("must specify pool name")}function xa(e,a,t,n){La("/guests/"+e,(function(e,o){if(201===e){var l=o.progress,r=o.name,c=o.created;n(l,r,c)}else 200===e?a(o):t("unexpected status "+e.toString())}),t)}function ka(e,a,t,n){La("/instances/"+e,(function(e,o){if(201===e){var l=o.progress,r=o.name,c=o.created;n(l,r,c)}else 200===e?a(o):t("unexpected status "+e.toString())}),t)}function Ca(e,a,t){Za("/instances/"+e+"/media",(function(){a(e)}),t)}function Sa(e,a,t){Ja("/instances/"+e,{reboot:!1,force:!1},(function(){a(e)}),t)}function Oa(e,a,t){Ja("/instances/"+e,{reboot:!0,force:!1},(function(){a(e)}),t)}function ja(e,a,t){Ja("/instances/"+e,{reboot:!0,force:!0},(function(){a(e)}),t)}function wa(e,a,t){Ha("/guests/"+e+"/auth",(function(t){var n=t.user,o=t.password;a(n,o,e)}),t)}function _a(e,a){Ha("/media_image_search/",e,a)}function Na(e,a,t){Za("/media_images/"+e,(function(){a(e)}),t)}function Ra(e,a){Ha("/disk_image_search/",e,a)}function Ta(e,a,t){Ha("/disk_images/"+e,a,t)}function Ia(e,a,t,n,o,l){var r=ca();if(null!==r){var c={name:e,description:t,tags:n,owner:r.user,group:r.group};a&&(c.guest=a);Ua("/disk_images/",c,(function(e){o(e.id)}),l)}else l("session expired")}function Da(e,a,t){Za("/disk_images/"+e,(function(){a(e)}),t)}function Aa(e,a,t,n){La("/batch/create_guest/"+e,(function(e,o){202===e?a(o):200===e?t(o):n("unexpected status "+e.toString())}),n)}function Pa(e,a,t,n){La("/batch/delete_guest/"+e,(function(e,o){202===e?a(o):200===e?t(o):n("unexpected status "+e.toString())}),n)}function Fa(e,a,t,n){La("/batch/stop_guest/"+e,(function(e,o){202===e?a(o):200===e?t(o):n("unexpected status "+e.toString())}),n)}function Ba(e,a){Ha("/templates/",e,a)}function Ma(e,a,t,n,o,l){var r="/search/security_policy_groups/",c=new URLSearchParams;e&&c.append("owner",e),a&&c.append("group",a),t&&c.append("enabled_only",t),n&&c.append("global_only",n),""!==c.toString()&&(r+="?"+c.toString()),Ha(r,o,l)}function za(e,a){Ha("/roles/",e,a)}function Wa(e,a,t){var n=ca();if(null!==n){var o=n.group+"."+n.user;n.address?o+="("+n.address+") : "+e:o+=": "+e;Ua("/logs/",{content:o},a,t)}else t("session expired")}function qa(e,a){!function(e,a,t){et("get",e,null,a,t)}("/system/",e,a)}function Ha(e,a,t){Ka("get",e,null,a,t)}function La(e,a,t){Xa("get",e,null,a,t)}function Ua(e,a,t,n){Ka("post",e,a,t,n)}function Va(e,a,t,n){Xa("post",e,a,t,n)}function Ga(e,a,t,n){et("post",e,a,t,n)}function $a(e,a,t,n){Ka("put",e,a,t,n)}function Za(e,a,t){Ka("delete",e,null,a,t)}function Ya(e,a,t,n){Ka("patch",e,a,t,n)}function Ja(e,a,t,n){Ka("delete",e,a,t,n)}function Qa(e,a,t,n,o,l){var r=ca();if(null!==r){var c=Object(Y.a)({},"Nano-Session",r.id),i=new FormData;i.append(a,t),ra.a.post("/api/v1"+e,i,{onUploadProgress:function(e){var a=100*e.loaded/e.total;n(a)},headers:c}).then((function(e){var a=e.data;0===a.error_code?o(a.data):l(a.message)})).catch((function(e){l(e.message)}))}else l("session expired")}function Ka(e,a,t,n,o){var l=ca();if(null!==l){var r={method:e,url:"/api/v1"+a,headers:Object(Y.a)({},"Nano-Session",l.id)};t&&(r.data=t),ra()(r).then((function(e){var a=e.data;0===a.error_code?n&&n(a.data):o&&o(a.message)})).catch((function(e){o&&o(e.message)}))}else o&&o("session expired")}function Xa(e,a,t,n,o){var l=ca();if(null!==l){var r={method:e,url:"/api/v1"+a,headers:Object(Y.a)({},"Nano-Session",l.id)};t&&(r.data=t),ra()(r).then((function(e){var a=e.data,t=e.status;0===a.error_code?n&&n(t,a.data):o&&o(a.message)})).catch((function(e){o&&o(e.message)}))}else o&&o("session expired")}function et(e,a,t,n,o){var l={method:e,url:"/api/v1"+a};t&&(l.data=t),ra()(l).then((function(e){var a=e.data;0===a.error_code?n&&n(a.data):o&&o(a.message)})).catch((function(e){o&&o(e.message)}))}var at={en:{title:"Modify Password",current:"Current Password",new:"New Password",confirmNew:"Confirm New Password",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u5bc6\u7801",current:"\u5f53\u524d\u5bc6\u7801",new:"\u65b0\u5bc6\u7801",confirmNew:"\u786e\u8ba4\u65b0\u5bc6\u7801",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function tt(e){var a={old:"",new:"",new2:""},t=e.lang,o=e.user,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!0),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!1),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(""),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(a),C=Object(n.a)(k,2),S=C[0],O=C[1],j=at[t],w=j.title,_=function(){x(""),O(a)},N=function(e){b&&(d(!0),x(e))},R=function(){Wa("change password of "+o),b&&(_(),d(!0),r())},T=function(e){return function(a){var t=a.target.value;O((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}};s.a.useEffect((function(){return g(!0),function(){g(!1)}}),[]);var I=[{type:"password",label:j.current,onChange:T("old"),value:S.old,required:!0,xs:12},{type:"password",label:j.new,onChange:T("new"),value:S.new,required:!0,xs:12},{type:"password",label:j.confirmNew,onChange:T("new2"),value:S.new2,required:!0,xs:12}],D=s.a.createElement(ze,{inputs:I}),A=[{color:"transparent",label:j.cancel,onClick:function(){_(),c()}},{color:"info",label:j.confirm,onClick:function(){d(!1),""!==S.old?""!==S.new?S.new2===S.new?function(e,a,t,n,o){$a("/users/"+e+"/password/",{old:a,new:t},(function(){n(e)}),o)}(o,S.old,S.new,R,N):N("confirm password mismatched"):N("new password required"):N("previous password required")}}];return s.a.createElement(oa,{size:"sm",open:l,prompt:y,title:w,buttons:A,content:D,operatable:m})}var nt=function(e){return{buttonLink:Object(Y.a)({},e.breakpoints.down("md"),{display:"flex",marginLeft:"30px",width:"auto"}),links:Object(Y.a)({width:"20px",height:"20px",zIndex:"4"},e.breakpoints.down("md"),{display:"block",width:"30px",height:"30px",color:_[9],marginRight:"15px"}),linkText:Object(E.a)(Object(E.a)({zIndex:"4"},x),{},{fontSize:"14px"}),popperClose:{pointerEvents:"none"},pooperResponsive:Object(Y.a)({},e.breakpoints.down("md"),{zIndex:"1640",position:"static",float:"none",width:"auto",marginTop:"0",backgroundColor:"transparent",border:"0",WebkitBoxShadow:"none",boxShadow:"none",color:"black"}),popperNav:Object(Y.a)({},e.breakpoints.down("sm"),{position:"static !important",left:"unset !important",top:"unset !important",transform:"none !important",willChange:"unset !important","& > div":{boxShadow:"none !important",marginLeft:"0rem",marginRight:"0rem",transition:"none !important",marginTop:"0px !important",marginBottom:"0px !important",padding:"0px !important",backgroundColor:"transparent !important","& ul li":{color:"#FFF !important",margin:"10px 15px 0!important",padding:"10px 15px !important","&:hover":{backgroundColor:"hsla(0,0%,78%,.2)",boxShadow:"none"}}}}),dropdown:{borderRadius:"3px",border:"0",boxShadow:"0 2px 5px 0 rgba("+h("#000")+", 0.26)",top:"100%",zIndex:"1000",minWidth:"160px",padding:"5px 0",margin:"2px 0 0",fontSize:"14px",textAlign:"left",listStyle:"none",backgroundColor:"#FFF",WebkitBackgroundClip:"padding-box",backgroundClip:"padding-box"},dropdownItem:Object(E.a)(Object(E.a)({},x),{},{fontSize:"13px",padding:"10px 20px",margin:"0 5px",borderRadius:"2px",WebkitTransition:"all 150ms linear",MozTransition:"all 150ms linear",OTransition:"all 150ms linear",MsTransition:"all 150ms linear",transition:"all 150ms linear",display:"block",clear:"both",fontWeight:"400",lineHeight:"1.42857143",color:_[8],whiteSpace:"nowrap",height:"unset",minHeight:"unset","&:hover":Object(E.a)({backgroundColor:k[0],color:"#FFF"},R)})}},ot=function(e){var a,t,n;return Object(E.a)(Object(E.a)({},nt(e)),{},{search:Object(Y.a)({"& > div":{marginTop:"0"}},e.breakpoints.down("sm"),{margin:"10px 15px !important",float:"none !important",paddingTop:"1px",paddingBottom:"1px",padding:"0!important",width:"60%",marginTop:"40px","& input":{color:"#FFF"}}),linkText:Object(E.a)(Object(E.a)({zIndex:"4"},x),{},{fontSize:"14px",margin:"0px"}),buttonLink:Object(Y.a)({},e.breakpoints.down("sm"),{display:"flex",margin:"10px 15px 0",width:"-webkit-fill-available","& svg":{width:"24px",height:"30px",marginRight:"15px",marginLeft:"-15px"},"& .fab,& .fas,& .far,& .fal,& .material-icons":{fontSize:"24px",lineHeight:"30px",width:"24px",height:"30px",marginRight:"15px",marginLeft:"-15px"},"& > span":{justifyContent:"flex-start",width:"100%"}}),searchButton:Object(Y.a)({},e.breakpoints.down("sm"),{top:"-50px !important",marginRight:"22px",float:"right"}),margin:{zIndex:"4",margin:"0"},searchIcon:{width:"17px",zIndex:"4"},notifications:(a={zIndex:"4"},Object(Y.a)(a,e.breakpoints.up("md"),{position:"absolute",top:"2px",border:"1px solid #FFF",right:"4px",fontSize:"9px",background:S[0],color:"#FFF",minWidth:"16px",height:"16px",borderRadius:"10px",textAlign:"center",lineHeight:"16px",verticalAlign:"middle",display:"block"}),Object(Y.a)(a,e.breakpoints.down("sm"),Object(E.a)(Object(E.a)({},x),{},{fontSize:"14px",marginRight:"8px"})),a),manager:(t={},Object(Y.a)(t,e.breakpoints.down("sm"),{width:"100%"}),Object(Y.a)(t,"display","inline-block"),t),searchWrapper:(n={},Object(Y.a)(n,e.breakpoints.down("sm"),{width:"-webkit-fill-available",margin:"10px 15px 0"}),Object(Y.a)(n,"display","inline-block"),n)})},lt=Object(g.a)(ot),rt={en:{modify:"Modify Password",logout:"Logout"},cn:{modify:"\u4fee\u6539\u5bc6\u7801",logout:"\u6ce8\u9500"}};function ct(e){var a=lt(),t=e.lang,o=rt[t],l=s.a.useState(!1),r=Object(n.a)(l,2),c=r[0],i=r[1],u=s.a.useState(""),m=Object(n.a)(u,2),d=m[0],p=m[1],f=s.a.useState(null),b=Object(n.a)(f,2),g=b[0],E=b[1],h=s.a.useState(!1),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState((function(){return null!==ca()})),C=Object(n.a)(k,2),S=C[0],O=C[1],j=function(){E(null)},w=function(){var e=function(){ia(),O(!1)};Wa("logout",e,e)},_=function(){x(!0)},N=function(){x(!1)},R=s.a.useCallback((function(){!function(e){var a=ca();if(null!=a){var t="/api/v1/sessions/"+a.id,n=Object(Y.a)({},"Nano-Session",a.id);ra.a.put(t,"",{headers:n}).then((function(a){var t=a.data;0===t.error_code||e(t.message)})).catch((function(a){e(a.message)}))}else e("session expired")}((function(){ia(),c&&O(!1)}))}),[c]);return s.a.useEffect((function(){i(!0),R();var e=ca();if(null!==e){c&&p(e.user);var a=1e3*e.timeout*2/3,t=setInterval((function(){R()}),a);return function(){i(!1),clearInterval(t)}}}),[c,R]),S?s.a.createElement("div",{className:a.manager},s.a.createElement(fe,{color:window.innerWidth>959?"transparent":"white",simple:!(window.innerWidth>959),"aria-owns":g?"profile-menu-list-grow":null,"aria-haspopup":"true",onClick:function(e){g&&g.contains(e.target)?E(null):E(e.currentTarget)},className:a.buttonLink},d,s.a.createElement(ge.a,{className:a.icons})),s.a.createElement(ke.a,{open:Boolean(g),anchorEl:g,transition:!0,disablePortal:!0,className:Q()(Object(Y.a)({},a.popperClose,!g))+" "+a.popperNav},(function(e){var t=e.TransitionProps,n=e.placement;return s.a.createElement(ve.a,Object.assign({},t,{id:"profile-menu-list-grow",style:{transformOrigin:"bottom"===n?"center top":"center bottom"}}),s.a.createElement(ye.a,null,s.a.createElement(xe.a,{onClickAway:j},s.a.createElement(he.a,{role:"menu"},s.a.createElement(Ee.a,{onClick:_,className:a.dropdownItem},o.modify),s.a.createElement(Ce.a,{light:!0}),s.a.createElement(Ee.a,{onClick:w,className:a.dropdownItem},o.logout)))))})),s.a.createElement(tt,{lang:t,open:y,user:d,onSuccess:N,onCancel:N})):sa()}function it(e){return s.a.createElement("div",null,s.a.createElement(ct,e))}var st=t(80),ut=t(321),mt=t(286),dt=t.n(mt),pt=["lang","setLang"],ft=["buttonClass"],bt=function(e){Object(r.a)(t,e);var a=Object(c.a)(t);function t(e){var n;Object(o.a)(this,t),(n=a.call(this,e)).openMenu=n.openMenu.bind(Object(st.a)(n)),n.closeMenu=n.closeMenu.bind(Object(st.a)(n)),n.languages=[{locale:"cn",name:"\u7b80\u4f53\u4e2d\u6587"},{locale:"en",name:"English"}];var l,r=e.lang,c=e.setLang,i=Object($.a)(e,pt);return n.restProps=i,n.changeLanguage=c,n.languages.forEach((function(e){r===e.locale&&(l=e.name)})),n.state={language:r,anchorEl:null,displayText:l},n}return Object(l.a)(t,[{key:"updateLanguage",value:function(e){var a=this;this.languages.forEach((function(t){e===t.locale&&a.setState({displayText:t.name,anchorEl:null})})),function(e){var a=localStorage.getItem("nano-language-data");if(!a||0===a.length)return!1;var t=JSON.parse(a);!!t.lang&&(t.lang===e||(t.lang=e,localStorage.setItem("nano-language-data",JSON.stringify(t))))}(e),this.changeLanguage(e)}},{key:"openMenu",value:function(e){this.setState({anchorEl:e.currentTarget})}},{key:"closeMenu",value:function(){this.setState({anchorEl:null})}},{key:"render",value:function(){var e=this,a=this.state.language,t=this.restProps,n=t.buttonClass,o=Object($.a)(t,ft);return s.a.createElement(oe.a,{component:"div"},s.a.createElement(fe,Object.assign({},o,{onClick:this.openMenu,color:"transparent",size:"sm"}),s.a.createElement(dt.a,{fontSize:"small",className:n}),this.state.displayText),s.a.createElement(ut.a,{keepMounted:!0,anchorEl:this.state.anchorEl,onClose:this.closeMenu,open:Boolean(this.state.anchorEl)},this.languages.map((function(t){return s.a.createElement(Ee.a,{key:t.locale,selected:t.locale===a,onClick:function(){e.updateLanguage(t.locale)}},s.a.createElement(oe.a,{component:"div",variant:"overline"},t.name))}))))}}]),t}(s.a.Component),gt=function(){return{appBar:{backgroundColor:"transparent",boxShadow:"none",borderBottom:"0",marginBottom:"0",position:"absolute",width:"100%",paddingTop:"10px",zIndex:"1029",color:_[7],border:"0",borderRadius:"3px",padding:"10px 0",transition:"all 150ms ease 0s",minHeight:"50px",display:"block"},container:Object(E.a)(Object(E.a)({},y),{},{minHeight:"50px"}),flex:{flex:1},title:Object(E.a)(Object(E.a)({},x),{},{letterSpacing:"unset",lineHeight:"30px",fontSize:"18px",borderRadius:"3px",textTransform:"none",color:"inherit",margin:"0","&:hover,&:focus":{background:"transparent"}}),appResponsive:{top:"8px"},primary:Object(E.a)({backgroundColor:k[0],color:"#FFF"},H),info:Object(E.a)({backgroundColor:j[0],color:"#FFF"},H),success:Object(E.a)({backgroundColor:O[0],color:"#FFF"},H),warning:Object(E.a)({backgroundColor:C[0],color:"#FFF"},H),danger:Object(E.a)({backgroundColor:S[0],color:"#FFF"},H)}},Et=Object(g.a)(gt),ht={en:{manual:"Online Manual",manualURL:"https://nanocloud.readthedocs.io/projects/guide/en/latest/"},cn:{manual:"\u5728\u7ebf\u6587\u6863",manualURL:"https://nanocloud.readthedocs.io/projects/guide/zh_CN/latest/"}};function vt(e){var a=Et();var t=e.color,n=e.lang,o=e.setLang,l=ht[n],r=Q()(Object(Y.a)({}," "+a[t],t)),c=s.a.createElement(K.a,{mr:2},s.a.createElement(oe.a,{component:"span"},"Project Nano 1.3.1 \xa9 2018~2020")),i=s.a.createElement(te.a,{title:l.manual,placement:"top"},s.a.createElement(ae.a,{target:"_blank",href:l.manualURL},s.a.createElement(ne.a,{color:"default",size:"small"},s.a.createElement(re.a,null))));return s.a.createElement(X.a,{className:a.appBar+r},s.a.createElement(ee.a,{className:a.container},s.a.createElement("div",{className:a.flex},s.a.createElement(fe,{color:"transparent",href:"#",className:a.title},function(){var a="";return e.routes.every((function(t){return-1===window.location.href.indexOf(t.layout+t.path)||(a=t.display[e.lang],!1)})),a}())),s.a.createElement(ce.a,{smDown:!0},c,i),s.a.createElement(bt,{lang:n,setLang:o}),s.a.createElement(ce.a,{smDown:!0,implementation:"css"},s.a.createElement(it,{lang:n})),s.a.createElement(ce.a,{mdUp:!0,implementation:"css"},s.a.createElement(ne.a,{color:"inherit","aria-label":"open drawer",onClick:e.handleDrawerToggle},s.a.createElement(se.a,null)))))}var yt=t(25),xt=t(616),kt=t(530),Ct=t(531),St=t(590),Ot=t(589),jt=function(e){var a,t;return{drawerPaper:Object(E.a)(Object(E.a)({border:"none",position:"fixed",top:"0",bottom:"0",left:"0",zIndex:"1"},N),{},(a={width:260},Object(Y.a)(a,e.breakpoints.up("md"),{width:260,position:"fixed",height:"100%"}),Object(Y.a)(a,e.breakpoints.down("sm"),Object(E.a)(Object(E.a)({width:260},N),{},{position:"fixed",display:"block",top:"0",height:"100vh",right:"0",left:"auto",zIndex:"1032",visibility:"visible",overflowY:"visible",borderTop:"none",textAlign:"left",paddingRight:"0px",paddingLeft:"0",transform:"translate3d(".concat(260,"px, 0, 0)")},v)),a)),drawerPaperRTL:(t={},Object(Y.a)(t,e.breakpoints.up("md"),{left:"auto !important",right:"0 !important"}),Object(Y.a)(t,e.breakpoints.down("sm"),{left:"0  !important",right:"auto !important"}),t),logo:{position:"relative",padding:"15px 15px",zIndex:"4","&:after":{content:'""',position:"absolute",bottom:"0",height:"1px",right:"15px",width:"calc(100% - 30px)",backgroundColor:"rgba("+h(_[6])+", 0.3)"}},logoLink:Object(E.a)(Object(E.a)({},x),{},{textTransform:"uppercase",padding:"5px 0",display:"block",fontSize:"18px",textAlign:"left",fontWeight:"400",lineHeight:"30px",textDecoration:"none",backgroundColor:"transparent","&,&:hover":{color:"#FFF"}}),logoLinkRTL:{textAlign:"right"},logoImage:{width:"30px",display:"inline-block",maxHeight:"30px",marginLeft:"10px",marginRight:"15px"},img:{width:"35px",top:"22px",position:"absolute",verticalAlign:"middle",border:"0"},background:{position:"absolute",zIndex:"1",height:"100%",width:"100%",display:"block",top:"0",left:"0",backgroundSize:"cover",backgroundPosition:"center center","&:after":{position:"absolute",zIndex:"3",width:"100%",height:"100%",content:'""',display:"block",background:"#000",opacity:".8"}},list:{marginTop:"20px",paddingLeft:"0",paddingTop:"0",paddingBottom:"0",marginBottom:"0",listStyle:"none",position:"unset"},item:{position:"relative",display:"block",textDecoration:"none","&:hover,&:focus,&:visited,&":{color:"#FFF"}},itemLink:Object(E.a)({width:"auto",transition:"all 300ms linear",margin:"10px 15px 0",borderRadius:"3px",position:"relative",display:"block",padding:"10px 15px",backgroundColor:"transparent"},x),itemIcon:{width:"24px",height:"30px",fontSize:"24px",lineHeight:"30px",float:"left",marginRight:"15px",textAlign:"center",verticalAlign:"middle",color:"rgba("+h("#FFF")+", 0.8)"},itemIconRTL:{marginRight:"3px",marginLeft:"15px",float:"right"},itemText:Object(E.a)(Object(E.a)({},x),{},{margin:"0",lineHeight:"30px",fontSize:"14px",color:"#FFF"}),itemTextRTL:{textAlign:"right"},whiteFont:{color:"#FFF"},purple:Object(E.a)(Object(E.a)({backgroundColor:k[0]},R),{},{"&:hover,&:focus":Object(E.a)({backgroundColor:k[0]},R)}),blue:{backgroundColor:j[0],boxShadow:"0 12px 20px -10px rgba("+h(j[0])+",.28), 0 4px 20px 0 rgba("+h("#000")+",.12), 0 7px 8px -5px rgba("+h(j[0])+",.2)","&:hover,&:focus":{backgroundColor:j[0],boxShadow:"0 12px 20px -10px rgba("+h(j[0])+",.28), 0 4px 20px 0 rgba("+h("#000")+",.12), 0 7px 8px -5px rgba("+h(j[0])+",.2)"}},green:{backgroundColor:O[0],boxShadow:"0 12px 20px -10px rgba("+h(O[0])+",.28), 0 4px 20px 0 rgba("+h("#000")+",.12), 0 7px 8px -5px rgba("+h(O[0])+",.2)","&:hover,&:focus":{backgroundColor:O[0],boxShadow:"0 12px 20px -10px rgba("+h(O[0])+",.28), 0 4px 20px 0 rgba("+h("#000")+",.12), 0 7px 8px -5px rgba("+h(O[0])+",.2)"}},orange:{backgroundColor:C[0],boxShadow:"0 12px 20px -10px rgba("+h(C[0])+",.28), 0 4px 20px 0 rgba("+h("#000")+",.12), 0 7px 8px -5px rgba("+h(C[0])+",.2)","&:hover,&:focus":{backgroundColor:C[0],boxShadow:"0 12px 20px -10px rgba("+h(C[0])+",.28), 0 4px 20px 0 rgba("+h("#000")+",.12), 0 7px 8px -5px rgba("+h(C[0])+",.2)"}},red:{backgroundColor:S[0],boxShadow:"0 12px 20px -10px rgba("+h(S[0])+",.28), 0 4px 20px 0 rgba("+h("#000")+",.12), 0 7px 8px -5px rgba("+h(S[0])+",.2)","&:hover,&:focus":{backgroundColor:S[0],boxShadow:"0 12px 20px -10px rgba("+h(S[0])+",.28), 0 4px 20px 0 rgba("+h("#000")+",.12), 0 7px 8px -5px rgba("+h(S[0])+",.2)"}},sidebarWrapper:{position:"relative",height:"calc(100vh - 75px)",overflow:"auto",width:"260px",zIndex:"4",overflowScrolling:"touch"},activePro:Object(Y.a)({},e.breakpoints.up("md"),{position:"absolute",width:"100%",bottom:"13px"})}},wt=Object(g.a)(jt);function _t(e){var a=wt();function t(e){return window.location.href.indexOf(e)>-1}var n=e.color,o=e.logo,l=e.image,r=e.logoText,c=e.routes,i=e.lang,u=s.a.createElement(kt.a,{className:a.list},c.map((function(o,l){var r;r=Q()(Object(Y.a)({}," "+a[n],t(o.layout+o.path)));var c=Q()(Object(Y.a)({}," "+a.whiteFont,t(o.layout+o.path)));return s.a.createElement(yt.b,{to:o.layout+o.path,className:" "+a.item,activeClassName:"active",key:l},s.a.createElement(Ct.a,{button:!0,className:a.itemLink+r},"string"===typeof o.icon?s.a.createElement(Ot.a,{className:Q()(a.itemIcon,c,Object(Y.a)({},a.itemIconRTL,e.rtlActive))},o.icon):s.a.createElement(o.icon,{className:Q()(a.itemIcon,c,Object(Y.a)({},a.itemIconRTL,e.rtlActive))}),s.a.createElement(St.a,{primary:o.display[i],className:Q()(a.itemText,c,Object(Y.a)({},a.itemTextRTL,e.rtlActive)),disableTypography:!0})))}))),m=s.a.createElement("div",{className:a.logo},s.a.createElement("a",{href:"https://nanos.cloud/",className:Q()(a.logoLink,Object(Y.a)({},a.logoLinkRTL,e.rtlActive)),target:"_blank"},s.a.createElement("div",{className:a.logoImage},s.a.createElement("img",{src:o,alt:"logo",className:a.img})),r));return s.a.createElement("div",null,s.a.createElement(ce.a,{mdUp:!0,implementation:"css"},s.a.createElement(xt.a,{variant:"temporary",anchor:e.rtlActive?"left":"right",open:e.open,classes:{paper:Q()(a.drawerPaper,Object(Y.a)({},a.drawerPaperRTL,e.rtlActive))},onClose:e.handleDrawerToggle,ModalProps:{keepMounted:!0}},m,s.a.createElement("div",{className:a.sidebarWrapper},s.a.createElement(it,{lang:i}),u),void 0!==l?s.a.createElement("div",{className:a.background,style:{backgroundImage:"url("+l+")"}}):null)),s.a.createElement(ce.a,{smDown:!0,implementation:"css"},s.a.createElement(xt.a,{anchor:e.rtlActive?"right":"left",variant:"permanent",open:!0,classes:{paper:Q()(a.drawerPaper,Object(Y.a)({},a.drawerPaperRTL,e.rtlActive))}},m,s.a.createElement("div",{className:a.sidebarWrapper},u),void 0!==l?s.a.createElement("div",{className:a.background,style:{backgroundImage:"url("+l+")"}}):null)))}var Nt=t(597),Rt=t(29),Tt=t.n(Rt),It=t(613),Dt=t(68),At=t.n(Dt),Pt=t(59),Ft=t.n(Pt),Bt=t(184),Mt=t.n(Bt),zt=t(24),Wt=t.n(zt),qt=t(57),Ht=t.n(qt),Lt=t(186),Ut=t.n(Lt),Vt=t(181),Gt=t.n(Vt),$t=t(182),Zt=t.n($t),Yt=t(185),Jt=t.n(Yt),Qt=["children"],Kt=Object(g.a)({grid:{margin:"0 -15px !important",width:"unset"}});function Xt(e){var a=Kt(),t=e.children,n=Object($.a)(e,Qt);return s.a.createElement(De.a,Object.assign({container:!0},n,{className:a.grid}),t)}var en={card:{border:"0",marginBottom:"30px",marginTop:"30px",borderRadius:"6px",color:"rgba("+h("#000")+", 0.87)",background:"#FFF",width:"100%",boxShadow:"0 1px 4px 0 rgba("+h("#000")+", 0.14)",position:"relative",display:"flex",flexDirection:"column",minWidth:"0",wordWrap:"break-word",fontSize:".875rem"},cardPlain:{background:"transparent",boxShadow:"none"},cardProfile:{marginTop:"30px",textAlign:"center"},cardChart:{"& p":{marginTop:"0px",paddingTop:"0px"}}},an=["className","children","plain","profile","chart"],tn=Object(g.a)(en);function nn(e){var a,t=tn(),n=e.className,o=e.children,l=e.plain,r=e.profile,c=e.chart,i=Object($.a)(e,an),u=Q()((a={},Object(Y.a)(a,t.card,!0),Object(Y.a)(a,t.cardPlain,l),Object(Y.a)(a,t.cardProfile,r),Object(Y.a)(a,t.cardChart,c),Object(Y.a)(a,n,void 0!==n),a));return s.a.createElement("div",Object.assign({className:u},i),o)}var on={cardHeader:{padding:"0.75rem 1.25rem",marginBottom:"0",borderBottom:"none",background:"transparent",zIndex:"3 !important","&$cardHeaderPlain,&$cardHeaderIcon,&$cardHeaderStats,&$warningCardHeader,&$successCardHeader,&$dangerCardHeader,&$infoCardHeader,&$primaryCardHeader,&$roseCardHeader":{margin:"0 15px",padding:"0",position:"relative",color:"#FFF"},"&:first-child":{borderRadius:"calc(.25rem - 1px) calc(.25rem - 1px) 0 0"},"&$warningCardHeader,&$successCardHeader,&$dangerCardHeader,&$infoCardHeader,&$primaryCardHeader,&$roseCardHeader":{"&:not($cardHeaderIcon)":{borderRadius:"3px",marginTop:"-20px",padding:"15px"}},"&$cardHeaderStats svg":{fontSize:"36px",lineHeight:"56px",textAlign:"center",width:"36px",height:"36px",margin:"10px 10px 4px"},"&$cardHeaderStats i,&$cardHeaderStats .material-icons":{fontSize:"36px",lineHeight:"56px",width:"56px",height:"56px",textAlign:"center",overflow:"unset",marginBottom:"1px"},"&$cardHeaderStats$cardHeaderIcon":{textAlign:"right"}},cardHeaderPlain:{marginLeft:"0px !important",marginRight:"0px !important"},cardHeaderStats:{"& $cardHeaderIcon":{textAlign:"right"},"& h1,& h2,& h3,& h4,& h5,& h6":{margin:"0 !important"}},cardHeaderIcon:{"&$warningCardHeader,&$successCardHeader,&$dangerCardHeader,&$infoCardHeader,&$primaryCardHeader,&$roseCardHeader":{background:"transparent",boxShadow:"none"},"& i,& .material-icons":{width:"33px",height:"33px",textAlign:"center",lineHeight:"33px"},"& svg":{width:"24px",height:"24px",textAlign:"center",lineHeight:"33px",margin:"5px 4px 0px"}},warningCardHeader:{color:"#FFF","&:not($cardHeaderIcon)":Object(E.a)({},F)},successCardHeader:{color:"#FFF","&:not($cardHeaderIcon)":Object(E.a)({},B)},dangerCardHeader:{color:"#FFF","&:not($cardHeaderIcon)":Object(E.a)({},M)},infoCardHeader:{color:"#FFF","&:not($cardHeaderIcon)":Object(E.a)({},z)},primaryCardHeader:{color:"#FFF","&:not($cardHeaderIcon)":Object(E.a)({},W)},roseCardHeader:{color:"#FFF","&:not($cardHeaderIcon)":Object(E.a)({},q)}},ln=["className","children","color","plain","stats","icon"],rn=Object(g.a)(on);function cn(e){var a,t=rn(),n=e.className,o=e.children,l=e.color,r=e.plain,c=e.stats,i=e.icon,u=Object($.a)(e,ln),m=Q()((a={},Object(Y.a)(a,t.cardHeader,!0),Object(Y.a)(a,t[l+"CardHeader"],l),Object(Y.a)(a,t.cardHeaderPlain,r),Object(Y.a)(a,t.cardHeaderStats,c),Object(Y.a)(a,t.cardHeaderIcon,i),Object(Y.a)(a,n,void 0!==n),a));return s.a.createElement("div",Object.assign({className:m},u),o)}var sn={cardBody:{padding:"0.9375rem 20px",flex:"1 1 auto",WebkitBoxFlex:"1",position:"relative"},cardBodyPlain:{paddingLeft:"5px",paddingRight:"5px"},cardBodyProfile:{marginTop:"15px"}},un=["className","children","plain","profile"],mn=Object(g.a)(sn);function dn(e){var a,t=mn(),n=e.className,o=e.children,l=e.plain,r=e.profile,c=Object($.a)(e,un),i=Q()((a={},Object(Y.a)(a,t.cardBody,!0),Object(Y.a)(a,t.cardBodyPlain,l),Object(Y.a)(a,t.cardBodyProfile,r),Object(Y.a)(a,n,void 0!==n),a));return s.a.createElement("div",Object.assign({className:i},c),o)}var pn=Object(g.a)(U);function fn(e){var a=pn(),t=e.children;return s.a.createElement("div",{className:a.defaultFontStyle+" "+a.infoText},t)}var bn=t(591),gn=Object(g.a)(aa);function En(e){var a=gn(),t=e.message,n=e.color,o=e.close,l=e.icon,r=e.place,c=e.open,i=e.rtlActive,u=[],m=Q()(Object(Y.a)({},a.iconMessage,void 0!==l));return void 0!==o&&(u=[s.a.createElement(ne.a,{className:a.iconButton,key:"close","aria-label":"Close",color:"inherit",onClick:function(){return e.closeNotification()}},s.a.createElement(ea.a,{className:a.close}))]),s.a.createElement(bn.a,{anchorOrigin:{vertical:-1===r.indexOf("t")?"bottom":"top",horizontal:-1!==r.indexOf("l")?"left":-1!==r.indexOf("c")?"center":"right"},open:c,message:s.a.createElement("div",null,void 0!==l?s.a.createElement(e.icon,{className:a.icon}):null,s.a.createElement("span",{className:m},t)),action:u,ContentProps:{classes:{root:a.root+" "+a[n],message:a.message,action:Q()(Object(Y.a)({},a.actionRTL,i))}}})}var hn=t(592),vn=t(593),yn=t(594),xn=t(596),kn=t(595),Cn=function(e){return{warningTableHeader:{color:C[0]},primaryTableHeader:{color:k[0]},dangerTableHeader:{color:S[0]},successTableHeader:{color:O[0]},infoTableHeader:{color:j[0]},roseTableHeader:{color:w[0]},grayTableHeader:{color:_[0]},table:{marginBottom:"0",width:"100%",maxWidth:"100%",backgroundColor:"transparent",borderSpacing:"0",borderCollapse:"collapse"},tableHeadCell:Object(E.a)(Object(E.a)({color:"inherit"},x),{},{"&, &$tableCell":{fontSize:"1em"}}),tableCell:Object(E.a)(Object(E.a)({},x),{},{lineHeight:"1.42857143",padding:"12px 8px",verticalAlign:"middle",fontSize:"0.8125rem"}),tableResponsive:{width:"100%",marginTop:e.spacing(3),overflowX:"auto"},tableHeadRow:{height:"56px",color:"inherit",display:"table-row",outline:"none",verticalAlign:"middle"},tableBodyRow:{height:"48px",color:"inherit",display:"table-row",outline:"none",verticalAlign:"middle"}}},Sn=Object(g.a)(Cn);function On(e){var a=Sn(),t=e.color,n=e.headers,o=e.rows;return s.a.createElement("div",{className:a.tableResponsive},s.a.createElement(hn.a,{className:a.table},s.a.createElement(vn.a,{className:a[t+"TableHeader"]},s.a.createElement(yn.a,{className:a.tableHeadRow},n.map((function(e,t){return s.a.createElement(kn.a,{className:a.tableCell+" "+a.tableHeadCell,key:t},e)})))),s.a.createElement(xn.a,null,o.map((function(e,t){return s.a.createElement(yn.a,{key:t,className:a.tableBodyRow},e.map((function(e,t){return s.a.createElement(kn.a,{className:a.tableCell,key:t},e)})))})))))}function jn(e){var a,t,n,o=e.label,l=e.icon,r=e.href,c=e.placement,i=e.color,u=e.onClick;return a=c||"top",t=i||"inherit",n=u?s.a.createElement(ne.a,{onClick:u,color:t},s.a.createElement(l)):r?s.a.createElement(yt.a,{to:r},s.a.createElement(ne.a,{color:t},s.a.createElement(l))):s.a.createElement(ne.a,{color:t},s.a.createElement(l)),s.a.createElement(te.a,{title:o,placement:a},n)}On.defaultProps={color:"gray"};var wn={en:{title:"Remove Cell",content:"Are you sure to remove compute cell ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u79fb\u9664\u8d44\u6e90\u8282\u70b9",content:"\u662f\u5426\u79fb\u9664\u8d44\u6e90\u8282\u70b9 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}},_n=function(e){var a=e.lang,t=e.pool,o=e.cell,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!0),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(""),f=Object(n.a)(p,2),b=f[0],g=f[1],E=wn[a],h=E.title,v=E.content+o,y=function(e){d(!0),g(e)},x=function(e,a){d(!0),g(""),r(a)},k=[{color:"transparent",label:E.cancel,onClick:function(){g(""),c()}},{color:"info",label:E.confirm,onClick:function(){d(!1),function(e,a,t,n){Za("/compute_pool_cells/"+e+"/"+a,(function(){t(e,a)}),(function(t){n('remove cell "'+a+'" from pool "'+e+'" fail: '+t)}))}(t,o,x,y)}}];return s.a.createElement(oa,{size:"xs",open:l,prompt:b,title:h,buttons:k,content:v,operatable:m})},Nn={en:{title:"Add Compute Cell To Pool ",name:"Cell Name",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6dfb\u52a0\u8282\u70b9\u5230\u8d44\u6e90\u6c60 ",name:"\u8d44\u6e90\u8282\u70b9\u540d\u79f0",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}},Rn=function(e){var a={cell:""},t=e.lang,o=e.pool,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!1),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!0),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(""),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(a),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState([]),w=Object(n.a)(j,2),_=w[0],N=w[1],R=Nn[t],T=R.title,I=function(e){g(!0),x(e)},D=function(){x(""),O(a),d(!1)},A=function(e,a){g(!0),D(),r(a)};s.a.useEffect((function(){if(l&&!m){!function(e,a){Ha("/compute_pool_cells/",e,a)}((function(e){var a=[];if(e.forEach((function(e){var t={label:e.address?e.name+" ("+e.address+")":e.name,value:e.name};a.push(t)})),0===a.length)return I("no unallocated cells available"),void d(!0);N(a),d(!0)}),I)}}),[m,l]);var P,F,B=[{color:"transparent",label:R.cancel,onClick:function(){D(),c()}}];if(m){var M=[{type:"select",label:R.name,onChange:(F="cell",function(e){var a=e.target.value;O((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},F,a))}))}),value:S.cell,options:_,required:!0,oneRow:!0,xs:8}];P=s.a.createElement(ze,{inputs:M}),B.push({color:"info",label:R.confirm,onClick:function(){g(!1);var e=S.cell;""!==e?function(e,a,t,n){Ua("/compute_pool_cells/"+e+"/"+a,"",(function(){t(e,a)}),n)}(o,e,A,I):I("must select a cell")}})}else P=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:l,prompt:y,title:T,buttons:B,content:P,operatable:b})},Tn=t(183),In=t.n(Tn),Dn=t(289),An=t.n(Dn),Pn={en:{name:"Name",address:"Address",alive:"Alive",status:"Status",enabled:"Enabled",disabled:"Disabled",online:"Online",offline:"Offline",title:"Current Cell Status",cancel:"Cancel",confirm:"Confirm",attached:"Attached",storage:"Backend Storage",localStorage:"Use local storage"},cn:{name:"\u540d\u79f0",address:"\u5730\u5740",alive:"\u6d3b\u52a8",status:"\u72b6\u6001",enabled:"\u5df2\u542f\u7528",disabled:"\u5df2\u7981\u7528",online:"\u5728\u7ebf",offline:"\u79bb\u7ebf",title:"\u5f53\u524d\u8282\u70b9\u72b6\u6001",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a",attached:"\u5df2\u6302\u8f7d",storage:"\u540e\u7aef\u5b58\u50a8",localStorage:"\u4f7f\u7528\u672c\u5730\u5b58\u50a8"}},Fn=function(e){var a,t,o,l,r,c,i=Object(g.a)(Cn)(),u=Object(g.a)(U)(),m=e.lang,d=e.pool,p=e.cell,f=e.open,b=e.onCancel,E=s.a.useState(!1),h=Object(n.a)(E,2),v=h[0],y=h[1],x=s.a.useState(""),k=Object(n.a)(x,2),C=k[0],S=k[1],O=s.a.useState(null),j=Object(n.a)(O,2),w=j[0],_=j[1],N=Pn[m],R=function(e){S(e)};(s.a.useEffect((function(){if(d&&p&&f&&!v){Ha("/compute_pool_cells/"+d+"/"+p,(function(e){_(e),y(!0)}),R)}}),[v,f,d,p]),v)?(w.enabled?(t=N.enabled,o=s.a.createElement(At.a,{className:u.successText})):(t=N.disabled,o=s.a.createElement(Ft.a,{className:u.warningText})),w.alive?(l=N.online,r=s.a.createElement(Gt.a,{className:u.successText})):(l=N.offline,r=s.a.createElement(Zt.a,{className:u.warningText})),a=s.a.createElement("div",{className:i.tableResponsive},s.a.createElement(hn.a,{className:i.table},s.a.createElement(xn.a,null,s.a.createElement(yn.a,{className:i.tableBodyRow},s.a.createElement(kn.a,{className:i.tableCell},N.name),s.a.createElement(kn.a,{className:i.tableCell},w.name)),s.a.createElement(yn.a,{className:i.tableBodyRow},s.a.createElement(kn.a,{className:i.tableCell},N.address),s.a.createElement(kn.a,{className:i.tableCell},w.address?w.address:"")),s.a.createElement(yn.a,{className:i.tableBodyRow},s.a.createElement(kn.a,{className:i.tableCell},N.status),s.a.createElement(kn.a,{className:i.tableCell},s.a.createElement(te.a,{title:t,placement:"top"},o))),s.a.createElement(yn.a,{className:i.tableBodyRow},s.a.createElement(kn.a,{className:i.tableCell},N.alive),s.a.createElement(kn.a,{className:i.tableCell},s.a.createElement(te.a,{title:l,placement:"top"},r))),w.storage?w.storage.map((function(e){var a;return a=e.attached?s.a.createElement(te.a,{title:N.attached,placement:"top"},s.a.createElement(In.a,{className:u.successText})):s.a.createElement(te.a,{title:e.error,placement:"top"},s.a.createElement(An.a,{className:u.warningText})),s.a.createElement(yn.a,{className:i.tableBodyRow},s.a.createElement(kn.a,{className:i.tableCell},e.name),s.a.createElement(kn.a,{className:i.tableCell},a))})):s.a.createElement(yn.a,{className:i.tableBodyRow},s.a.createElement(kn.a,{className:i.tableCell},N.storage),s.a.createElement(kn.a,{className:i.tableCell},N.localStorage)))))):a=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return c=C&&""!==C?s.a.createElement(Qe,{xs:12},s.a.createElement(na,{message:C,color:"danger"})):s.a.createElement(Qe,{xs:12}),s.a.createElement(We.a,{open:f,"aria-labelledby":N.title,maxWidth:"sm",fullWidth:!0},s.a.createElement(Le.a,null,N.title),s.a.createElement(He.a,null,s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},a),c)),s.a.createElement(qe.a,null,s.a.createElement(fe,{onClick:function(){S(""),_(null),y(!1),b()},color:"transparent",autoFocus:!0},N.cancel)))},Bn={en:{title:"Migrate All Instance",sourcePool:"Source Pool",sourceCell:"Source Cell",targetCell:"Target Cell",offline:"Offline",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u8fc1\u79fb\u6240\u6709\u4e91\u4e3b\u673a\u5b9e\u4f8b",sourcePool:"\u6e90\u8d44\u6e90\u6c60",sourceCell:"\u6e90\u8282\u70b9",targetCell:"\u76ee\u6807\u8282\u70b9",offline:"\u79bb\u7ebf",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function Mn(e){var a={targetCell:""},t=e.lang,o=e.open,l=e.sourcePool,r=e.sourceCell,c=e.onSuccess,i=e.onCancel,u=s.a.useState(!1),m=Object(n.a)(u,2),d=m[0],p=m[1],f=s.a.useState(!0),b=Object(n.a)(f,2),g=b[0],h=b[1],v=s.a.useState(""),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState(!1),S=Object(n.a)(C,2),O=S[0],j=S[1],w=s.a.useState(a),_=Object(n.a)(w,2),N=_[0],R=_[1],T=s.a.useState({cells:[]}),I=Object(n.a)(T,2),D=I[0],A=I[1],P=Bn[t],F=P.title,B=s.a.useCallback((function(e){O&&(h(!0),k(e))}),[O]),M=function(){k(""),R(a),p(!1)},z=function(e,a){O&&(h(!0),M(),c(e,a))};s.a.useEffect((function(){if(o){j(!0);return ba(l,(function(e){if(O){var a=[];e.forEach((function(e){var t;e.name!==r&&(t=e.alive?e.name+"("+e.address+")":e.name+"("+P.offline+")",a.push({label:t,value:e.name,disabled:!e.alive}))})),0!==a.length?(A({cells:a}),p(!0)):B("no target cell available")}}),B),function(){j(!1)}}}),[O,o,l,r,B,P.offline]);var W,q,H=[{color:"transparent",label:P.cancel,onClick:function(){M(),i()}}];if(d){var L=[{type:"text",label:P.sourcePool,value:l,disabled:!0,oneRow:!0,xs:12,sm:8},{type:"text",label:P.sourceCell,value:r,disabled:!0,oneRow:!0,xs:12,sm:8},{type:"select",label:P.targetCell,onChange:(q="targetCell",function(e){if(O){var a=e.target.value;R((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},q,a))}))}}),value:N.targetCell,options:D.cells,required:!0,oneRow:!0,xs:12,sm:10}];W=s.a.createElement(ze,{inputs:L}),H.push({color:"info",label:P.confirm,onClick:function(){h(!1);var e=N.targetCell;""!==e?function(e,a,t,n,o){Ua("/migrations/",{source_pool:e,source_cell:a,target_cell:t},(function(){n(a,t)}),o)}(l,r,e,z,B):B("select a target cell")}})}else W=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:o,prompt:x,title:F,buttons:H,content:W,operatable:g})}var zn={en:{title:"Change Storage Path",current:"Current Storage Path",location:"New Storage Location",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u5b58\u50a8\u8def\u5f84",current:"\u5f53\u524d\u5b58\u50a8\u8def\u5f84",location:"\u65b0\u5b58\u50a8\u8def\u5f84",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function Wn(e){var a={current:"",path:""},t=e.lang,o=e.open,l=e.pool,r=e.cell,c=e.onSuccess,i=e.onCancel,u=s.a.useState(!1),m=Object(n.a)(u,2),d=m[0],p=m[1],f=s.a.useState(!0),b=Object(n.a)(f,2),g=b[0],h=b[1],v=s.a.useState(""),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState(!1),S=Object(n.a)(C,2),O=S[0],j=S[1],w=s.a.useState(a),_=Object(n.a)(w,2),N=_[0],R=_[1],T=zn[t],I=T.title,D=s.a.useCallback((function(e){O&&(h(!0),k(e))}),[O]),A=function(){k(""),R(a),p(!1)},P=function(){O&&(h(!0),A(),c(N.path,r,l))};s.a.useEffect((function(){if(o&&l&&r){j(!0);return function(e,a,t,n){Ha("/compute_cell_status/"+e+"/"+a+"/storages/",t,n)}(l,r,(function(e){if(O)if(e.system)if(0!==e.system.length){var a=e.system[0];a?(R({current:a,path:""}),p(!0)):D("no system paths available")}else D("no system paths available");else D("no system paths available")}),D),function(){j(!1)}}}),[O,o,l,r,D]);var F,B,M=[{color:"transparent",label:T.cancel,onClick:function(){A(),i()}}];if(d){var z=[{type:"text",label:T.current,value:N.current,disabled:!0,oneRow:!0,xs:12,sm:8},{type:"text",label:T.location,onChange:(B="path",function(e){if(O){var a=e.target.value;R((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},B,a))}))}}),value:N.path,required:!0,oneRow:!0,xs:12,sm:8}];F=s.a.createElement(ze,{inputs:z}),M.push({color:"info",label:T.confirm,onClick:function(){var e=N.path;""!==e?(k(""),h(!1),function(e,a,t,n,o){$a("/compute_cell_status/"+e+"/"+a+"/storages/",{default:t},(function(){n(a,e)}),o)}(l,r,e,P,D)):D("input a new location")}})}else F=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:o,prompt:x,title:I,buttons:M,content:F,operatable:g})}var qn=Object(E.a)(Object(E.a)({},U),{},{cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),Hn=Object(g.a)(qn),Ln={en:{addButton:"Add Compute Cell",tableTitle:"Compute Cells",name:"Name",address:"Address",alive:"Alive",status:"Status",operates:"Operates",noResource:"No compute cell available",computePools:"Compute Pools",enable:"Enable",disable:"Disable",enabled:"Enabled",disabled:"Disabled",instances:"Instances",detail:"Detail",remove:"Remove",migrate:"Migrate",online:"Online",offline:"Offline",changeStorage:"Change Storage Path"},cn:{addButton:"\u6dfb\u52a0\u8d44\u6e90\u8282\u70b9",tableTitle:"\u8ba1\u7b97\u8d44\u6e90\u8282\u70b9",name:"\u540d\u79f0",address:"\u5730\u5740",alive:"\u6d3b\u52a8",status:"\u72b6\u6001",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u8ba1\u7b97\u8d44\u6e90\u8282\u70b9",computePools:"\u8ba1\u7b97\u8d44\u6e90\u6c60",enable:"\u542f\u7528",disable:"\u7981\u7528",enabled:"\u5df2\u542f\u7528",disabled:"\u5df2\u7981\u7528",instances:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",detail:"\u8be6\u60c5",remove:"\u79fb\u9664",migrate:"\u8fc1\u79fb",online:"\u5728\u7ebf",offline:"\u79bb\u7ebf",changeStorage:"\u4fee\u6539\u5b58\u50a8\u8def\u5f84"}};function Un(e){var a,t=Hn(),o=e.lang,l=Ln[o],r=s.a.useState(!1),c=Object(n.a)(r,2),i=c[0],u=c[1],m=s.a.useState(null),d=Object(n.a)(m,2),p=d[0],f=d[1],g=Object(b.g)(),h=new URLSearchParams(g.search).get("pool"),v=s.a.useState(!1),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState(!1),S=Object(n.a)(C,2),O=S[0],j=S[1],w=s.a.useState(!1),_=Object(n.a)(w,2),N=_[0],R=_[1],T=s.a.useState(!1),I=Object(n.a)(T,2),D=I[0],A=I[1],P=s.a.useState(!1),F=Object(n.a)(P,2),B=F[0],M=F[1],z=s.a.useState(""),W=Object(n.a)(z,2),q=W[0],H=W[1],L=s.a.useState("warning"),U=Object(n.a)(L,2),V=U[0],G=U[1],$=s.a.useState(""),Z=Object(n.a)($,2),Y=Z[0],J=Z[1],Q=function(){J("")},X=s.a.useCallback((function(e){if(i){G("warning"),J(e),setTimeout(Q,3e3)}}),[G,J,i]),ee=s.a.useCallback((function(){if(i){ba(h,f,(function(e){i&&X(e)}))}}),[h,X,i]),ae=function(e){G("info"),J(e),Wa(e),setTimeout(Q,3e3)},ne=function(){R(!1)},le=function(){k(!1)},re=function(){A(!1)},ce=function(){M(!1)};if(s.a.useEffect((function(){u(!0),ee();var e=setInterval((function(){i&&ee()}),5e3);return function(){u(!1),clearInterval(e)}}),[ee,i]),null===p)a=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});else if(0===p.length)a=s.a.createElement(K.a,{display:"flex",justifyContent:"center"},s.a.createElement(fn,null,l.noResource));else{var ie=[];p.forEach((function(e){var a,n,o=[{label:l.instances,icon:Mt.a,href:"/admin/instances/range/?pool="+h+"&cell="+e.name},{onClick:function(a){return t=e.name,j(!0),void H(t);var t},icon:Ht.a,label:l.detail},{onClick:function(a){return t=e.name,M(!0),void H(t);var t},icon:Jt.a,label:l.changeStorage},{onClick:function(a){return t=e.name,R(!0),void H(t);var t},icon:Wt.a,label:l.remove},{onClick:function(a){return t=e.name,A(!0),void H(t);var t},icon:Ut.a,label:l.migrate}],r=e.name,c=e.address,u=e.enabled,m=e.alive;u?(a=s.a.createElement(te.a,{title:l.enabled,placement:"top"},s.a.createElement(At.a,{className:t.successText})),o=[{label:l.disable,icon:Ft.a,onClick:function(){ga(h,r,!1,(function(){i&&ee()}),X)}}].concat(o)):(a=s.a.createElement(te.a,{title:l.disabled,placement:"top"},s.a.createElement(Ft.a,{className:t.warningText})),o=[{label:l.enable,icon:At.a,onClick:function(){ga(h,r,!0,(function(){i&&ee()}),X)}}].concat(o));n=m?s.a.createElement(te.a,{title:l.online,placement:"top"},s.a.createElement(Gt.a,{className:t.successText})):s.a.createElement(te.a,{title:l.offline,placement:"top"},s.a.createElement(Zt.a,{className:t.warningText}));var d=o.map((function(e,a){return s.a.createElement(jn,Object(E.a)(Object(E.a)({},e),{},{key:a}))})),p=[r,c,n,a,d];ie.push(p)})),a=s.a.createElement(On,{color:"primary",headers:[l.name,l.address,l.alive,l.status,l.operates],rows:ie})}return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(It.a,{separator:"\u203a","aria-label":"breadcrumb"},s.a.createElement(yt.a,{to:"/admin/compute_pools/"},l.computePools),s.a.createElement(oe.a,{color:"textPrimary"},h))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12},s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:3},s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){k(!0)}},s.a.createElement(Tt.a,null),l.addButton)))),s.a.createElement(Qe,{xs:12},s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",{className:t.cardTitleWhite},l.tableTitle)),s.a.createElement(dn,null,a))),s.a.createElement(En,{place:"tr",color:V,message:Y,open:""!==Y,closeNotification:Q,close:!0}),s.a.createElement(Rn,{lang:o,open:x,pool:h,onSuccess:function(e){le(),ae("cell "+e+" added to "+h),ee()},onCancel:le}),s.a.createElement(Fn,{lang:o,open:O,pool:h,cell:q,onCancel:function(){j(!1)}}),s.a.createElement(_n,{lang:o,open:N,pool:h,cell:q,onSuccess:function(e){ne(),ae("cell "+e+" removed from "+h),ee()},onCancel:ne}),s.a.createElement(Mn,{lang:o,open:D,sourcePool:h,sourceCell:q,onSuccess:function(){re(),ee()},onCancel:re}),s.a.createElement(Wn,{lang:o,open:B,pool:h,cell:q,onSuccess:function(e,a){ce(),ae("storage path of  "+a+" changed to "+e)},onCancel:ce}))}var Vn=t(118),Gn=t.n(Vn),$n=t(95);function Zn(e){var a=e.series,t=[],n=[],o=[];a.forEach((function(e){t.push(e.label),n.push(e.value),o.push(e.color)}));var l={labels:t,datasets:[{data:n,backgroundColor:o,borderWidth:1,hoverBorderWidth:0}]};return s.a.createElement($n.Pie,{data:l,options:{cutoutPercentage:5,legend:{display:!1},layout:{padding:{left:0,right:0,top:0,bottom:0}}}})}var Yn={successText:{color:O[0]},upArrowCardCategory:{width:"16px",height:"16px"},stats:{color:_[0],display:"inline-flex",fontSize:"12px",lineHeight:"22px","& svg":{top:"4px",width:"16px",height:"16px",position:"relative",marginRight:"3px",marginLeft:"3px"},"& .fab,& .fas,& .far,& .fal,& .material-icons":{top:"4px",fontSize:"16px",position:"relative",marginRight:"3px",marginLeft:"3px"}},cardCategory:{color:_[0],margin:"0",fontSize:"14px",marginTop:"0",paddingTop:"10px",marginBottom:"0"},cardCategoryWhite:{color:"rgba("+h("#FFF")+",.62)",margin:"0",fontSize:"14px",marginTop:"0",marginBottom:"0"},cardTitle:{color:_[2],marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:_[1],fontWeight:"400",lineHeight:"1"}},cardTitleWhite:{color:"#FFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:_[1],fontWeight:"400",lineHeight:"1"}}};function Jn(e){var a=e.title,t=e.series,n=e.displayValue,o=0,l=Object(E.a)({},Yn);t.forEach((function(e,a){o+=e.value;var t="series-"+a.toString();l[t]=Object(E.a)(Object(E.a)({},Yn.cardCategory),{},{color:e.color})})),l.topDivider={borderTop:"1px solid "+_[10]};var r,c=Object(g.a)(l)();return r=n?n(o):o.toString(),s.a.createElement(nn,{chart:!0},s.a.createElement(cn,null,s.a.createElement(oe.a,{className:c.cardCategory},a+": "+r),s.a.createElement(Zn,{series:t})),s.a.createElement(dn,null,s.a.createElement(K.a,{m:0,p:2,className:c.topDivider},s.a.createElement(K.a,{display:"flex"},t.map((function(e,a){var t;return t=n?n(e.value):e.value.toString(),s.a.createElement(K.a,{m:1,key:e.label},s.a.createElement(oe.a,{component:"span",className:c["series-"+a.toString()]},e.label),s.a.createElement(oe.a,{component:"span"},": "+t))}))))))}function Qn(e){var a,t=e.series,n=e.minTickStep,o=e.displayValue,l=e.light,r=e.maxTicks,c=e.maxValue,i=r||10,u=l?"#000":"#FFF",m=t[0].data.length,d=new Array(m).fill(""),p=!!c;a=p?c:n;var f=[];t.forEach((function(e){p||e.data.forEach((function(e){a=Math.max(a,e)})),f.push({data:e.data,label:e.label,pointBackgroundColor:e.color,pointBorderColor:e.color,pointRadius:5,borderColor:u,borderWidth:4,lineTension:0})}));var b,g={labels:d,datasets:f};b=a<=i*n?n:Math.ceil(a/i/n)*n;var E={scales:{xAxes:[{gridLines:{drawBorder:!1,lineWidth:0,zeroLineColor:u,zeroLineWidth:2}}],yAxes:[{gridLines:{borderDash:[2,4],color:u,zeroLineColor:u,zeroLineWidth:2,drawBorder:!1},ticks:{stepSize:b,fontColor:u,suggestedMax:a,suggestedMin:0,callback:function(e){return o?o(e):e.toString()}}}]},legend:{display:!1}};return s.a.createElement($n.Line,{data:g,options:E})}function Kn(e){var a=e.title,t=e.color,n=e.series,o=e.displayValue,l=e.minTickStep,r=e.maxValue,c=Object(E.a)(Object(E.a)({},Yn),{},{topDivider:{borderTop:"1px solid "+_[10]}}),i=[];n.forEach((function(e){var a,t=e.data,n=e.label,l=t[t.length-1];a=o?o(l):l.toString(),i.push(n+": "+a)}));var u=Object(g.a)(c)();return s.a.createElement(nn,{chart:!0},s.a.createElement(cn,{color:t},s.a.createElement(Qn,{series:n,minTickStep:l,displayValue:o,maxValue:r})),s.a.createElement(dn,null,s.a.createElement(oe.a,{variant:"h5",component:"div",className:u.cardTitle},a),s.a.createElement(K.a,{m:0,p:2,className:u.topDivider},s.a.createElement(oe.a,{component:"span",className:u.cardCategory},i.join(" / ")+" / "+r.toString()))))}function Xn(e){var a=e.series,t=e.minTickStep,n=e.displayValue,o=e.light,l=e.maxTicks,r=l||10,c=o?"#000":"#FFF",i=a[0].data.length,u=new Array(i).fill(""),m=t,d=[];a.forEach((function(e){d.push({data:e.data,label:e.label,backgroundColor:e.color,barPercentage:.6,borderColor:c,borderWidth:1,stack:"default"})}));for(var p=0;p<i;p++){for(var f=0,b=0;b<a.length;b++)f+=a[b].data[p];m=Math.max(m,f)}var g,E={labels:u,datasets:d};g=m<=r*t?t:Math.ceil(m/r/t)*t;var h={scales:{xAxes:[{display:!1}],yAxes:[{stacked:!0,gridLines:{borderDash:[2,4],color:c,zeroLineColor:c,zeroLineWidth:2},ticks:{stepSize:g,fontColor:c,suggestedMax:m,suggestedMin:0,callback:function(e){return n?n(e):e.toString()}}}]},legend:{display:!1}};return s.a.createElement($n.Bar,{data:E,options:h})}function eo(e){var a=e.title,t=e.color,n=e.series,o=e.displayValue,l=e.minTickStep,r=Object(E.a)(Object(E.a)({},Yn),{},{topDivider:{borderTop:"1px solid "+_[10]}}),c=[];n.forEach((function(e){var a,t=e.data,n=e.label,l=t[t.length-1];a=o?o(l):l.toString(),c.push(n+": "+a)}));var i=Object(g.a)(r)();return s.a.createElement(nn,{chart:!0},s.a.createElement(cn,{color:t},s.a.createElement(Xn,{series:n,minTickStep:l,displayValue:o})),s.a.createElement(dn,null,s.a.createElement(oe.a,{variant:"h5",component:"div",className:i.cardTitle},a),s.a.createElement(K.a,{m:0,p:2,className:i.topDivider},s.a.createElement(oe.a,{component:"span",className:i.cardCategory},c.join(" / ")))))}function ao(e){var a=e.series,t=e.minTickStep,n=e.displayValue,o=e.light,l=e.maxTicks,r=l||10,c=o?"#000":"#FFF",i=a[0].data.length,u=new Array(i).fill(""),m=t,d=[];a.forEach((function(e){e.data.forEach((function(e){m=Math.max(m,e)})),d.push({data:e.data,label:e.label,backgroundColor:e.color,barPercentage:.6,borderColor:c,borderWidth:1})}));var p,f={labels:u,datasets:d};p=m<=r*t?t:Math.ceil(m/r/t)*t;var b={scales:{xAxes:[{display:!1}],yAxes:[{gridLines:{borderDash:[2,4],color:c,zeroLineColor:c,zeroLineWidth:2},ticks:{stepSize:p,fontColor:c,suggestedMax:m,suggestedMin:0,callback:function(e){return n?n(e):e.toString()}}}]},legend:{display:!1}};return s.a.createElement($n.Bar,{data:f,options:b})}function to(e){var a=e.title,t=e.color,n=e.series,o=e.displayValue,l=e.minTickStep,r=Object(E.a)(Object(E.a)({},Yn),{},{topDivider:{borderTop:"1px solid "+_[10]}}),c=[];n.forEach((function(e){var a,t=e.data,n=e.label,l=t[t.length-1];a=o?o(l):l.toString(),c.push(n+": "+a)}));var i=Object(g.a)(r)();return s.a.createElement(nn,{chart:!0},s.a.createElement(cn,{color:t},s.a.createElement(ao,{series:n,minTickStep:l,displayValue:o})),s.a.createElement(dn,null,s.a.createElement(oe.a,{variant:"h5",component:"div",className:i.cardTitle},a),s.a.createElement(K.a,{m:0,p:2,className:i.topDivider},s.a.createElement(oe.a,{component:"span",className:i.cardCategory},c.join(" / ")))))}var no={en:{allButton:"Show All Compute Pools",pools:"Compute Pools Summary",cells:"Compute Cells Summary",instances:"Instances Summary",disks:"Storage Space Summary",coreUsage:"Core Usage",memoryUsage:"Memory Usage",ioUsage:"IO Usage",enabled:"Enabled",disabled:"Disabled",online:"Online",offline:"Offline",stopped:"Stopped",running:"Running",lost:"Lost",migrate:"Migrating",used:"Used",available:"Available",coresUsed:"Cores Used",network:"Network Usage",diskIO:"Disk IO",receive:"Receive",send:"Send",write:"Write",read:"Read"},cn:{allButton:"\u67e5\u770b\u6240\u6709\u8d44\u6e90\u6c60",pools:"\u8ba1\u7b97\u8d44\u6e90\u6c60\u603b\u6570",cells:"\u8d44\u6e90\u8282\u70b9\u603b\u6570",instances:"\u4e91\u4e3b\u673a\u603b\u6570",disks:"\u603b\u78c1\u76d8\u7a7a\u95f4",coreUsage:"CPU\u7528\u91cf",memoryUsage:"\u5185\u5b58\u7528\u91cf",ioUsage:"IO\u541e\u5410\u91cf",enabled:"\u542f\u7528",disabled:"\u7981\u7528",online:"\u5728\u7ebf",offline:"\u79bb\u7ebf",stopped:"\u505c\u6b62",running:"\u8fd0\u884c",lost:"\u5931\u8054",migrate:"\u8fc1\u79fb\u4e2d",used:"\u5df2\u7528",available:"\u53ef\u7528",coresUsed:"\u6838\u5fc3\u5df2\u5360\u7528",network:"\u7f51\u7edc\u6d41\u91cf",diskIO:"\u78c1\u76d8IO",receive:"\u63a5\u6536",send:"\u53d1\u9001",write:"\u5199\u78c1\u76d8",read:"\u8bfb\u78c1\u76d8"}},oo=j[0],lo=O[0],ro=C[0],co=k[0],io=_[2];function so(e){var a,t=s.a.useState(null),o=Object(n.a)(t,2),l=o[0],r=o[1],c=e.lang,i=no[c];if(s.a.useEffect((function(){var e=!0,a=new Array(6).fill({current:0,max:0}),t=new Array(10).fill({available:0,used:0}),n=new Array(10).fill({receive:0,send:0}),o=new Array(10).fill({write:0,read:0}),l=function(){var e;Ha("/compute_zone_status/",(function(e){a.shift(),a.push({current:da(e.cpu_usage,2),max:e.max_cpu}),t.shift(),t.push({available:da(e.available_memory/(1<<20),2),used:da((e.max_memory-e.available_memory)/(1<<20),2)}),n.shift(),n.push({receive:e.receive_speed,send:e.send_speed}),o.shift(),o.push({write:e.write_speed,read:e.read_speed}),r(Object(E.a)(Object(E.a)({},e),{},{coreRecords:a,memoryRecords:t,networkRecords:n,diskRecords:o}))}),e)};l();var c=setInterval((function(){e&&l()}),2e3);return function(){e=!1,clearInterval(c)}}),[]),null===ca())return sa();if(l){var u=new Date(l.start_time.replace(" ","T")),m=new Date,d=Math.floor((m.getTime()-u.getTime())/1e3),p=Math.floor(d/86400);d-=24*p*3600;var f=Math.floor(d/3600);d-=3600*f;var b,g=Math.floor(d/60);d-=60*g,b="cn"===c?"\u7cfb\u7edf\u542f\u52a8\u65f6\u95f4 "+l.start_time+", \u5df2\u8fd0\u884c "+p+" \u5929 "+f+" \u5c0f\u65f6 "+g+" \u5206 "+d+" \u79d2":"System start at "+l.start_time+", Uptime: "+p+" day, "+f+" hour, "+g+" min, "+d+" secs";var h=s.a.createElement(Qe,{xs:12,key:"uptime"},s.a.createElement(K.a,{align:"center"},s.a.createElement(oe.a,{component:"span"},b))),v=Object(n.a)(l.pools,2),y=v[0],x=v[1],C=[{value:y,label:i.disabled,color:io},{value:x,label:i.enabled,color:oo}],S=s.a.createElement(Qe,{xs:12,sm:4,md:3,key:"pool"},s.a.createElement(Jn,{title:i.pools,series:C})),w=Object(n.a)(l.cells,2),N=w[0],R=w[1],T=[{value:N,label:i.offline,color:io},{value:R,label:i.online,color:lo}],I=s.a.createElement(Qe,{xs:12,sm:4,md:3,key:"cell"},s.a.createElement(Jn,{title:i.cells,series:T})),D=Object(n.a)(l.instances,4),A=D[0],P=D[1],F=D[2],B=D[3],M=[{value:A,label:i.stopped,color:io},{value:P,label:i.running,color:oo},{value:F,label:i.lost,color:ro},{value:B,label:i.migrate,color:co}],z=s.a.createElement(Qe,{xs:12,sm:4,md:3,key:"instance"},s.a.createElement(Jn,{title:i.instances,series:M})),W=da(l.available_disk/(1<<30),2),q=da((l.max_disk-l.available_disk)/(1<<30),2),H=[{value:W,label:i.available,color:oo},{value:q,label:i.used,color:co}],L=s.a.createElement(Qe,{xs:12,sm:4,md:3,key:"disks"},s.a.createElement(Jn,{title:i.disks,series:H,displayValue:function(e){return Number.isInteger(e)?e.toString()+" GB":e.toFixed(2)+" GB"}})),U={label:i.coresUsed,color:"#FFF",data:[]},V=0;l.coreRecords.forEach((function(e){U.data.push(e.current),V=Math.max(V,e.max)}));var G=s.a.createElement(Qe,{xs:12,sm:6,md:4,key:"cores-usage"},s.a.createElement(Kn,{title:i.coreUsage,series:[U],color:"success",minTickStep:1,maxValue:V})),$={label:i.used,color:_[4],data:[]},Z={label:i.available,color:O[1],data:[]};l.memoryRecords.forEach((function(e){$.data.push(e.used),Z.data.push(e.available)}));var Y=s.a.createElement(Qe,{xs:12,sm:6,md:4,key:"memory-usage"},s.a.createElement(eo,{title:i.memoryUsage,series:[$,Z],color:"info",minTickStep:1024,displayValue:function(e){return 0===e?"0":e>=1024?0===e%1024?(e/1024).toString()+" GB":(e/1024).toFixed(2)+" GB":e.toString()+" MB"}})),J={label:i.receive,color:j[3],data:[]},Q={label:i.send,color:k[1],data:[]};l.networkRecords.forEach((function(e){J.data.push(e.receive),Q.data.push(e.send)}));var X=[J,Q],ee=s.a.createElement(Qe,{xs:12,sm:6,md:4,key:"network-usage"},s.a.createElement(to,{title:i.network,series:X,displayValue:ma,minTickStep:1<<20,color:"warning"})),ae={label:i.write,color:O[1],data:[]},te={label:i.read,color:j[3],data:[]};l.diskRecords.forEach((function(e){ae.data.push(e.write),te.data.push(e.read)}));var ne=[ae,te];a=[h,S,I,z,L,G,Y,ee,s.a.createElement(Qe,{xs:12,sm:6,md:4,key:"io-usage"},s.a.createElement(to,{title:i.diskIO,series:ne,displayValue:ma,minTickStep:1<<20,color:"rose"}))]}else a=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:6,sm:4,md:3},s.a.createElement(yt.a,{to:"/admin/dashboard/pools/"},s.a.createElement(fe,{size:"sm",color:"info",round:!0},s.a.createElement(Gn.a,null),i.allButton))))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),a)}function uo(e){var a=e.title,t=e.series,n=e.valueName,o=e.colorName,l=e.labelName,r=e.displayValue,c=e.baseClass,i={};i.title=c?Object(E.a)({},c):{},t.forEach((function(e,a){var t="series-"+a.toString();i[t]=c?Object(E.a)(Object(E.a)({},c),{},{color:e[o]}):{color:e[o]}}));var u=Object(g.a)(i)();return s.a.createElement(K.a,{display:"flex"},s.a.createElement(K.a,{m:1},s.a.createElement(oe.a,{component:"span",className:u.title},a)),t.map((function(e,a){var t,o=e[n],c=e[l],i="series-"+a.toString();return t=r?r(o):o.toString(),s.a.createElement(K.a,{m:1,key:c},s.a.createElement(oe.a,{component:"span",className:u[i]},c),s.a.createElement(oe.a,{component:"span"},": "+t))})))}var mo={en:{viewButton:"View Cell Status",zone:"Zone Status",title:"Compute Pool",pools:"All Compute Pools",cells:"Compute Cells",instances:"Instances",disks:"Storage(GB)",memory:"Memory",ioUsage:"IO Usage",enabled:"Enabled",disabled:"Disabled",online:"Online ",offline:"Offline ",stopped:"Stopped ",running:"Running ",lost:"Lost ",migrate:"Migrating ",used:"Used ",available:"Available ",coresUsed:"Cores Used",throughput:"Throughput",receive:"Receive ",send:"Send ",write:"Write ",read:"Read "},cn:{viewButton:"\u67e5\u770b\u8282\u70b9\u72b6\u6001",zone:"\u5168\u57df\u72b6\u6001",title:"\u8ba1\u7b97\u8d44\u6e90\u6c60",pools:"\u6240\u6709\u8ba1\u7b97\u8d44\u6e90\u6c60",cells:"\u8d44\u6e90\u8282\u70b9",instances:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",disks:"\u78c1\u76d8\u7a7a\u95f4(GB)",memory:"\u5185\u5b58",ioUsage:"IO\u541e\u5410\u91cf",enabled:"\u542f\u7528",disabled:"\u7981\u7528",online:"\u5728\u7ebf",offline:"\u79bb\u7ebf",stopped:"\u505c\u6b62",running:"\u8fd0\u884c",lost:"\u5931\u8054",migrate:"\u8fc1\u79fb\u4e2d",used:"\u5df2\u7528",available:"\u53ef\u7528",coresUsed:"\u6838\u5fc3\u5df2\u5360\u7528",throughput:"\u541e\u5410\u91cf",receive:"\u63a5\u6536",send:"\u53d1\u9001",write:"\u5199\u5165",read:"\u8bfb\u53d6"}},po=j[0],fo=O[0],bo=C[0],go=k[0],Eo=_[2],ho={borderRadius:"3px",marginTop:"-20px",padding:"15px"},vo=Object(E.a)(Object(E.a)(Object(E.a)({},U),Yn),{},{cardWithDivider:{borderTop:"1px solid "+_[10]},coresChart:Object(E.a)(Object(E.a)({},ho),{},{background:O[0]}),memoryChart:Object(E.a)(Object(E.a)({},ho),{},{background:j[0]}),networkChart:Object(E.a)(Object(E.a)({},ho),{},{background:C[0]}),diskChart:Object(E.a)(Object(E.a)({},ho),{},{background:w[0]})}),yo=Object(g.a)(vo),xo=function(e){var a=e.lang,t=e.status,o=e.poolName,l=yo(),r=mo[a],c=r.title+": "+o,i=Object(n.a)(t.cells,2),u=i[0],m=i[1],d=[{value:u,label:r.offline,color:Eo},{value:m,label:r.online,color:fo}],p=s.a.createElement(uo,{key:"cells-labels",title:r.cells,series:d,valueName:"value",colorName:"color",labelName:"label",baseClass:Yn.cardCategory}),f=Object(n.a)(t.instances,4),b=f[0],g=f[1],E=f[2],h=f[3],v=[{value:b,label:r.stopped,color:Eo},{value:g,label:r.running,color:po},{value:E,label:r.lost,color:bo},{value:h,label:r.migrate,color:go}],y=s.a.createElement(uo,{key:"instances-labels",title:r.instances,series:v,valueName:"value",colorName:"color",labelName:"label",baseClass:Yn.cardCategory}),x=da(t.available_disk/(1<<30),2),C=da((t.max_disk-t.available_disk)/(1<<30),2),S=[{value:x,label:r.available,color:po},{value:C,label:r.used,color:go}],w=s.a.createElement(uo,{key:"storage-labels",title:r.disks,series:S,valueName:"value",colorName:"color",labelName:"label",baseClass:Yn.cardCategory}),N={label:r.coresUsed,color:"#FFF",data:[]},R=0;t.coreRecords.forEach((function(e){N.data.push(e.current),R=Math.max(R,e.max)}));var T=s.a.createElement(Qe,{xs:12,sm:6,md:3,key:"cores-usage"},s.a.createElement(K.a,{m:0,p:0,className:l.coresChart,boxShadow:2},s.a.createElement(Qn,{series:[N],minTickStep:1,maxValue:R}))),I={label:r.used+r.memory,color:_[4],data:[]},D={label:r.available+r.memory,color:O[1],data:[]};t.memoryRecords.forEach((function(e){I.data.push(e.used),D.data.push(e.available)}));var A=s.a.createElement(Qe,{xs:12,sm:6,md:3,key:"memory-usage"},s.a.createElement(K.a,{m:0,p:0,className:l.memoryChart,boxShadow:2},s.a.createElement(Xn,{series:[I,D],minTickStep:1024,displayValue:function(e){return 0===e?"0":e>=1024?0===e%1024?(e/1024).toString()+" GB":(e/1024).toFixed(2)+" GB":e.toString()+" MB"}}))),P={label:r.receive+r.throughput,color:j[3],data:[]},F={label:r.send+r.throughput,color:k[1],data:[]};t.networkRecords.forEach((function(e){P.data.push(da(e.receive/(1<<20),2)),F.data.push(da(e.send/(1<<20),2))}));var B=function(e){return e>=1024?0===e%1024?(e/1024).toString()+" GB/s":(e/1024).toFixed(2)+" GB/s":Number.isInteger(e)?e.toString()+" MB/s":e.toFixed(2)+" MB/s"},M=[P,F],z=s.a.createElement(Qe,{xs:12,sm:6,md:3,key:"network-usage"},s.a.createElement(K.a,{m:0,p:0,className:l.networkChart,boxShadow:2},s.a.createElement(ao,{series:M,displayValue:B,minTickStep:1}))),W={label:r.write+r.throughput,color:O[1],data:[]},q={label:r.read+r.throughput,color:j[3],data:[]};t.diskRecords.forEach((function(e){W.data.push(da(e.write/(1<<20),2)),q.data.push(da(e.read/(1<<20),2))}));var H=[W,q],L=[T,A,z,s.a.createElement(Qe,{xs:12,sm:6,md:3,key:"io-usage"},s.a.createElement(K.a,{m:0,p:0,className:l.diskChart,boxShadow:2},s.a.createElement(ao,{series:H,displayValue:B,minTickStep:1})))],U=[p,y,w];return s.a.createElement(nn,{chart:!0},s.a.createElement(cn,null,s.a.createElement(Xt,null,L)),s.a.createElement(dn,null,s.a.createElement(oe.a,{component:"span",className:l.cardTitle},c),U,s.a.createElement(K.a,{m:0,p:2,className:l.cardWithDivider},s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:6,sm:4,md:3},s.a.createElement(yt.a,{to:"/admin/dashboard/pools/"+o},s.a.createElement(fe,{size:"sm",color:"info",round:!0},s.a.createElement(Gn.a,null),r.viewButton)))))))};function ko(e){var a,t=s.a.useState(null),o=Object(n.a)(t,2),l=o[0],r=o[1],c=mo[e.lang];if(s.a.useEffect((function(){var e=!0,a=new Map,t=function(){var e;Ha("/compute_pool_status/",(function(e){e.forEach((function(e){var t,n,o,l,r=e.name;if(a.has(r)){var c=a.get(r);(t=c.coreRecords).shift(),(n=c.memoryRecords).shift(),(o=c.networkRecords).shift(),(l=c.diskRecords).shift()}else t=new Array(4).fill({current:0,max:0}),n=new Array(4).fill({available:0,used:0}),o=new Array(4).fill({receive:0,send:0}),l=new Array(4).fill({write:0,read:0});t.push({current:da(e.cpu_usage,2),max:e.max_cpu}),n.push({available:da(e.available_memory/(1<<20),2),used:da((e.max_memory-e.available_memory)/(1<<20),2)}),o.push({receive:e.receive_speed,send:e.send_speed}),l.push({write:e.write_speed,read:e.read_speed});var i=Object(E.a)(Object(E.a)({},e),{},{coreRecords:t,memoryRecords:n,networkRecords:o,diskRecords:l});a.set(r,i)}));var t=new Map;a.forEach((function(e,a){t.set(a,e)})),r(t)}),e)};t();var n=setInterval((function(){e&&t()}),2e3);return function(){e=!1,clearInterval(n)}}),[]),null===ca())return sa();if(l){var i=[];l.forEach((function(e,a){i.push(a)})),i.sort(),a=[],i.forEach((function(t){var n=l.get(t);a.push(s.a.createElement(Qe,{xs:12,key:t},s.a.createElement(xo,{status:n,lang:e.lang,poolName:t})))}))}else a=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});var u=[s.a.createElement(yt.a,{to:"/admin/dashboard/",key:c.zone},c.zone),s.a.createElement(oe.a,{color:"textPrimary",key:c.pools},c.pools)];return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(It.a,{separator:"\u203a","aria-label":"breadcrumb"},u)),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),a)}var Co=t(291),So=t.n(Co),Oo=t(290),jo=t.n(Oo),wo={en:{viewButton:"View Instances",zone:"Zone Status",title:"Compute Cell",pools:"All Compute Pools",cells:"Compute Cells",instances:"Instances",disks:"Storage(GB)",memory:"Memory",ioUsage:"IO Usage",enabled:"Enabled",disabled:"Disabled",online:"Online",offline:"Offline",stopped:"Stopped ",running:"Running ",lost:"Lost ",migrate:"Migrating ",used:"Used ",available:"Available ",coresUsed:"Cores Used",throughput:"Throughput",receive:"Receive ",send:"Send ",write:"Write ",read:"Read "},cn:{viewButton:"\u67e5\u770b\u627f\u8f7d\u4e91\u4e3b\u673a",zone:"\u5168\u57df\u72b6\u6001",title:"\u8ba1\u7b97\u8d44\u6e90\u8282\u70b9",pools:"\u6240\u6709\u8ba1\u7b97\u8d44\u6e90\u6c60",cells:"\u8d44\u6e90\u8282\u70b9",instances:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",disks:"\u78c1\u76d8\u7a7a\u95f4(GB)",memory:"\u5185\u5b58",ioUsage:"IO\u541e\u5410\u91cf",enabled:"\u542f\u7528",disabled:"\u7981\u7528",online:"\u5728\u7ebf",offline:"\u79bb\u7ebf",stopped:"\u505c\u6b62",running:"\u8fd0\u884c",lost:"\u5931\u8054",migrate:"\u8fc1\u79fb\u4e2d",used:"\u5df2\u7528",available:"\u53ef\u7528",coresUsed:"\u6838\u5fc3\u5df2\u5360\u7528",throughput:"\u541e\u5410\u91cf",receive:"\u63a5\u6536",send:"\u53d1\u9001",write:"\u5199\u5165",read:"\u8bfb\u53d6"}},_o=j[0],No=C[0],Ro=k[0],To=_[2],Io={borderRadius:"3px",marginTop:"-20px",padding:"15px"},Do=Object(E.a)(Object(E.a)(Object(E.a)({},Yn),U),{},{cardWithDivider:{borderTop:"1px solid "+_[10]},coresChart:Object(E.a)(Object(E.a)({},Io),{},{background:O[0]}),memoryChart:Object(E.a)(Object(E.a)({},Io),{},{background:j[0]}),networkChart:Object(E.a)(Object(E.a)({},Io),{},{background:C[0]}),diskChart:Object(E.a)(Object(E.a)({},Io),{},{background:w[0]}),disableChart:Object(E.a)(Object(E.a)({},Io),{},{background:_[5]})}),Ao=Object(g.a)(Do),Po=function(e){var a,t,o,l,r=e.lang,c=e.status,i=e.cellName,u=e.poolName,m=Ao(),d=wo[r];if(a=c.alive?c.enabled?s.a.createElement(oe.a,{component:"span",className:m.cardTitle},s.a.createElement(So.a,{className:m.successText}),d.title+": "+i+" ( "+d.online+" )"):s.a.createElement(oe.a,{component:"span",className:m.cardTitle},s.a.createElement(Ft.a,{className:m.mutedText}),d.title+": "+i+" ( "+d.disabled+" )"):s.a.createElement(oe.a,{component:"span",className:m.cardTitle},s.a.createElement(jo.a,{className:m.mutedText}),d.title+": "+i+" ( "+d.offline+" )"),c.alive){l=[s.a.createElement(Qe,{xs:6,sm:4,md:3,key:"view"},s.a.createElement(yt.a,{to:"/admin/instances/?pool="+u+"&cell="+i},s.a.createElement(fe,{size:"sm",color:"info",round:!0},s.a.createElement(Gn.a,null),d.viewButton)))];var p=Object(n.a)(c.instances,4),f=p[0],b=p[1],g=p[2],E=p[3],h=[{value:f,label:d.stopped,color:To},{value:b,label:d.running,color:_o},{value:g,label:d.lost,color:No},{value:E,label:d.migrate,color:Ro}],v=s.a.createElement(uo,{key:"instances-labels",title:d.instances,series:h,valueName:"value",colorName:"color",labelName:"label",baseClass:Yn.cardCategory}),y=da(c.available_disk/(1<<30),2),x=da((c.max_disk-c.available_disk)/(1<<30),2),C=[{value:y,label:d.available,color:_o},{value:x,label:d.used,color:Ro}],S=s.a.createElement(uo,{key:"storage-labels",title:d.disks,series:C,valueName:"value",colorName:"color",labelName:"label",baseClass:Yn.cardCategory}),w={label:d.coresUsed,color:"#FFF",data:[]},N=0;c.coreRecords.forEach((function(e){w.data.push(e.current),N=Math.max(N,e.max)}));var R=s.a.createElement(Qe,{xs:12,sm:6,md:3,key:"cores-usage"},s.a.createElement(K.a,{m:0,p:0,className:m.coresChart,boxShadow:2},s.a.createElement(Qn,{series:[w],minTickStep:1,maxValue:N}))),T={label:d.used+d.memory,color:_[4],data:[]},I={label:d.available+d.memory,color:O[1],data:[]};c.memoryRecords.forEach((function(e){T.data.push(e.used),I.data.push(e.available)}));var D=s.a.createElement(Qe,{xs:12,sm:6,md:3,key:"memory-usage"},s.a.createElement(K.a,{m:0,p:0,className:m.memoryChart,boxShadow:2},s.a.createElement(Xn,{series:[T,I],minTickStep:1024,displayValue:function(e){return 0===e?"0":e>=1024?0===e%1024?(e/1024).toString()+" GB":(e/1024).toFixed(2)+" GB":e.toString()+" MB"}}))),A={label:d.receive+d.throughput,color:j[3],data:[]},P={label:d.send+d.throughput,color:k[1],data:[]};c.networkRecords.forEach((function(e){A.data.push(da(e.receive/(1<<20),2)),P.data.push(da(e.send/(1<<20),2))}));var F=function(e){return e>=1024?0===e%1024?(e/1024).toString()+" GB/s":(e/1024).toFixed(2)+" GB/s":Number.isInteger(e)?e.toString()+" MB/s":e.toFixed(2)+" MB/s"},B=[A,P],M=s.a.createElement(Qe,{xs:12,sm:6,md:3,key:"network-usage"},s.a.createElement(K.a,{m:0,p:0,className:m.networkChart,boxShadow:2},s.a.createElement(ao,{series:B,displayValue:F,minTickStep:1}))),z={label:d.write+d.throughput,color:O[1],data:[]},W={label:d.read+d.throughput,color:j[3],data:[]};c.diskRecords.forEach((function(e){z.data.push(da(e.write/(1<<20),2)),W.data.push(da(e.read/(1<<20),2))}));var q=[z,W];t=[R,D,M,s.a.createElement(Qe,{xs:12,sm:6,md:3,key:"io-usage"},s.a.createElement(K.a,{m:0,p:0,className:m.diskChart,boxShadow:2},s.a.createElement(ao,{series:q,displayValue:F,minTickStep:1})))],o=[v,S]}else t=["core-usage","memory-usage","disk-io","network-io"].map((function(e){return s.a.createElement(Qe,{xs:12,sm:6,md:3,key:e},s.a.createElement(K.a,{m:0,p:0,className:m.disableChart,boxShadow:2}))})),o=[],l=[];return s.a.createElement(nn,{chart:!0},s.a.createElement(cn,null,s.a.createElement(Xt,null,t)),s.a.createElement(dn,null,a,o,s.a.createElement(K.a,{m:0,p:2,className:m.cardWithDivider},s.a.createElement(Xt,null,l))))};function Fo(e){var a,t=e.match.params.pool,o=s.a.useState(null),l=Object(n.a)(o,2),r=l[0],c=l[1],i=wo[e.lang];if(s.a.useEffect((function(){var e=!0,a=new Map,n=function(){!function(e,a,t){Ha("/compute_cell_status/"+e,a,t)}(t,(function(e){e.forEach((function(e){var t,n,o,l,r=e.name;if(a.has(r)){var c=a.get(r);(t=c.coreRecords).shift(),(n=c.memoryRecords).shift(),(o=c.networkRecords).shift(),(l=c.diskRecords).shift()}else t=new Array(4).fill({current:0,max:0}),n=new Array(4).fill({available:0,used:0}),o=new Array(4).fill({receive:0,send:0}),l=new Array(4).fill({write:0,read:0});t.push({current:da(e.cpu_usage,2),max:e.max_cpu}),n.push({available:da(e.available_memory/(1<<20),2),used:da((e.max_memory-e.available_memory)/(1<<20),2)}),o.push({receive:e.receive_speed,send:e.send_speed}),l.push({write:e.write_speed,read:e.read_speed});var i=Object(E.a)(Object(E.a)({},e),{},{coreRecords:t,memoryRecords:n,networkRecords:o,diskRecords:l});a.set(r,i)}));var t=new Map;a.forEach((function(e,a){t.set(a,e)})),c(t)}))};n();var o=setInterval((function(){e&&n()}),2e3);return function(){e=!1,clearInterval(o)}}),[t]),null===ca())return sa();if(r){var u=[];r.forEach((function(e,a){u.push(a)})),u.sort(),a=[],u.forEach((function(n){var o=r.get(n);a.push(s.a.createElement(Qe,{xs:12,key:n},s.a.createElement(Po,{status:o,lang:e.lang,cellName:n,poolName:t})))}))}else a=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});var m=[s.a.createElement(yt.a,{to:"/admin/dashboard/",key:i.zone},i.zone),s.a.createElement(yt.a,{to:"/admin/dashboard/pools/",key:i.pools},i.pools),s.a.createElement(oe.a,{color:"textPrimary",key:t},t)];return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(It.a,{separator:"\u203a","aria-label":"breadcrumb"},m)),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),a)}var Bo=t(292),Mo=t.n(Bo),zo={en:{title:"Delete Pool",content:"Are you sure to delete compute pool ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u8d44\u6e90\u6c60",content:"\u662f\u5426\u5220\u9664\u8ba1\u7b97\u8d44\u6e90\u6c60 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}},Wo=function(e){var a=e.lang,t=e.pool,o=e.open,l=e.onSuccess,r=e.onCancel,c=s.a.useState(!0),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(""),p=Object(n.a)(d,2),f=p[0],b=p[1],g=zo[a],E=g.title,h=g.content+t,v=function(e){m(!0),b(e)},y=function(e){m(!0),b(""),l(e)},x=[{color:"transparent",label:g.cancel,onClick:function(){b(""),r()}},{color:"info",label:g.confirm,onClick:function(){m(!1),function(e,a,t){Za("/compute_pools/"+e,(function(){a(e)}),(function(a){t('delete compute pool "'+e+'" fail: '+a)}))}(t,y,v)}}];return s.a.createElement(oa,{size:"xs",open:o,prompt:f,title:E,buttons:x,content:h,operatable:u})},qo={en:{title:"Create Pool",localStorage:"Use local storage",noAddressPool:"Don't use address pool",name:"Pool Name",storage:"Backend Storage",network:"Address Pool",failover:"Failover",off:"Off",on:"On",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u521b\u5efa\u8d44\u6e90\u6c60",localStorage:"\u4f7f\u7528\u672c\u5730\u5b58\u50a8",noAddressPool:"\u4e0d\u4f7f\u7528\u5730\u5740\u6c60",name:"\u8d44\u6e90\u6c60\u540d\u79f0",storage:"\u540e\u7aef\u5b58\u50a8",network:"\u5730\u5740\u6c60",failover:"\u6545\u969c\u5207\u6362",off:"\u5173\u95ed",on:"\u5f00\u542f",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}},Ho=function(e){var a={name:"",storage:"__default",network:"__default",failover:!1},t=e.lang,o=e.open,l=e.onSuccess,r=e.onCancel,c=s.a.useState(!0),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(""),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(!1),h=Object(n.a)(g,2),v=h[0],y=h[1],x=s.a.useState(a),k=Object(n.a)(x,2),C=k[0],S=k[1],O=s.a.useState({storage:[],network:[]}),j=Object(n.a)(O,2),w=j[0],_=j[1],N=qo[t],R=N.title,T=function(e){m(!0),b(e)},I=function(){b(""),S(a)},D=function(e){m(!0),I(),l(e)},A=function(e){return function(a){var t=a.target.value;S((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}};s.a.useEffect((function(){if(o&&!v){var e=[{label:N.localStorage,value:"__default"}],a=[{label:N.noAddressPool,value:"__default"}],t=function(t){t.forEach((function(e){var t={label:e.name+" ("+e.allocated+"/"+e.addresses+" allocated via gateway "+e.gateway+")",value:e.name};a.push(t)})),_({storage:e,network:a}),y(!0)};Ea((function(a){a.forEach((function(a){var t={label:a.name+" ("+a.type+":"+a.host+")",value:a.name};e.push(t)})),ha(t,T)}),T)}}),[v,o,N.localStorage,N.noAddressPool]);var P,F,B=[{color:"transparent",label:N.cancel,onClick:function(){I(),r()}}];if(v){var M=[{type:"text",label:N.name,onChange:A("name"),value:C.name,required:!0,oneRow:!0,xs:6},{type:"select",label:N.storage,onChange:A("storage"),value:C.storage,options:w.storage,required:!0,oneRow:!0,xs:8},{type:"select",label:N.network,onChange:A("network"),value:C.network,options:w.network,required:!0,oneRow:!0,xs:10},{type:"switch",label:N.failover,onChange:(F="failover",function(e){var a=e.target.checked;S((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},F,a))}))}),value:C.failover,on:N.on,off:N.off,oneRow:!0,xs:6}];P=s.a.createElement(ze,{inputs:M}),B.push({color:"info",label:N.confirm,onClick:function(){m(!1);var e=C.name;""!==e?function(e,a,t,n,o,l){Ua("/compute_pools/"+e,{storage:a,network:t,failover:n},(function(){o(e)}),l)}(e,"__default"===C.storage?"":C.storage,"__default"===C.network?"":C.network,C.failover,D,T):T("must specify pool name")}})}else P=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:o,prompt:f,title:R,buttons:B,content:P,operatable:u})},Lo={en:{title:"Modify Pool",localStorage:"Use local storage",noAddressPool:"Don't use address pool",storage:"Backend Storage",network:"Address Pool",failover:"Failover",off:"Off",on:"On",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u8d44\u6e90\u6c60",localStorage:"\u4f7f\u7528\u672c\u5730\u5b58\u50a8",noAddressPool:"\u4e0d\u4f7f\u7528\u5730\u5740\u6c60",storage:"\u540e\u7aef\u5b58\u50a8",network:"\u5730\u5740\u6c60",failover:"\u6545\u969c\u5207\u6362",off:"\u5173\u95ed",on:"\u5f00\u542f",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}},Uo=function(e){var a={storage:"__default",network:"__default",failover:!1},t=e.lang,o=e.pool,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!1),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!0),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(""),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(!1),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState(a),w=Object(n.a)(j,2),_=w[0],N=w[1],R=s.a.useState({storage:[],network:[]}),T=Object(n.a)(R,2),I=T[0],D=T[1],A=Lo[t],P=A.title+" "+o,F=s.a.useCallback((function(e){S&&(g(!0),x(e))}),[S]),B=function(){x(""),N(a),d(!1)},M=function(e){S&&(g(!0),B(),r(e))},z=function(e){return function(a){var t=a.target.value;N((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}};s.a.useEffect((function(){if(o&&l){O(!0);var e=[{label:A.localStorage,value:"__default"}],a=[{label:A.noAddressPool,value:"__default"}],t=function(t){S&&(D({storage:e,network:a}),N({storage:t.storage?t.storage:"__default",network:t.network?t.network:"__default",failover:t.failover}),d(!0))},n=function(e){S&&(e.forEach((function(e){var t={label:e.name+" ("+e.allocated+"/"+e.addresses+" allocated via gateway "+e.gateway+")",value:e.name};a.push(t)})),function(e,a,t){Ha("/compute_pools/"+e,a,t)}(o,t,F))};return Ea((function(a){S&&(a.forEach((function(a){var t={label:a.name+" ("+a.type+":"+a.host+")",value:a.name};e.push(t)})),ha(n,F))}),F),function(){O(!1)}}}),[S,l,o,A.localStorage,A.noAddressPool,F]);var W,q,H=[{color:"transparent",label:A.cancel,onClick:function(){B(),c()}}];if(m){var L=[{type:"select",label:A.storage,onChange:z("storage"),value:_.storage,options:I.storage,required:!0,oneRow:!0,xs:8},{type:"select",label:A.network,onChange:z("network"),value:_.network,options:I.network,required:!0,oneRow:!0,xs:10},{type:"switch",label:A.failover,onChange:(q="failover",function(e){var a=e.target.checked;N((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},q,a))}))}),value:_.failover,on:A.on,off:A.off,oneRow:!0,xs:6}];W=s.a.createElement(ze,{inputs:L}),H.push({color:"info",label:A.confirm,onClick:function(){var e,a;x(""),g(!1),e="__default"===_.storage?"":_.storage,a="__default"===_.network?"":_.network,function(e,a,t,n,o,l){$a("/compute_pools/"+e,{storage:a,network:t,failover:n},(function(){o(e)}),l)}(o,e,a,_.failover,M,F)}})}else W=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:l,prompt:y,title:P,buttons:H,content:W,operatable:b})},Vo=Object(E.a)(Object(E.a)({},U),{},{cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),Go=Object(g.a)(Vo),$o={en:{createButton:"Create Compute Pool",tableTitle:"Compute Pools",name:"Name",cells:"Cells",storage:"Backend Storage",network:"Address Pool",failover:"FailOver",status:"Status",operates:"Operates",noPools:"No compute pool available",computePools:"Compute Pools",enable:"Enable",disable:"Disable",enabled:"Enabled",disabled:"Disabled",instances:"Instances",modify:"Modify",delete:"Delete",on:"on",off:"off",localStorage:"Use local storage",noAddressPool:"Don't use address pool"},cn:{createButton:"\u521b\u5efa\u8d44\u6e90\u6c60",tableTitle:"\u8ba1\u7b97\u8d44\u6e90\u6c60",name:"\u540d\u79f0",cells:"\u8d44\u6e90\u8282\u70b9",storage:"\u540e\u7aef\u5b58\u50a8",network:"\u5730\u5740\u6c60",failover:"\u6545\u969c\u5207\u6362",status:"\u72b6\u6001",operates:"\u64cd\u4f5c",noPools:"\u6ca1\u6709\u8ba1\u7b97\u8d44\u6e90\u6c60",computePools:"\u8ba1\u7b97\u8d44\u6e90\u6c60",enable:"\u542f\u7528",disable:"\u7981\u7528",enabled:"\u5df2\u542f\u7528",disabled:"\u5df2\u7981\u7528",instances:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",modify:"\u4fee\u6539",delete:"\u5220\u9664",on:"\u5f00\u542f",off:"\u5173\u95ed",localStorage:"\u4f7f\u7528\u672c\u5730\u5b58\u50a8",noAddressPool:"\u4e0d\u4f7f\u7528\u5730\u5740\u6c60"}};var Zo=t(43),Yo=t.n(Zo),Jo=t(79),Qo=t.n(Jo),Ko={en:{title:"Delete Address Pool",content:"Are you sure to delete address pool ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u5730\u5740\u8d44\u6e90\u6c60",content:"\u662f\u5426\u5220\u9664\u5730\u5740\u8d44\u6e90\u6c60 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Xo(e){var a=e.lang,t=e.pool,o=e.open,l=e.onSuccess,r=e.onCancel,c=s.a.useState(!0),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(""),p=Object(n.a)(d,2),f=p[0],b=p[1],g=Ko[a],E=g.title,h=function(e){m(!0),b(e)},v=function(e){m(!0),b(""),l(e)},y=g.content+t,x=[{color:"transparent",label:g.cancel,onClick:function(){b(""),r()}},{color:"info",label:g.confirm,onClick:function(){m(!1),function(e,a,t){Za("/address_pools/"+e,(function(){a(e)}),t)}(t,v,h)}}];return s.a.createElement(oa,{size:"xs",open:o,prompt:f,title:E,buttons:x,content:y,operatable:u})}var el={en:{title:"Create Network Pool",name:"Name",provider:"Provider",interface:"Interface Mode",internal:"Internal",external:"External",both:"Both",gateway:"Gateway",dns1:"DNS1",dns2:"DNS2",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u521b\u5efa\u5730\u5740\u8d44\u6e90\u6c60",name:"\u540d\u79f0",provider:"\u5206\u914d\u6a21\u5f0f",interface:"\u63a5\u53e3\u7c7b\u578b",internal:"\u5185\u90e8",external:"\u5916\u90e8",both:"\u5185\u5916\u90e8",gateway:"\u7f51\u5173\u5730\u5740",dns1:"\u4e3bDNS",dns2:"\u526fDNS",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function al(e){var a={name:"",gateway:"",provider:"dhcp",mode:"internal",dns1:"",dns2:""},t=e.lang,o=e.open,l=e.onSuccess,r=e.onCancel,c=s.a.useState(!0),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(""),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(a),h=Object(n.a)(g,2),v=h[0],y=h[1],x=el[t],k=x.title,C=[{label:x.internal,value:"internal"},{label:x.external,value:"external"},{label:x.both,value:"both"}],S=function(e){m(!0),b(e)},O=function(){b(""),y(a)},j=function(e){m(!0),O(),l(e)},w=function(e){return function(a){var t=a.target.value;y((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}},_=[{type:"text",label:x.name,onChange:w("name"),value:v.name,required:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"radio",label:x.provider,onChange:w("provider"),value:v.provider,oneRow:!0,disabled:!0,options:[{label:"DHCP",value:"dhcp"},{label:"Cloud-Init",value:"cloudinit"}],xs:12,sm:8,md:6},{type:"text",label:x.gateway,onChange:w("gateway"),value:v.gateway,required:!0,oneRow:!0,xs:12,sm:10,md:8},{type:"text",label:x.dns1,onChange:w("dns1"),value:v.dns1,required:!0,oneRow:!0,xs:12,sm:10,md:8},{type:"text",label:x.dns2,onChange:w("dns2"),value:v.dns2,oneRow:!0,xs:12,sm:10,md:8},{type:"radio",label:x.interface,onChange:w("mode"),value:v.mode,oneRow:!0,disabled:!0,options:C,xs:12,sm:8,md:6}],N=s.a.createElement(ze,{inputs:_}),R=[{color:"transparent",label:x.cancel,onClick:function(){O(),r()}},{color:"info",label:x.confirm,onClick:function(){m(!1);var e=new RegExp("^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?).){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");if(v.name)if(v.gateway)if(e.test(v.gateway))if(v.dns1)if(e.test(v.dns1)){var a=[v.dns1];if(v.dns2){if(!e.test(v.dns2))return void S("invalid secondary DNS format");a.push(v.dns2)}!function(e,a,t,n,o,l){Ua("/address_pools/"+e,{gateway:a,provider:t,dns:n},(function(){o(e)}),l)}(v.name,v.gateway,v.provider,a,j,S)}else S("invalid primary DNS format");else S("must specify primary DNS");else S("invalid gateway format");else S("must specify gateway");else S("must specify pool name")}}];return s.a.createElement(oa,{size:"sm",open:o,prompt:f,title:k,buttons:R,content:N,operatable:u})}var tl={en:{title:"Modify Address Pool",name:"Name",gateway:"Gateway",provider:"Provider",interface:"Interface Mode",internal:"Internal",external:"External",both:"Both",dns1:"DNS1",dns2:"DNS2",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u5730\u5740\u8d44\u6e90\u6c60",name:"\u540d\u79f0",gateway:"\u7f51\u5173\u5730\u5740",provider:"\u5206\u914d\u6a21\u5f0f",interface:"\u63a5\u53e3\u7c7b\u578b",internal:"\u5185\u90e8",external:"\u5916\u90e8",both:"\u5185\u5916\u90e8",dns1:"\u4e3bDNS",dns2:"\u526fDNS",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function nl(e){var a={name:"",gateway:"",provider:"dhcp",mode:"internal",dns1:"",dns2:""},t=e.lang,o=e.pool,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!1),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!0),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(""),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(!1),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState(a),w=Object(n.a)(j,2),_=w[0],N=w[1],R=tl[t],T=R.title+" "+o,I=[{label:R.internal,value:"internal"},{label:R.external,value:"external"},{label:R.both,value:"both"}],D=s.a.useCallback((function(e){S&&(g(!0),x(e))}),[S]),A=function(){x(""),N(a),d(!1)},P=function(e){g(!0),A(),r(e)},F=function(e){return function(a){var t=a.target.value;N((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}};s.a.useEffect((function(){if(o&&l){O(!0);return va(o,(function(e){var a,t;if(S)if(0!==e.dns.length){1===e.dns.length?(a=e.dns[0],t=""):2===e.dns.length&&(a=e.dns[0],t=e.dns[1]);var n="dhcp";e.provider&&(n=e.provider);var o="internal";e.mode&&(o=e.mode),N({name:e.name,gateway:e.gateway,provider:n,mode:o,dns1:a,dns2:t}),d(!0)}else D("no DNS available for pool "+e)}),D),function(){O(!1)}}}),[S,l,o,D]);var B,M=[{color:"transparent",label:R.cancel,onClick:function(){A(),c()}}];if(m){var z=[{type:"text",label:R.name,value:o,disabled:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"radio",label:R.provider,onChange:F("provider"),value:_.provider,oneRow:!0,disabled:!0,options:[{label:"DHCP",value:"dhcp"},{label:"Cloud-Init",value:"cloudinit"}],xs:12,sm:8,md:6},{type:"text",label:R.gateway,onChange:F("gateway"),value:_.gateway,required:!0,oneRow:!0,xs:12,sm:10,md:8},{type:"text",label:R.dns1,onChange:F("dns1"),value:_.dns1,required:!0,oneRow:!0,xs:12,sm:10,md:8},{type:"text",label:R.dns2,onChange:F("dns2"),value:_.dns2,oneRow:!0,xs:12,sm:10,md:8},{type:"radio",label:R.interface,onChange:F("mode"),value:_.mode,oneRow:!0,disabled:!0,options:I,xs:12,sm:8,md:6}];B=s.a.createElement(ze,{inputs:z}),M.push({color:"info",label:R.confirm,onClick:function(){g(!1);var e=new RegExp("^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?).){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");if(_.gateway)if(e.test(_.gateway))if(_.dns1)if(e.test(_.dns1)){var a=[_.dns1];if(_.dns2){if(!e.test(_.dns2))return void D("invalid secondary DNS format");a.push(_.dns2)}!function(e,a,t,n,o,l){$a("/address_pools/"+e,{gateway:a,provider:t,dns:n},(function(){o(e)}),l)}(o,_.gateway,_.provider,a,P,D)}else D("invalid primary DNS format");else D("must specify primary DNS");else D("invalid gateway format");else D("must specify gateway")}})}else B=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:l,prompt:y,title:T,buttons:M,content:B,operatable:b})}var ol=Object(g.a)({cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),ll={en:{createButton:"Create Address Pool",tableTitle:"Address Pools",name:"Name",gateway:"Gateway",address:"Total Address",allocated:"Allocated Address",provider:"Provider",operates:"Operates",noResource:"No address pool available",modify:"Modify",delete:"Delete",detail:"Detail"},cn:{createButton:"\u521b\u5efa\u5730\u5740\u6c60",tableTitle:"\u5730\u5740\u8d44\u6e90\u6c60",name:"\u540d\u79f0",gateway:"\u7f51\u5173",address:"\u5730\u5740\u6570\u91cf",allocated:"\u5df2\u5206\u914d\u5730\u5740",provider:"\u5206\u914d\u6a21\u5f0f",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u5730\u5740\u6c60",modify:"\u4fee\u6539",delete:"\u5220\u9664",detail:"\u8be6\u60c5"}};function rl(e){var a=ol(),t=s.a.useState(!1),o=Object(n.a)(t,2),l=o[0],r=o[1],c=s.a.useState(null),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(!1),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(!1),E=Object(n.a)(g,2),h=E[0],v=E[1],y=s.a.useState(!1),x=Object(n.a)(y,2),k=x[0],C=x[1],S=s.a.useState(""),O=Object(n.a)(S,2),j=O[0],w=O[1],_=s.a.useState("warning"),N=Object(n.a)(_,2),R=N[0],T=N[1],I=s.a.useState(""),D=Object(n.a)(I,2),A=D[0],P=D[1],F=function(){P("")},B=s.a.useCallback((function(e){if(l){T("warning"),P(e),setTimeout(F,3e3)}}),[T,P,l]),M=s.a.useCallback((function(){if(l){ha(m,(function(e){l&&B(e)}))}}),[B,l]),z=function(e){if(l){T("info"),P(e),Wa(e),setTimeout(F,3e3)}},W=function(){v(!1)},q=function(){C(!1)},H=function(){b(!1)};s.a.useEffect((function(){return r(!0),M(),function(){r(!1)}}),[M]);var L,U=e.lang,V=ll[U];if(null===u)L=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});else if(0===u.length)L=s.a.createElement(K.a,{display:"flex",justifyContent:"center"},s.a.createElement(fn,null,V.noResource));else{var G=[];u.forEach((function(e){var a=[{onClick:function(a){return t=e.name,v(!0),void w(t);var t},icon:Yo.a,label:V.modify},{icon:Qo.a,label:V.detail,href:"/admin/address_pools/"+e.name},{onClick:function(a){return t=e.name,C(!0),void w(t);var t},icon:Wt.a,label:V.delete}];G.push(function(e,a){var t=a.map((function(e,a){return s.a.createElement(jn,{label:e.label,icon:e.icon,onClick:e.onClick,href:e.href,key:a})})),n=e.name,o=e.gateway,l=e.provider,r=e.addresses,c=e.allocated,i="DHCP";return"cloudinit"===l&&(i="Cloud-Init"),[n,i,o,r.toString(),c.toString(),t]}(e,a))})),L=s.a.createElement(On,{color:"primary",headers:[V.name,V.provider,V.gateway,V.address,V.allocated,V.operates],rows:G})}return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12,sm:12,md:12},s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:3,sm:3,md:3},s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){b(!0)}},s.a.createElement(Tt.a,null),V.createButton)))),s.a.createElement(Qe,{xs:12,sm:12,md:12},s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",{className:a.cardTitleWhite},V.tableTitle)),s.a.createElement(dn,null,L))),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:R,message:A,open:""!==A,closeNotification:F,close:!0})),s.a.createElement(Qe,null,s.a.createElement(al,{lang:U,open:f,onSuccess:function(e){H(),z("pool "+e+" created"),M()},onCancel:H})),s.a.createElement(Qe,null,s.a.createElement(nl,{lang:U,open:h,pool:j,onSuccess:function(e){W(),z("pool "+e+" modified"),M()},onCancel:W})),s.a.createElement(Qe,null,s.a.createElement(Xo,{lang:U,open:k,pool:j,onSuccess:function(e){q(),z("pool "+e+" deleted"),M()},onCancel:q})))}var cl=t(58),il=t.n(cl),sl=t(86),ul=t.n(sl),ml={en:{title:"Remove Address Range",content:"Are you sure to remove address range ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u5730\u5740\u6bb5",content:"\u662f\u5426\u5220\u9664\u5730\u5740\u6bb5 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function dl(e){var a=e.lang,t=e.open,o=e.poolName,l=e.rangeType,r=e.startAddress,c=e.onSuccess,i=e.onCancel,u=s.a.useState(!0),m=Object(n.a)(u,2),d=m[0],p=m[1],f=s.a.useState(""),b=Object(n.a)(f,2),g=b[0],E=b[1],h=ml[a],v=h.title,y=function(e){p(!0),E(e)},x=function(){p(!0),E(""),c(l,r)},k=h.content+r,C=[{color:"transparent",label:h.cancel,onClick:function(){E(""),i()}},{color:"info",label:h.confirm,onClick:function(){p(!1),function(e,a,t,n,o){Za("/address_pools/"+e+"/"+a+"/ranges/"+t,(function(){n(t,e)}),o)}(o,l,r,x,y)}}];return s.a.createElement(oa,{size:"xs",open:t,prompt:g,title:v,buttons:C,content:k,operatable:d})}var pl={en:{title:"Add Address Range",type:"Range Type",internal:"Internal Address",external:"External Address",start:"Start Address",end:"End Address",netmask:"Netmask",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6dfb\u52a0\u5730\u5740\u6bb5",type:"\u7c7b\u578b",internal:"\u5185\u90e8\u5730\u5740\u6bb5",external:"\u5916\u90e8\u5730\u5740\u6bb5",start:"\u8d77\u59cb\u5730\u5740",end:"\u7ed3\u675f\u5730\u5740",netmask:"\u5b50\u7f51\u63a9\u7801",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function fl(e){var a={type:"internal",start:"",end:"",netmask:""},t=e.lang,o=e.poolName,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!0),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(""),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(a),v=Object(n.a)(h,2),y=v[0],x=v[1],k=pl[t],C=k.title,S=function(e){d(!0),g(e)},O=function(){g(""),x(a)},j=function(){d(!0),O(),r(y.type,y.start)},w=function(e){return function(a){var t=a.target.value;x((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}},_=[{type:"radio",label:k.type,onChange:w("type"),value:y.type,options:[{label:k.internal,value:"internal"}],required:!0,oneRow:!0,xs:12},{type:"text",label:k.start,onChange:w("start"),value:y.start,required:!0,oneRow:!0,xs:12,sm:8,md:6},{type:"text",label:k.end,onChange:w("end"),value:y.end,required:!0,oneRow:!0,xs:12,sm:8,md:6},{type:"text",label:k.netmask,onChange:w("netmask"),value:y.netmask,oneRow:!0,xs:12,sm:8,md:6}],N=s.a.createElement(ze,{inputs:_}),R=[{color:"transparent",label:k.cancel,onClick:function(){O(),c()}},{color:"info",label:k.confirm,onClick:function(){d(!1);var e=new RegExp("^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?).){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");y.start?e.test(y.start)?y.end?e.test(y.end)?y.netmask?e.test(y.netmask)?function(e,a,t,n,o,l,r){Ua("/address_pools/"+e+"/"+a+"/ranges/"+t,{end:n,netmask:o},(function(){l(t,e)}),r)}(o,y.type,y.start,y.end,y.netmask,j,S):S("invalid netmask format"):S("must specify netmask"):S("invalid end address format"):S("must specify end address"):S("invalid start start format"):S("must specify start address")}}];return s.a.createElement(oa,{size:"sm",open:l,prompt:b,title:C,buttons:R,content:N,operatable:m})}var bl=Object(g.a)({cardCategoryWhite:{"&,& a,& a:hover,& a:focus":{color:"rgba(255,255,255,.62)",margin:"0",fontSize:"14px",marginTop:"0",marginBottom:"0"},"& a,& a:hover,& a:focus":{color:"#FFFFFF"}},cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),gl={en:{back:"Back",createButton:"Add Address Range",tableTitle:"Address Pool Status",internal:"Internal Address Range",allocated:"Allocated Address",startAddress:"Start Address",endAddress:"End Address",netmask:"Netmask",noInternalRange:"No internal range available",noAllocated:"No address allocated",operates:"Operates",allocatedAddress:"Allocated Address",instance:"Instance",detail:"Detail",remove:"Remove"},cn:{back:"\u8fd4\u56de",createButton:"\u6dfb\u52a0\u5730\u5740\u6bb5",tableTitle:"\u5730\u5740\u8d44\u6e90\u6c60\u72b6\u6001",internal:"\u5185\u90e8\u5730\u5740\u6bb5",allocated:"\u5df2\u5206\u914d\u5730\u5740",startAddress:"\u5f00\u59cb\u5730\u5740",endAddress:"\u7ed3\u675f\u5730\u5740",netmask:"\u5b50\u7f51\u63a9\u7801",noInternalRange:"\u6ca1\u6709\u5185\u90e8\u5730\u5740\u6bb5",noAllocated:"\u672a\u5206\u914d\u5730\u5740",operates:"\u64cd\u4f5c",allocatedAddress:"\u5df2\u5206\u914d\u5730\u5740",instance:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",detail:"\u8be6\u60c5",remove:"\u5220\u9664"}};function El(e){var a,t=e.lang,o=gl[t],l=e.match.params.pool,r=bl(),c=s.a.useState(!1),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(null),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(!1),h=Object(n.a)(g,2),v=h[0],y=h[1],x=s.a.useState(!1),k=Object(n.a)(x,2),C=k[0],S=k[1],O=s.a.useState({type:"",start:""}),j=Object(n.a)(O,2),w=j[0],_=j[1],N=s.a.useState("warning"),R=Object(n.a)(N,2),T=R[0],I=R[1],D=s.a.useState(""),A=Object(n.a)(D,2),P=A[0],F=A[1],B=function(){F("")},M=s.a.useCallback((function(e){if(u){I("warning"),F(e),setTimeout(B,3e3)}}),[I,F,u]),z=s.a.useCallback((function(){if(u){va(l,b,(function(e){u&&M(e)}))}}),[M,l,u]),W=function(e){if(u){I("info"),F(e),Wa(e),setTimeout(B,3e3)}},q=function(){S(!1)},H=function(){y(!1)};if(s.a.useEffect((function(){return m(!0),z(),function(){m(!1)}}),[z]),null===f)a=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});else{var L;if(f.ranges&&0!==f.ranges.length){var U=[];f.ranges.forEach((function(e){var a=[{icon:Qo.a,label:o.detail,href:"/admin/address_pools/"+l+"/internal/ranges/"+e.start},{onClick:function(a){return t="internal",n=e.start,S(!0),void _({type:t,start:n});var t,n},icon:Wt.a,label:o.remove}];U.push(function(e,a){var t=a.map((function(e,a){return s.a.createElement(jn,Object(E.a)(Object(E.a)({},e),{},{key:a}))}));return[e.start,e.end,e.netmask,t]}(e,a))})),L=s.a.createElement(On,{color:"primary",headers:[o.startAddress,o.endAddress,o.netmask,o.operates],rows:U})}else L=s.a.createElement(K.a,{display:"flex",justifyContent:"center"},s.a.createElement(fn,null,o.noInternalRange));var V,G=s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",{className:r.cardTitleWhite},o.internal)),s.a.createElement(dn,null,L));f.allocated&&0!==f.allocated.length?(U=[],f.allocated.forEach((function(e){var a=[{icon:ul.a,label:o.detail,href:"/admin/instances/details/"+e.instance}];U.push(function(e,a){var t=e.address,n=e.instance,o=a.map((function(e,a){return s.a.createElement(jn,Object(E.a)(Object(E.a)({},e),{},{key:a}))}));return[t,n].concat(o)}(e,a))})),V=s.a.createElement(On,{color:"primary",headers:[o.allocatedAddress,o.instance,""],rows:U})):V=s.a.createElement(K.a,{display:"flex",justifyContent:"center"},s.a.createElement(fn,null,o.noAllocated));var $=s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",{className:r.cardTitleWhite},o.allocated)),s.a.createElement(dn,null,V));a=s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},G),s.a.createElement(Qe,{xs:12},$))}var Z=[s.a.createElement(fe,{key:"back",size:"sm",color:"info",round:!0,onClick:function(){e.history.goBack()}},s.a.createElement(il.a,null),o.back),s.a.createElement(fe,{key:"add",size:"sm",color:"info",round:!0,onClick:function(){y(!0)}},s.a.createElement(Tt.a,null),o.createButton)];return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{display:"flex"},Z.map((function(e,a){return s.a.createElement(K.a,{key:a,m:1},e)})))),s.a.createElement(Qe,{xs:12},a),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:T,message:P,open:""!==P,closeNotification:B,close:!0})),s.a.createElement(Qe,null,s.a.createElement(fl,{lang:t,poolName:l,open:v,onSuccess:function(e,a){H(),W('range "'+a+'" of '+e+" address added"),z()},onCancel:H})),s.a.createElement(Qe,null,s.a.createElement(dl,{lang:t,open:C,poolName:l,rangeType:w.type,startAddress:w.start,onSuccess:function(e,a){q(),W('range "'+a+'" of '+e+" address removed"),z()},onCancel:q})))}var hl=Object(g.a)({cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),vl={en:{back:"Back",internal:"Address Range Status",allocated:"Allocated Address",startAddress:"Start Address",endAddress:"End Address",netmask:"Netmask",noAllocated:"No address allocated",allocatedAddress:"Allocated Address",instance:"Instance",detail:"Detail"},cn:{back:"\u8fd4\u56de",internal:"\u5730\u5740\u6bb5\u72b6\u6001",allocated:"\u5df2\u5206\u914d\u5730\u5740",startAddress:"\u5f00\u59cb\u5730\u5740",endAddress:"\u7ed3\u675f\u5730\u5740",netmask:"\u5b50\u7f51\u63a9\u7801",noAllocated:"\u672a\u5206\u914d\u5730\u5740",allocatedAddress:"\u5df2\u5206\u914d\u5730\u5740",instance:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",detail:"\u8be6\u60c5"}};function yl(e){var a,t=e.match.params.pool,o=e.match.params.type,l=e.match.params.start,r=e.lang,c=vl[r],i=hl(),u=s.a.useState(!1),m=Object(n.a)(u,2),d=m[0],p=m[1],f=s.a.useState(null),b=Object(n.a)(f,2),g=b[0],h=b[1],v=s.a.useState("warning"),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState(""),S=Object(n.a)(C,2),O=S[0],j=S[1],w=function(){j("")},_=s.a.useCallback((function(e){if(d){k("warning"),j(e),setTimeout(w,3e3)}}),[k,j,d]),N=s.a.useCallback((function(){if(d){!function(e,a,t,n,o){Ha("/address_pools/"+e+"/"+a+"/ranges/"+t,n,o)}(t,o,l,h,(function(e){d&&_(e)}))}}),[_,t,o,l,d]);if(s.a.useEffect((function(){return p(!0),N(),function(){p(!1)}}),[N]),null===g)a=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});else{var R,T=s.a.createElement(On,{color:"primary",headers:[c.startAddress,c.endAddress,c.netmask],rows:[[l,g.end,g.netmask]]}),I=s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",{className:i.cardTitleWhite},c.internal)),s.a.createElement(dn,null,T));if(g.allocated&&0!==g.allocated.length){var D=[];g.allocated.forEach((function(e){var a=[{icon:ul.a,label:c.detail,href:"/admin/instances/details/"+e.instance}];D.push(function(e,a){var t=e.address,n=e.instance,o=a.map((function(e,a){return s.a.createElement(jn,Object(E.a)(Object(E.a)({},e),{},{key:a}))}));return[t,n].concat(o)}(e,a))})),R=s.a.createElement(On,{color:"primary",headers:[c.allocatedAddress,c.instance,""],rows:D})}else R=s.a.createElement(K.a,{display:"flex",justifyContent:"center"},s.a.createElement(fn,null,c.noAllocated));var A=s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",{className:i.cardTitleWhite},c.allocated)),s.a.createElement(dn,null,R));a=s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},I),s.a.createElement(Qe,{xs:12},A))}var P=[s.a.createElement(fe,{key:"back",size:"sm",color:"info",round:!0,onClick:function(){e.history.goBack()}},s.a.createElement(il.a,null),c.back)];return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{display:"flex"},P.map((function(e,a){return s.a.createElement(K.a,{key:a,m:1},e)})))),s.a.createElement(Qe,{xs:12},a),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:x,message:O,open:""!==O,closeNotification:w,close:!0})))}var xl={en:{title:"Delete Storage Pool",content:"Are you sure to delete storage pool ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u5b58\u50a8\u8d44\u6e90\u6c60",content:"\u662f\u5426\u5220\u9664\u5b58\u50a8\u8d44\u6e90\u6c60 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}},kl=function(e){var a=e.lang,t=e.pool,o=e.open,l=e.onSuccess,r=e.onCancel,c=s.a.useState(!0),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(""),p=Object(n.a)(d,2),f=p[0],b=p[1],g=xl[a],E=g.title,h=function(e){m(!0),b(e)},v=function(e){m(!0),b(""),l(e)},y=g.content+t,x=[{color:"transparent",label:g.cancel,onClick:function(){b(""),r()}},{color:"info",label:g.confirm,onClick:function(){m(!1),function(e,a,t){Za("/storage_pools/"+e,(function(){a(e)}),t)}(t,v,h)}}];return s.a.createElement(oa,{size:"xs",open:o,prompt:f,title:E,buttons:x,content:y,operatable:u})},Cl={en:{title:"Create Storage Pool",name:"Name",type:"Type",host:"Host",target:"Target",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u521b\u5efa\u5b58\u50a8\u8d44\u6e90\u6c60",name:"\u540d\u79f0",type:"\u7c7b\u578b",host:"\u4e3b\u673a",target:"\u76ee\u6807",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Sl(e){var a={name:"",type:"nfs",host:"",target:""},t=e.lang,o=e.open,l=e.onSuccess,r=e.onCancel,c=s.a.useState(!0),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(""),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(a),h=Object(n.a)(g,2),v=h[0],y=h[1],x={type:[{value:"nfs",label:"NFS"}]},k=Cl[t],C=function(e){m(!0),b(e)},S=function(){b(""),y(a)},O=function(e){S(),m(!0),l(e)},j=function(e){return function(a){var t=a.target.value;y((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}},w=[{type:"text",label:k.name,onChange:j("name"),value:v.name,required:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"select",label:k.type,onChange:j("type"),value:v.type,options:x.type,required:!0,oneRow:!0,xs:12,sm:8,md:6},{type:"text",label:k.host,onChange:j("host"),value:v.host,required:!0,oneRow:!0,xs:12,sm:10,md:8},{type:"text",label:k.target,onChange:j("target"),value:v.target,required:!0,oneRow:!0,xs:12,sm:10,md:8}],_=s.a.createElement(ze,{inputs:w}),N=[{color:"transparent",label:k.cancel,onClick:function(){S(),r()}},{color:"info",label:k.confirm,onClick:function(){if(m(!1),v.name)if(v.type)if(v.host)if(v.target){var e=new RegExp("^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?).){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]).)+([A-Za-z]|[A-Za-z][A-Za-z0-9-]*[A-Za-z0-9])$"),a=new RegExp("^(/[^/ ]*)+/?$");e.test(v.host)?a.test(v.target)?function(e,a,t,n,o,l){Ua("/storage_pools/"+e,{type:a,host:t,target:n},(function(){o(e)}),l)}(v.name,v.type,v.host,v.target,O,C):C("invalid target format"):C("invalid host format")}else C("must specify storage target");else C("must specify storage host");else C("must specify storage type");else C("must specify storage name")}}];return s.a.createElement(oa,{size:"sm",open:o,prompt:f,title:k.title,buttons:N,content:_,operatable:u})}var Ol={en:{title:"Modify Storage Pool",name:"Name",type:"Type",host:"Host",target:"Target",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u5b58\u50a8\u8d44\u6e90\u6c60",name:"\u540d\u79f0",type:"\u7c7b\u578b",host:"\u4e3b\u673a",target:"\u76ee\u6807",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function jl(e){var a,t={name:"",type:"",host:"",target:""},o=e.lang,l=e.pool,r=e.open,c=e.onSuccess,i=e.onCancel,u=s.a.useState(!1),m=Object(n.a)(u,2),d=m[0],p=m[1],f=s.a.useState(!0),b=Object(n.a)(f,2),g=b[0],h=b[1],v=s.a.useState(""),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState(!1),S=Object(n.a)(C,2),O=S[0],j=S[1],w=s.a.useState(t),_=Object(n.a)(w,2),N=_[0],R=_[1],T={type:[{value:"nfs",label:"NFS"}]},I=Ol[o],D=s.a.useCallback((function(e){O&&(h(!0),k(e))}),[O]),A=function(){k(""),R(t),p(!1)},P=function(e){O&&(A(),h(!0),c(e))},F=function(e){return function(a){var t=a.target.value;R((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}};if(s.a.useEffect((function(){if(l&&r){j(!0);return function(e,a,t){Ha("/storage_pools/"+e,a,t)}(l,(function(e){O&&(R({type:e.type,host:e.host,target:e.target}),p(!0))}),D),function(){j(!1)}}}),[d,r,l,O,D]),d){var B=[{type:"text",label:I.name,value:l,disabled:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"select",label:I.type,onChange:F("type"),value:N.type,options:T.type,required:!0,oneRow:!0,xs:12,sm:8,md:6},{type:"text",label:I.host,onChange:F("host"),value:N.host,required:!0,oneRow:!0,xs:12,sm:10,md:8},{type:"text",label:I.target,onChange:F("target"),value:N.target,required:!0,oneRow:!0,xs:12,sm:10,md:8}];a=s.a.createElement(ze,{inputs:B})}else a=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});var M=[{color:"transparent",label:I.cancel,onClick:function(){A(),i()}},{color:"info",label:I.confirm,onClick:function(){if(h(!1),N.type)if(N.host)if(N.target){var e=new RegExp("^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?).){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]).)+([A-Za-z]|[A-Za-z][A-Za-z0-9-]*[A-Za-z0-9])$"),a=new RegExp("^(/[^/ ]*)+/?$");e.test(N.host)?a.test(N.target)?function(e,a,t,n,o,l){$a("/storage_pools/"+e,{type:a,host:t,target:n},(function(){o(e)}),l)}(l,N.type,N.host,N.target,P,D):D("invalid target format"):D("invalid host format")}else D("must specify storage target");else D("must specify storage host");else D("must specify storage type")}}],z=I.title+" "+l;return s.a.createElement(oa,{size:"sm",open:r,prompt:x,title:z,buttons:M,content:a,operatable:g})}var wl=Object(g.a)({cardCategoryWhite:{"&,& a,& a:hover,& a:focus":{color:"rgba(255,255,255,.62)",margin:"0",fontSize:"14px",marginTop:"0",marginBottom:"0"},"& a,& a:hover,& a:focus":{color:"#FFFFFF"}},cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),_l={en:{createButton:"Create Storage Pool",tableTitle:"Storage Pools",name:"Name",type:"Type",host:"Host",target:"Target",operates:"Operates",noResource:"No storage pool available",modify:"Modify",delete:"Delete"},cn:{createButton:"\u521b\u5efa\u5b58\u50a8\u8d44\u6e90\u6c60",tableTitle:"\u5b58\u50a8\u8d44\u6e90\u6c60",name:"\u540d\u79f0",type:"\u7c7b\u578b",host:"\u4e3b\u673a",target:"\u76ee\u6807",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u5b58\u50a8\u8d44\u6e90\u6c60",modify:"\u4fee\u6539",delete:"\u5220\u9664"}};var Nl=t(187),Rl=t.n(Nl),Tl=t(188),Il=t.n(Tl),Dl=t(619),Al={en:{title:"Delete Media Image",content:"Are you sure to delete media image ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u5149\u76d8\u955c\u50cf",content:"\u662f\u5426\u5220\u9664\u5149\u76d8\u955c\u50cf ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Pl(e){var a=e.lang,t=e.imageID,o=e.open,l=e.onSuccess,r=e.onCancel,c=s.a.useState(!0),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(""),p=Object(n.a)(d,2),f=p[0],b=p[1],g=Al[a],E=g.title,h=g.content+t,v=function(e){m(!0),b(e)},y=function(){m(!0),b(""),l(t)},x=[{color:"transparent",label:g.cancel,onClick:function(){b(""),r()}},{color:"info",label:g.confirm,onClick:function(){m(!1),Na(t,y,v)}}];return s.a.createElement(oa,{size:"xs",open:o,prompt:f,title:E,buttons:x,content:h,operatable:u})}var Fl=t(598),Bl={en:{title:"Upload New Image",name:"Image Name",description:"Description",tags:"Tags",file:"Image File",choose:"Choose File",cancel:"Cancel",confirm:"Upload"},cn:{title:"\u4e0a\u4f20\u65b0\u955c\u50cf",name:"\u955c\u50cf\u540d\u79f0",description:"\u63cf\u8ff0",tags:"\u6807\u7b7e",file:"\u955c\u50cf\u6587\u4ef6",choose:"\u6d4f\u89c8\u6587\u4ef6",cancel:"\u53d6\u6d88",confirm:"\u4e0a\u4f20"}};function Ml(e){var a={name:"",description:"",tags:new Map,file:null},t=e.lang,o=e.open,l=e.onSuccess,r=e.onCancel,c=s.a.useState(!1),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(!1),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(0),h=Object(n.a)(g,2),v=h[0],y=h[1],x=s.a.useState(!0),k=Object(n.a)(x,2),C=k[0],S=k[1],O=s.a.useState(""),j=Object(n.a)(O,2),w=j[0],_=j[1],N=s.a.useState(!1),R=Object(n.a)(N,2),T=R[0],I=R[1],D=s.a.useState(a),A=Object(n.a)(D,2),P=A[0],F=A[1],B=s.a.useState({tags:[]}),M=Object(n.a)(B,2),z=M[0],W=M[1],q=Bl[t],H=q.title,L=s.a.useCallback((function(e){T&&(S(!0),_(e))}),[T]),U=function(){_(""),F(a),m(!1),b(!1),y(0)},V=function(e){T&&(S(!0),U(),l(e))},G=function(e){T&&y(e)},$=function(e){return function(a){if(T){var t=a.target.value;F((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}}};s.a.useEffect((function(){if(o){I(!0);var e=[];return[["linux","Linux"],["windows","Windows"],["centos","Centos"],["ubuntu","Ubuntu"],["64bit","64Bit"],["32bit","32Bit"]].forEach((function(a){e.push({label:a[1],value:a[0]})})),W({tags:e}),m(!0),function(){I(!1)}}}),[o]);var Z,J,Q=[{color:"transparent",label:q.cancel,onClick:function(){U(),r()}}];if(u)if(f)Z=s.a.createElement(De.a,{container:!0},s.a.createElement(De.a,{item:!0,xs:12},s.a.createElement(Fl.a,{variant:"determinate",value:v})),s.a.createElement(De.a,{item:!0,xs:12},s.a.createElement(oe.a,{align:"center"},v.toFixed(2)+"%")));else{var K=[{type:"text",label:q.name,value:P.name,onChange:$("name"),required:!0,oneRow:!0,xs:8},{type:"textarea",label:q.description,value:P.description,onChange:$("description"),required:!0,oneRow:!0,rows:4,xs:12},{type:"checkbox",label:q.tags,onChange:function(e){return function(a){if(T){var t=a.target.checked;F((function(a){return Object(E.a)(Object(E.a)({},a),{},{tags:a.tags.set(e,t)})}))}}},value:P.tags,options:z.tags,required:!0,oneRow:!0,xs:10},{type:"file",label:q.file,onChange:(J="file",function(e){if(T){var a=e.target.files[0];F((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},J,a))}))}}),required:!0,oneRow:!0,xs:12}];Z=s.a.createElement(ze,{inputs:K}),Q.push({color:"info",label:q.confirm,onClick:function(){_(""),S(!1);var e=P.name;if(""!==e){var a=P.description;if(""!==a)if(P.tags){var t=[];if(P.tags.forEach((function(e,a){e&&t.push(a)})),0!==t.length)if(P.file){!function(e,a,t,n,o){var l=ca();if(null!==l){Ua("/media_images/",{name:e,description:a,tags:t,owner:l.user,group:l.group},(function(e){n(e.id)}),o)}else o("session expired")}(e,a,t,(function(a){if(T){var t=function(){L("new image "+e+" deleted")},n=function(a){L("delete new image "+e+" fail: "+a)};b(!0),function(e,a,t,n,o){Qa("/media_images/"+e+"/file/","image",a,t,(function(){n(e)}),o)}(a,P.file,G,V,(function(){Na(a,t,n)}))}}),L)}else L("must specify upload file");else L("image tags required")}else L("image tags required");else L("desciption required")}else L("must specify image name")}})}else Z=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:o,prompt:w,hideBackdrop:!0,title:H,buttons:Q,content:Z,operatable:C})}var zl={en:{title:"Modify Media Image",name:"Image Name",description:"Description",tags:"Tags",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u955c\u50cf\u4fe1\u606f",name:"\u955c\u50cf\u540d\u79f0",description:"\u63cf\u8ff0",tags:"\u6807\u7b7e",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function Wl(e){var a={name:"",description:"",tags:new Map},t=e.lang,o=e.imageID,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!1),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!0),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(""),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(!1),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState(a),w=Object(n.a)(j,2),_=w[0],N=w[1],R=s.a.useState({tags:[]}),T=Object(n.a)(R,2),I=T[0],D=T[1],A=zl[t],P=A.title,F=s.a.useCallback((function(e){S&&(g(!0),x(e))}),[S]),B=function(){x(""),N(a),d(!1)},M=function(e){S&&(g(!0),B(),r(e))},z=function(e){return function(a){if(S){var t=a.target.value;N((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}}};s.a.useEffect((function(){if(l){var e=[["linux","Linux"],["windows","Windows"],["centos","Centos"],["ubuntu","Ubuntu"],["64bit","64Bit"],["32bit","32Bit"]];O(!0);return function(e,a,t){Ha("/media_images/"+e,a,t)}(o,(function(a){if(S){var t=[];e.forEach((function(e){t.push({label:e[1],value:e[0]})})),D({tags:t});var n=new Map;a.tags&&a.tags.forEach((function(e){n.set(e,!0)})),N({name:a.name,description:a.description,tags:n}),d(!0)}}),F),function(){O(!1)}}}),[l,o,S,F]);var W,q=[{color:"transparent",label:A.cancel,onClick:function(){B(),c()}}];if(m){var H=[{type:"text",label:A.name,value:_.name,onChange:z("name"),required:!0,oneRow:!0,xs:8},{type:"textarea",label:A.description,value:_.description,onChange:z("description"),required:!0,oneRow:!0,rows:4,xs:12},{type:"checkbox",label:A.tags,onChange:function(e){return function(a){if(S){var t=a.target.checked;N((function(a){return Object(E.a)(Object(E.a)({},a),{},{tags:a.tags.set(e,t)})}))}}},value:_.tags,options:I.tags,required:!0,oneRow:!0,xs:10}];W=s.a.createElement(ze,{inputs:H}),q.push({color:"info",label:A.confirm,onClick:function(){g(!1);var e=_.name;if(""!==e){var a=_.description;if(""!==a)if(_.tags){var t=[];_.tags.forEach((function(e,a){e&&t.push(a)})),0!==t.length?function(e,a,t,n,o,l){var r=ca();if(null!==r){$a("/media_images/"+e,{name:a,description:t,tags:n,owner:r.user,group:r.group},(function(){o(e)}),l)}else l("session expired")}(o,e,a,t,M,F):F("image tags required")}else F("image tags required");else F("desciption required")}else F("must specify image name")}})}else W=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:l,prompt:y,title:P,buttons:q,content:W,operatable:b})}var ql={en:{title:"Sync Local Media Images",content:"Are you sure to synchronize local media images",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u540c\u6b65\u672c\u5730\u5149\u76d8\u955c\u50cf",content:"\u662f\u5426\u540c\u6b65\u672c\u5730\u5149\u76d8\u955c\u50cf\u6587\u4ef6",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Hl(e){var a=e.lang,t=e.open,o=e.onSuccess,l=e.onCancel,r=s.a.useState(!0),c=Object(n.a)(r,2),i=c[0],u=c[1],m=s.a.useState(""),d=Object(n.a)(m,2),p=d[0],f=d[1],b=ql[a],g=b.title,E=b.content,h=function(e){u(!0),f(e)},v=function(){u(!0),f(""),o()},y=[{color:"transparent",label:b.cancel,onClick:function(){f(""),l()}},{color:"info",label:b.confirm,onClick:function(){u(!1),function(e,a){var t=ca();null!==t?Ya("/media_images/",{owner:t.user,group:t.group},e,a):a("session expired")}(v,h)}}];return s.a.createElement(oa,{size:"xs",open:t,prompt:p,title:g,buttons:y,content:E,operatable:i})}var Ll={en:{modify:"Modify Info",delete:"Delete Image",createTime:"Created Time",modifyTime:"Modified Time",uploadButton:"Upload New ISO",syncButton:"Synchronize Local Images",noResource:"No images available"},cn:{modify:"\u4fee\u6539\u955c\u50cf\u4fe1\u606f",delete:"\u5220\u9664\u955c\u50cf",createTime:"\u521b\u5efa\u65f6\u95f4",modifyTime:"\u4fee\u6539\u65f6\u95f4",uploadButton:"\u4e0a\u4f20\u65b0\u5149\u76d8\u955c\u50cf",syncButton:"\u540c\u6b65\u672c\u5730\u955c\u50cf\u6587\u4ef6",noResource:"\u6ca1\u6709\u5149\u76d8\u955c\u50cf"}};var Ul=t(293),Vl=t.n(Ul),Gl={en:{title:"Delete Disk Image",content:"Are you sure to delete disk image ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u78c1\u76d8\u955c\u50cf",content:"\u662f\u5426\u5220\u9664\u78c1\u76d8\u955c\u50cf ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function $l(e){var a=e.lang,t=e.imageID,o=e.open,l=e.onSuccess,r=e.onCancel,c=s.a.useState(!0),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(""),p=Object(n.a)(d,2),f=p[0],b=p[1],g=Gl[a],E=g.title,h=g.content+t,v=function(e){m(!0),b(e)},y=function(){m(!0),b(""),l(t)},x=[{color:"transparent",label:g.cancel,onClick:function(){b(""),r()}},{color:"info",label:g.confirm,onClick:function(){m(!1),Da(t,y,v)}}];return s.a.createElement(oa,{size:"xs",open:o,prompt:f,title:E,buttons:x,content:h,operatable:u})}var Zl={en:{title:"Upload New Image",name:"Image Name",description:"Description",tags:"Tags",file:"Image File",choose:"Choose File",cancel:"Cancel",confirm:"Upload"},cn:{title:"\u4e0a\u4f20\u65b0\u955c\u50cf",name:"\u955c\u50cf\u540d\u79f0",description:"\u63cf\u8ff0",tags:"\u6807\u7b7e",file:"\u955c\u50cf\u6587\u4ef6",choose:"\u6d4f\u89c8\u6587\u4ef6",cancel:"\u53d6\u6d88",confirm:"\u4e0a\u4f20"}};function Yl(e){var a={name:"",description:"",tags:new Map,file:null},t=e.lang,o=e.open,l=e.onSuccess,r=e.onCancel,c=s.a.useState(!1),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(!1),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(0),h=Object(n.a)(g,2),v=h[0],y=h[1],x=s.a.useState(!0),k=Object(n.a)(x,2),C=k[0],S=k[1],O=s.a.useState(""),j=Object(n.a)(O,2),w=j[0],_=j[1],N=s.a.useState(!1),R=Object(n.a)(N,2),T=R[0],I=R[1],D=s.a.useState(a),A=Object(n.a)(D,2),P=A[0],F=A[1],B=s.a.useState({tags:[]}),M=Object(n.a)(B,2),z=M[0],W=M[1],q=Zl[t],H=q.title,L=s.a.useCallback((function(e){T&&(S(!0),_(e))}),[T]),U=function(){_(""),F(a),m(!1),b(!1),y(0)},V=function(e){T&&(S(!0),U(),l(e))},G=function(e){T&&y(e)},$=function(e){return function(a){if(T){var t=a.target.value;F((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}}};s.a.useEffect((function(){if(o){I(!0);var e=[];return[["linux","Linux"],["windows","Windows"],["centos","Centos"],["ubuntu","Ubuntu"],["64bit","64Bit"],["32bit","32Bit"]].forEach((function(a){e.push({label:a[1],value:a[0]})})),W({tags:e}),m(!0),function(){I(!1)}}}),[o]);var Z,J,Q=[{color:"transparent",label:q.cancel,onClick:function(){U(),r()}}];if(u)if(f)Z=s.a.createElement(De.a,{container:!0},s.a.createElement(De.a,{item:!0,xs:12},s.a.createElement(Fl.a,{variant:"determinate",value:v})),s.a.createElement(De.a,{item:!0,xs:12},s.a.createElement(oe.a,{align:"center"},v.toFixed(2)+"%")));else{var K=[{type:"text",label:q.name,value:P.name,onChange:$("name"),required:!0,oneRow:!0,xs:8},{type:"textarea",label:q.description,value:P.description,onChange:$("description"),required:!0,oneRow:!0,rows:4,xs:12},{type:"checkbox",label:q.tags,onChange:function(e){return function(a){if(T){var t=a.target.checked;F((function(a){return Object(E.a)(Object(E.a)({},a),{},{tags:a.tags.set(e,t)})}))}}},value:P.tags,options:z.tags,required:!0,oneRow:!0,xs:10},{type:"file",label:q.file,onChange:(J="file",function(e){if(T){var a=e.target.files[0];F((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},J,a))}))}}),required:!0,oneRow:!0,xs:12}];Z=s.a.createElement(ze,{inputs:K}),Q.push({color:"info",label:q.confirm,onClick:function(){_(""),S(!1);var e=P.name;if(""!==e){var a=P.description;if(""!==a)if(P.tags){var t=[];if(P.tags.forEach((function(e,a){e&&t.push(a)})),0!==t.length)if(P.file){Ia(e,null,a,t,(function(a){if(T){var t=function(){L("new image "+e+" deleted")},n=function(a){L("delete new image "+e+" fail: "+a)};b(!0),function(e,a,t,n,o){Qa("/disk_images/"+e+"/file/","image",a,t,(function(){n(e)}),o)}(a,P.file,G,V,(function(){Da(a,t,n)}))}}),L)}else L("must specify upload file");else L("image tags required")}else L("image tags required");else L("desciption required")}else L("must specify image name")}})}else Z=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:o,prompt:w,hideBackdrop:!0,title:H,buttons:Q,content:Z,operatable:C})}var Jl={en:{title:"Modify Disk Image",name:"Image Name",description:"Description",tags:"Tags",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u955c\u50cf\u4fe1\u606f",name:"\u955c\u50cf\u540d\u79f0",description:"\u63cf\u8ff0",tags:"\u6807\u7b7e",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function Ql(e){var a={name:"",description:"",tags:new Map},t=e.lang,o=e.imageID,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!1),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!0),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(""),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(!1),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState(a),w=Object(n.a)(j,2),_=w[0],N=w[1],R=s.a.useState({tags:[]}),T=Object(n.a)(R,2),I=T[0],D=T[1],A=Jl[t],P=A.title,F=s.a.useCallback((function(e){S&&(g(!0),x(e))}),[S]),B=function(){x(""),N(a),d(!1)},M=function(e){S&&(g(!0),B(),r(e))},z=function(e){return function(a){if(S){var t=a.target.value;N((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}}};s.a.useEffect((function(){if(l){var e=[["linux","Linux"],["windows","Windows"],["centos","Centos"],["ubuntu","Ubuntu"],["64bit","64Bit"],["32bit","32Bit"]];O(!0);return Ta(o,(function(a){if(S){var t=[];e.forEach((function(e){t.push({label:e[1],value:e[0]})})),D({tags:t});var n=new Map;a.tags&&a.tags.forEach((function(e){n.set(e,!0)})),N({name:a.name,description:a.description,tags:n}),d(!0)}}),F),function(){O(!1)}}}),[S,l,o,F]);var W,q=[{color:"transparent",label:A.cancel,onClick:function(){B(),c()}}];if(m){var H=[{type:"text",label:A.name,value:_.name,onChange:z("name"),required:!0,oneRow:!0,xs:8},{type:"textarea",label:A.description,value:_.description,onChange:z("description"),required:!0,oneRow:!0,rows:4,xs:12},{type:"checkbox",label:A.tags,onChange:function(e){return function(a){if(S){var t=a.target.checked;N((function(a){return Object(E.a)(Object(E.a)({},a),{},{tags:a.tags.set(e,t)})}))}}},value:_.tags,options:I.tags,required:!0,oneRow:!0,xs:10}];W=s.a.createElement(ze,{inputs:H}),q.push({color:"info",label:A.confirm,onClick:function(){g(!1);var e=_.name;if(""!==e){var a=_.description;if(""!==a)if(_.tags){var t=[];_.tags.forEach((function(e,a){e&&t.push(a)})),0!==t.length?function(e,a,t,n,o,l){var r=ca();if(null!==r){$a("/disk_images/"+e,{name:a,description:t,tags:n,owner:r.user,group:r.group},(function(){o(e)}),l)}else l("session expired")}(o,e,a,t,M,F):F("image tags required")}else F("image tags required");else F("desciption required")}else F("must specify image name")}})}else W=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:l,prompt:y,title:P,buttons:q,content:W,operatable:b})}var Kl={en:{title:"Build New Image",name:"Image Name",description:"Description",tags:"Tags",pool:"Compute Pool",guest:"Source Instance",cancel:"Cancel",confirm:"Build"},cn:{title:"\u6784\u5efa\u65b0\u955c\u50cf",name:"\u955c\u50cf\u540d\u79f0",description:"\u63cf\u8ff0",tags:"\u6807\u7b7e",pool:"\u8d44\u6e90\u6c60",guest:"\u6e90\u4e91\u4e3b\u673a",cancel:"\u53d6\u6d88",confirm:"\u6784\u5efa"}};function Xl(e){var a={name:"",description:"",tags:new Map,pool:"",guest:""},t=e.lang,o=e.open,l=e.onSuccess,r=e.onCancel,c=s.a.useState(!1),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(!1),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(0),h=Object(n.a)(g,2),v=h[0],y=h[1],x=s.a.useState(!0),k=Object(n.a)(x,2),C=k[0],S=k[1],O=s.a.useState(""),j=Object(n.a)(O,2),w=j[0],_=j[1],N=s.a.useState(!1),R=Object(n.a)(N,2),T=R[0],I=R[1],D=s.a.useState(a),A=Object(n.a)(D,2),P=A[0],F=A[1],B=s.a.useState({tags:[],pools:[],guests:[]}),M=Object(n.a)(B,2),z=M[0],W=M[1],q=Kl[t],H=q.title,L=s.a.useCallback((function(e){T&&(S(!0),_(e))}),[T]),U=function(e){return function(a){Da(e),L(a)}},V=function(){_(""),F(a),m(!1),b(!1),y(0)},G=function e(a,t){return function(n){T&&(n.created?function(e){T&&(S(!0),V(),l(e))}(a):(y(n.progress),setTimeout((function(){Ta(a,e(a,t),U(a))}),1e3)))}},$=function(e){return function(a){if(T){var t=a.target.value;F((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}}};s.a.useEffect((function(){if(o){var e=[["linux","Linux"],["windows","Windows"],["centos","Centos"],["ubuntu","Ubuntu"],["64bit","64Bit"],["32bit","32Bit"]];I(!0);return fa((function(a){if(T){var t=[];a.forEach((function(e){var a=e.name;t.push({label:a,value:a})}));var n=[];e.forEach((function(e){n.push({label:e[1],value:e[0]})})),W({tags:n,pools:t,guests:[]}),F((function(e){return Object(E.a)(Object(E.a)({},e),{},{pool:"",guest:""})})),m(!0)}}),L),function(){I(!1)}}}),[T,o,L]);var Z,J=[{color:"transparent",label:q.cancel,onClick:function(){V(),r()}}];if(u)if(f)Z=s.a.createElement(De.a,{container:!0},s.a.createElement(De.a,{item:!0,xs:12},s.a.createElement(Fl.a,{variant:"determinate",value:v})),s.a.createElement(De.a,{item:!0,xs:12},s.a.createElement(oe.a,{align:"center"},v.toFixed(2)+"%")));else{var Q=[{type:"text",label:q.name,value:P.name,onChange:$("name"),required:!0,oneRow:!0,xs:8},{type:"textarea",label:q.description,value:P.description,onChange:$("description"),required:!0,oneRow:!0,rows:4,xs:12},{type:"checkbox",label:q.tags,onChange:function(e){return function(a){if(T){var t=a.target.checked;F((function(a){return Object(E.a)(Object(E.a)({},a),{},{tags:a.tags.set(e,t)})}))}}},value:P.tags,options:z.tags,required:!0,oneRow:!0,xs:10},{type:"select",label:q.pool,onChange:function(e){if(T){var a=e.target.value;ya(a,null,(function(e){var t=[];e.forEach((function(e){t.push({value:e.id,label:e.name})})),W((function(e){return Object(E.a)(Object(E.a)({},e),{},{guests:t})})),F((function(e){return Object(E.a)(Object(E.a)({},e),{},{pool:a,guest:""})}))}),L)}},value:P.pool,options:z.pools,required:!0,oneRow:!0,xs:10,sm:8,md:6},{type:"select",label:q.guest,onChange:$("guest"),value:P.guest,options:z.guests,required:!0,oneRow:!0,xs:10,sm:8,md:6}];Z=s.a.createElement(ze,{inputs:Q}),J.push({color:"info",label:q.confirm,onClick:function(){if(_(""),S(!1),P.name)if(P.description)if(P.tags){var e=[];if(P.tags.forEach((function(a,t){a&&e.push(t)})),0!==e.length)if(P.guest){var a=P.name;Ia(a,P.guest,P.description,e,function(e){return function(a){T&&(b(!0),setTimeout((function(){Ta(a,G(a,e),U(a))}),1e3))}}(a),L)}else L("must specify source guest");else L("image tags required")}else L("image tags required");else L("desciption required");else L("must specify image name")}})}else Z=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:o,prompt:w,hideBackdrop:!0,title:H,buttons:J,content:Z,operatable:C})}var er={en:{title:"Sync Local Disk Images",content:"Are you sure to synchronize local disk images",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u540c\u6b65\u672c\u5730\u78c1\u76d8\u955c\u50cf",content:"\u662f\u5426\u540c\u6b65\u672c\u5730\u78c1\u76d8\u955c\u50cf\u6587\u4ef6",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function ar(e){var a=e.lang,t=e.open,o=e.onSuccess,l=e.onCancel,r=s.a.useState(!0),c=Object(n.a)(r,2),i=c[0],u=c[1],m=s.a.useState(""),d=Object(n.a)(m,2),p=d[0],f=d[1],b=er[a],g=b.title,E=b.content,h=function(e){u(!0),f(e)},v=function(){u(!0),f(""),o()},y=[{color:"transparent",label:b.cancel,onClick:function(){f(""),l()}},{color:"info",label:b.confirm,onClick:function(){u(!1),function(e,a){var t=ca();null!==t?Ya("/disk_images/",{owner:t.user,group:t.group},e,a):a("session expired")}(v,h)}}];return s.a.createElement(oa,{size:"xs",open:t,prompt:p,title:g,buttons:y,content:E,operatable:i})}var tr={en:{modify:"Modify Info",delete:"Delete Image",download:"Download Image",createTime:"Created Time",modifyTime:"Modified Time",uploadButton:"Upload New Disk Image",buildButton:"Build New Disk Image",syncButton:"Synchronize Local Images",noResource:"No images available"},cn:{modify:"\u4fee\u6539\u955c\u50cf\u4fe1\u606f",delete:"\u5220\u9664\u955c\u50cf",download:"\u4e0b\u8f7d\u955c\u50cf",createTime:"\u521b\u5efa\u65f6\u95f4",modifyTime:"\u4fee\u6539\u65f6\u95f4",uploadButton:"\u4e0a\u4f20\u65b0\u78c1\u76d8\u955c\u50cf",buildButton:"\u6784\u5efa\u65b0\u78c1\u76d8\u955c\u50cf",syncButton:"\u540c\u6b65\u672c\u5730\u955c\u50cf\u6587\u4ef6",noResource:"\u6ca1\u6709\u78c1\u76d8\u955c\u50cf"}};var nr={en:{title:"Delete System Template",content:"Are you sure to delete template ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u7cfb\u7edf\u6a21\u677f",content:"\u662f\u5426\u5220\u9664\u7cfb\u7edf\u6a21\u677f ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function or(e){var a=e.lang,t=e.templateID,o=e.open,l=e.onSuccess,r=e.onCancel,c=s.a.useState(!0),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(""),p=Object(n.a)(d,2),f=p[0],b=p[1],g=nr[a],E=g.title,h=function(e){m(!0),b(e)},v=function(e){m(!0),b(""),l(t)},y=g.content+t,x=[{color:"transparent",label:g.cancel,onClick:function(){b(""),r()}},{color:"info",label:g.confirm,onClick:function(){m(!1),function(e,a,t){Za("/templates/"+e,(function(){a(e)}),t)}(t,v,h)}}];return s.a.createElement(oa,{size:"xs",open:o,prompt:f,title:E,buttons:x,content:y,operatable:u})}var lr={en:{title:"Create System Template",name:"Name",admin:"Admin Name",operatingSystem:"Operating System",disk:"Disk Driver",network:"Network Interface Model",display:"Display Driver",control:"Remote Control Protocol",usb:"USB Model",tablet:"Tablet Mode",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u521b\u5efa\u7cfb\u7edf\u6a21\u677f",name:"\u6a21\u677f\u540d",admin:"\u7ba1\u7406\u5458\u540d\u79f0",operatingSystem:"\u64cd\u4f5c\u7cfb\u7edf",disk:"\u78c1\u76d8\u9a71\u52a8",network:"\u7f51\u5361\u578b\u53f7",display:"\u663e\u5361\u7c7b\u578b",control:"\u8fdc\u7a0b\u7ba1\u7406\u534f\u8bae",usb:"USB\u63a5\u53e3",tablet:"\u89e6\u6478\u5c4f\u63a5\u53e3",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}},rr=["linux","windows"],cr=["scsi","sata","ide"],ir=["virtio","e1000","rtl8139"],sr=["vga","cirrus"],ur=["vnc","spice"],mr=["","nec-xhci"],dr=["","usb"];function pr(e){var a={name:"",admin:"",operating_system:"",disk:"",network:"",display:"",control:"",usb:"",tablet:""},t=e.lang,o=e.open,l=e.onSuccess,r=e.onCancel,c=s.a.useState(!0),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(""),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(a),h=Object(n.a)(g,2),v=h[0],y=h[1],x=lr[t],k=x.title,C=function(e){m(!0),b(e)},S=function(){b(""),y(a)},O=function(e){m(!0),S(),l(e)},j=function(e){return function(a){var t=a.target.value;y((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}},w=function(e){return e.map((function(e){return{value:e,label:e||"none"}}))},_=[{type:"text",label:x.name,onChange:j("name"),value:v.name,required:!0,oneRow:!0,xs:10},{type:"text",label:x.admin,onChange:j("admin"),value:v.admin,required:!0,oneRow:!0,xs:8,sm:6},{type:"select",label:x.operatingSystem,onChange:j("operating_system"),value:v.operating_system,required:!0,oneRow:!0,options:w(rr),xs:12,sm:5},{type:"select",label:x.disk,onChange:j("disk"),value:v.disk,required:!0,oneRow:!0,options:w(cr),xs:6,sm:4},{type:"select",label:x.network,onChange:j("network"),value:v.network,required:!0,oneRow:!0,options:w(ir),xs:12,sm:5},{type:"select",label:x.display,onChange:j("display"),value:v.display,required:!0,oneRow:!0,options:w(sr),xs:6,sm:4},{type:"select",label:x.control,onChange:j("control"),value:v.control,required:!0,oneRow:!0,options:w(ur),xs:6,sm:4},{type:"select",label:x.usb,onChange:j("usb"),value:v.usb,required:!0,oneRow:!0,options:w(mr),xs:8,sm:4},{type:"select",label:x.tablet,onChange:j("tablet"),value:v.tablet,required:!0,oneRow:!0,options:w(dr),xs:8,sm:4}],N=s.a.createElement(ze,{inputs:_}),R=[{color:"transparent",label:x.cancel,onClick:function(){S(),r()}},{color:"info",label:x.confirm,onClick:function(){v.name?v.admin?(b(""),m(!1),function(e,a,t,n,o,l,r,c,i,s,u){var m={name:e,admin:a,operating_system:t,disk:n,network:o,display:l,control:r};c&&(m.usb=c),i&&(m.tablet=i),Ua("/templates/",m,(function(e){var a=e.id;s(a)}),u)}(v.name,v.admin,v.operating_system,v.disk,v.network,v.display,v.control,v.usb,v.tablet,O,C)):C("must specify admin name"):C("must specify template name")}}];return s.a.createElement(oa,{size:"xs",open:o,prompt:f,title:k,buttons:R,content:N,operatable:u})}var fr={en:{title:"Modify System Template",name:"Name",admin:"Admin Name",operatingSystem:"Operating System",disk:"Disk Driver",network:"Network Interface Model",display:"Display Driver",control:"Remote Control Protocol",usb:"USB Model",tablet:"Tablet Mode",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u7cfb\u7edf\u6a21\u677f",name:"\u6a21\u677f\u540d",admin:"\u7ba1\u7406\u5458\u540d\u79f0",operatingSystem:"\u64cd\u4f5c\u7cfb\u7edf",disk:"\u78c1\u76d8\u9a71\u52a8",network:"\u7f51\u5361\u578b\u53f7",display:"\u663e\u5361\u7c7b\u578b",control:"\u8fdc\u7a0b\u7ba1\u7406\u534f\u8bae",usb:"USB\u63a5\u53e3",tablet:"\u89e6\u6478\u5c4f\u63a5\u53e3",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}},br=["linux","windows"],gr=["scsi","sata","ide"],Er=["virtio","e1000","rtl8139"],hr=["vga","cirrus"],vr=["vnc","spice"],yr=["","nec-xhci"],xr=["","usb"];function kr(e){var a={name:"",admin:"",operating_system:"",disk:"",network:"",display:"",control:"",usb:"",tablet:""},t=e.lang,o=e.templateID,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!1),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!0),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(""),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(!1),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState(a),w=Object(n.a)(j,2),_=w[0],N=w[1],R=fr[t],T=R.title,I=s.a.useCallback((function(e){S&&(g(!0),x(e))}),[S]),D=function(){x(""),N(a),d(!1)},A=function(e){S&&(g(!0),D(),r(e))},P=function(e){return function(a){if(S){var t=a.target.value;N((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}}},F=function(e){return e.map((function(e){return{value:e,label:e||"none"}}))};s.a.useEffect((function(){if(o&&l){O(!0);return function(e,a,t){Ha("/templates/"+e,a,t)}(o,(function(e){S&&(N(e),d(!0))}),I),function(){O(!1)}}}),[S,l,o,I]);var B,M=[{color:"transparent",label:R.cancel,onClick:function(){D(),c()}}];if(m){var z=[{type:"text",label:R.name,onChange:P("name"),value:_.name,required:!0,oneRow:!0,xs:10},{type:"text",label:R.admin,onChange:P("admin"),value:_.admin,required:!0,oneRow:!0,xs:8,sm:6},{type:"select",label:R.operatingSystem,onChange:P("operating_system"),value:_.operating_system,required:!0,oneRow:!0,options:F(br),xs:12,sm:5},{type:"select",label:R.disk,onChange:P("disk"),value:_.disk,required:!0,oneRow:!0,options:F(gr),xs:6,sm:4},{type:"select",label:R.network,onChange:P("network"),value:_.network,required:!0,oneRow:!0,options:F(Er),xs:12,sm:5},{type:"select",label:R.display,onChange:P("display"),value:_.display,required:!0,oneRow:!0,options:F(hr),xs:6,sm:4},{type:"select",label:R.control,onChange:P("control"),value:_.control,required:!0,oneRow:!0,options:F(vr),xs:6,sm:4},{type:"select",label:R.usb,onChange:P("usb"),value:_.usb,required:!0,oneRow:!0,options:F(yr),xs:8,sm:4},{type:"select",label:R.tablet,onChange:P("tablet"),value:_.tablet,required:!0,oneRow:!0,options:F(xr),xs:8,sm:4}];B=s.a.createElement(ze,{inputs:z}),M.push({color:"info",label:R.confirm,onClick:function(){if(_.name)if(_.admin){x(""),g(!1);var e=_.name,a=_.admin,t=_.operating_system,n=_.disk,l=_.network,r=_.display,c=_.control,i=_.usb,s=_.tablet;!function(e,a,t,n,o,l,r,c,i,s,u,m){var d="/templates/"+e,p={name:a,admin:t,operating_system:n,disk:o,network:l,display:r,control:c};i&&(p.usb=i),s&&(p.tablet=s),$a(d,p,(function(){u(e)}),m)}(o,e,a,t,n,l,r,c,i,s,A,I)}else I("must specify admin name");else I("must specify template name")}})}else B=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"xs",open:l,prompt:y,title:T,buttons:M,content:B,operatable:b})}var Cr=Object(g.a)({cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),Sr={en:{createButton:"Create System Template",tableTitle:"System Templates",name:"Name",os:"Operating System",createdTime:"Created Time",modifiedTime:"Last Modified",operates:"Operates",noResource:"No system templates available",detail:"Detail",delete:"Delete"},cn:{createButton:"\u521b\u5efa\u65b0\u6a21\u677f",tableTitle:"\u7cfb\u7edf\u6a21\u677f",name:"\u540d\u79f0",os:"\u64cd\u4f5c\u7cfb\u7edf",createdTime:"\u521b\u5efa\u65f6\u95f4",modifiedTime:"\u6700\u540e\u4fee\u6539",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u7cfb\u7edf\u6a21\u677f",detail:"\u8be6\u60c5",delete:"\u5220\u9664"}};var Or=t(21),jr=t(96),wr=t.n(jr),_r=t(198),Nr=t.n(_r),Rr=t(120),Tr=t.n(Rr),Ir=Object(g.a)(Cn);function Dr(e){var a=Ir(),t=e.color,n=e.headers,o=e.rows;return s.a.createElement("div",{className:a.tableResponsive},s.a.createElement(hn.a,{className:a.table},s.a.createElement(vn.a,{className:a[t+"TableHeader"]},s.a.createElement(yn.a,{className:a.tableHeadRow},n.map((function(e,t){return s.a.createElement(kn.a,{className:a.tableCell+" "+a.tableHeadCell,key:t},e)})))),s.a.createElement(xn.a,null,o)))}Dr.defaultProps={color:"gray"};var Ar=t(134),Pr=t.n(Ar),Fr=t(195),Br=t.n(Fr),Mr=t(294),zr=t.n(Mr),Wr=t(189),qr=t.n(Wr),Hr=t(296),Lr=t.n(Hr),Ur=t(297),Vr=t.n(Ur),Gr=t(295),$r=t.n(Gr),Zr=t(190),Yr=t.n(Zr),Jr=t(298),Qr=t.n(Jr),Kr=t(191),Xr=t.n(Kr),ec=t(192),ac=t.n(ec),tc=t(193),nc=t.n(tc),oc=t(194),lc=t.n(oc),rc=t(135),cc=t.n(rc),ic=t(119),sc=t.n(ic),uc={en:{running:"Running",stopped:"Stopped",start:"Start Instance",startWithMedia:"Start Instance With Media",snapshot:"Snapshot",createImage:"Create Disk Image",resetSystem:"Reset System",delete:"Delete Instance",migrate:"Migrate Instance",monitor:"Monitor Resource Usage",detail:"Instance Detail",security:"Security Policies",remoteControl:"Remote Control",stop:"Stop Instance",forceStop:"Force Stop Instance",reboot:"Reboot Instance",reset:"Reset Instance",insertMedia:"Insert Media",ejectMedia:"Eject Media",autoStartup:"Auto Startup",mediaAttached:"Media Attached"},cn:{running:"\u8fd0\u884c\u4e2d",stopped:"\u5df2\u505c\u6b62",start:"\u542f\u52a8\u4e91\u4e3b\u673a",startWithMedia:"\u4ece\u5149\u76d8\u955c\u50cf\u542f\u52a8\u4e91\u4e3b\u673a",snapshot:"\u5feb\u7167",createImage:"\u521b\u5efa\u78c1\u76d8\u955c\u50cf",resetSystem:"\u91cd\u7f6e\u7cfb\u7edf",delete:"\u5220\u9664\u4e91\u4e3b\u673a",migrate:"\u8fc1\u79fb\u4e91\u4e3b\u673a",monitor:"\u76d1\u63a7\u8d44\u6e90\u7528\u91cf",detail:"\u5b9e\u4f8b\u8be6\u60c5",security:"\u5b89\u5168\u7b56\u7565",remoteControl:"\u8fdc\u7a0b\u76d1\u63a7",stop:"\u505c\u6b62\u4e91\u4e3b\u673a",forceStop:"\u5f3a\u5236\u7ec8\u6b62\u4e91\u4e3b\u673a",reboot:"\u91cd\u542f\u4e91\u4e3b\u673a",reset:"\u5f3a\u5236\u91cd\u542f\u4e91\u4e3b\u673a",insertMedia:"\u63d2\u5165\u5149\u76d8\u955c\u50cf",ejectMedia:"\u5f39\u51fa\u5149\u76d8\u955c\u50cf",autoStartup:"\u5f00\u673a\u542f\u52a8",mediaAttached:"\u5a92\u4f53\u5df2\u52a0\u8f7d"}};function mc(e){var a,t=Object(g.a)(Cn)(),n=Object(g.a)(U)(),o=e.lang,l=e.instance,r=e.onNotify,c=e.onError,i=e.onDelete,u=e.onStatusChange,m=e.onMediaStart,d=e.onInsertMedia,p=e.onResetSystem,f=e.onBuildImage,b=e.onMigrateInstance,E=e.checked,h=e.checkable,v=e.onCheckStatusChanged,y=uc[o],x={tips:y.start,icon:Pr.a,handler:function(e){!function(e,a,t){Ua("/instances/"+e,{},(function(){a(e)}),t)}(e,(function(e){r("instance "+e+" started"),u()}),(function(a){c("start instance "+e+" fail: "+a)}))}},k={tips:y.startWithMedia,icon:zr.a,handler:m},C={tips:y.snapshot,icon:qr.a,href:"/admin/instances/snapshots/"+l.id},S={tips:y.createImage,icon:$r.a,handler:f},O={tips:y.resetSystem,icon:Lr.a,handler:p},j={tips:y.delete,icon:Wt.a,handler:i},w={tips:y.migrate,icon:Ut.a,handler:function(e){b(e,l.pool,l.cell)}},_={tips:y.monitor,icon:Vr.a,href:"/admin/instances/status/"+l.id,target:"_blank"},N={tips:y.detail,icon:Ht.a,href:"/admin/instances/details/"+l.id,target:"_blank"},R={tips:y.security,icon:Yr.a,href:"/admin/instances/policies/"+l.id},T={tips:y.remoteControl,icon:Qr.a,href:"/monitor/"+l.id,target:"_blank"},I={tips:y.stop,icon:wr.a,handler:function(e){Sa(e,(function(e){r("instance "+e+" stopped"),u()}),(function(a){c("stop instance "+e+" fail: "+a)}))}},D={tips:y.forceStop,icon:In.a,handler:function(e){!function(e,a,t){Ja("/instances/"+e,{reboot:!1,force:!0},(function(){a(e)}),t)}(e,(function(e){r("instance "+e+" force stopped"),u()}),(function(a){c("force stop instance "+e+" fail: "+a)}))}},A={tips:y.reboot,icon:Xr.a,handler:function(e){Oa(e,(function(e){r("instance "+e+" reboot"),u()}),(function(a){c("reboot instance "+e+" fail: "+a)}))}},P={tips:y.reset,icon:ac.a,handler:function(e){ja(e,(function(e){r("instance "+e+" reset"),u()}),(function(a){c("reset instance "+e+" fail: "+a)}))}},F={tips:y.insertMedia,icon:nc.a,handler:d},B={tips:y.ejectMedia,icon:lc.a,handler:function(e){Ca(e,(function(e){r("media ejected from instance "+e),u()}),(function(a){c("eject media from instance "+e+" fail: "+a)}))}},M=[];l.running?(a=[s.a.createElement(te.a,{title:y.running,placement:"top",key:y.running},s.a.createElement(Pr.a,{className:n.successText}))],M=[T,I,D,A,P],l.auto_start&&a.push(s.a.createElement(te.a,{title:y.autoStartup,placement:"top",key:y.autoStartup},s.a.createElement(cc.a,{className:n.infoText}))),l.media_attached?(a.push(s.a.createElement(te.a,{title:y.mediaAttached,placement:"top",key:y.mediaAttached},s.a.createElement(sc.a,{className:n.infoText}))),M.push(B)):M.push(F),M=M.concat([_,N,R])):(a=[s.a.createElement(te.a,{title:y.stopped,placement:"top",key:y.stopped},s.a.createElement(Br.a,{className:n.dangerText}))],l.auto_start&&a.push(s.a.createElement(te.a,{title:y.autoStartup,placement:"top",key:y.autoStartup},s.a.createElement(cc.a,{className:n.infoText}))),M=[x,k,C,S,O,j,w,_,N,R]);var z="";l.internal&&l.internal.network_address&&(z=l.internal.network_address),l.external&&l.external.network_address&&(z+="/"+l.external.network_address);var W;W=l.memory>=1<<30?l.memory/(1<<30)+" GB":l.memory/(1<<20)+" MB";var q=[];l.disks.forEach((function(e){q.push((e/(1<<30)).toFixed(2).toString())}));var H,L=q.join("/")+" GB";return H=h?s.a.createElement(K.a,{display:"flex",alignItems:"center"},s.a.createElement(K.a,null,s.a.createElement(Pe.a,{checked:E,onChange:function(e){return function(e){var a=e.target.checked;v(a,l.id)}(e)},value:l.id})),s.a.createElement(K.a,null,l.name)):l.name,s.a.createElement(yn.a,{className:t.tableBodyRow},s.a.createElement(kn.a,{className:t.tableCell},H),s.a.createElement(kn.a,{className:t.tableCell},l.host?l.host:l.cell),s.a.createElement(kn.a,{className:t.tableCell},z),s.a.createElement(kn.a,{className:t.tableCell},l.cores),s.a.createElement(kn.a,{className:t.tableCell},W),s.a.createElement(kn.a,{className:t.tableCell},L),s.a.createElement(kn.a,{className:t.tableCell},a),s.a.createElement(kn.a,{className:t.tableCell},M.map((function(e,a){var t,n=s.a.createElement(e.icon);return t=e.href?s.a.createElement(yt.a,{to:e.href,target:e.target},s.a.createElement(ne.a,{color:"inherit"},n)):s.a.createElement(ne.a,{color:"inherit",onClick:function(){null!==e.handler&&e.handler(l.id)}},n),s.a.createElement(te.a,{title:e.tips,placement:"top",key:a},t)}))))}var dc={en:{title:"Delete Instance",content:"Are you sure to delete instance ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u4e91\u4e3b\u673a",content:"\u662f\u5426\u5220\u9664\u4e91\u4e3b\u673a ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function pc(e){var a=e.lang,t=e.instanceID,o=e.open,l=e.onSuccess,r=e.onCancel,c=s.a.useState(!0),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(""),p=Object(n.a)(d,2),f=p[0],b=p[1],g=dc[a],E=g.title,h=g.content+t,v=function(e){m(!0),b(e)},y=function(){m(!0),b(""),l(t)},x=[{color:"transparent",label:g.cancel,onClick:function(){b(""),r()}},{color:"info",label:g.confirm,onClick:function(){m(!1),function(e,a,t){Ja("/guests/"+e,{force:!1},a,t)}(t,y,v)}}];return s.a.createElement(oa,{size:"sm",open:o,prompt:f,title:E,buttons:x,content:h,operatable:u})}var fc=t(620),bc=t(599),gc=t(600),Ec=t(197),hc=t.n(Ec),vc=["children"];function yc(e){var a=e.children,t=Object($.a)(e,vc);return s.a.createElement(De.a,{item:!0,xs:12},s.a.createElement(K.a,{m:1,p:0},s.a.createElement(De.a,Object.assign({container:!0},t),a)))}var xc={en:{title:"Create Instance",name:"Instance Name",resourcePool:"Resource Pool",core:"Core",memory:"Memory",systemDisk:"System Disk Size",dataDisk:"Data Disk Size",autoStartup:"Automatic Startup",systemVersion:"System Version",sourceImage:"Source Image",blankSystem:"Blank System",qos:"QoS Options (Optional)",cpuPriority:"CPU Priority",iops:"IOPS",outbound:"Outband Bandwidth",inbound:"Inbound Bandwidth",noDataDisk:"Don't use data disk",noLimit:"No Limit",cpuPriorityHigh:"High",cpuPriorityMedium:"Medium",cpuPriorityLow:"Low",modules:"Pre-Installed Modules",adminName:"Admin Name",adminPassword:"Admin Password",blankHelper:"Leave blank to generate",dataPath:"Data Path",off:"Off",on:"On",ciOptions:"Cloud Init Options",securityPolicy:"Security Policy",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u521b\u5efa\u4e91\u4e3b\u673a",name:"\u4e91\u4e3b\u673a\u540d\u79f0",resourcePool:"\u8ba1\u7b97\u8d44\u6e90\u6c60",core:"\u6838\u5fc3\u6570",memory:"\u5185\u5b58",systemDisk:"\u7cfb\u7edf\u78c1\u76d8\u5bb9\u91cf",dataDisk:"\u6570\u636e\u78c1\u76d8\u5bb9\u91cf",autoStartup:"\u81ea\u52a8\u542f\u52a8",systemVersion:"\u7cfb\u7edf\u7248\u672c",sourceImage:"\u6765\u6e90\u955c\u50cf",blankSystem:"\u7a7a\u767d\u7cfb\u7edf",qos:"QoS\u9009\u9879 (\u53ef\u9009)",cpuPriority:"CPU\u4f18\u5148\u7ea7",iops:"\u78c1\u76d8\u8bfb\u5199\u9650\u5236",outbound:"\u4e0a\u884c\u5e26\u5bbd",inbound:"\u4e0b\u884c\u5e26\u5bbd",noDataDisk:"\u4e0d\u4f7f\u7528\u6570\u636e\u78c1\u76d8",noLimit:"\u65e0\u9650\u5236",cpuPriorityHigh:"\u9ad8",cpuPriorityMedium:"\u4e2d",cpuPriorityLow:"\u4f4e",modules:"\u9884\u88c5\u6a21\u5757",adminName:"\u7ba1\u7406\u5458\u8d26\u53f7",adminPassword:"\u7ba1\u7406\u5458\u5bc6\u7801",blankHelper:"\u7559\u7a7a\u5219\u81ea\u52a8\u751f\u6210\u65b0\u5bc6\u7801",dataPath:"\u6302\u8f7d\u6570\u636e\u8def\u5f84",off:"\u5173\u95ed",on:"\u5f00\u542f",ciOptions:"Cloud Init\u9009\u9879",securityPolicy:"\u5b89\u5168\u7b56\u7565",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function kc(e){var a=e.lang,t=e.open,o=e.onSuccess,l=e.onCancel,r={name:"",pool:"",cores:1..toString(),memory:(1<<30).toString(),system_disk:5,data_disk:0,auto_start:!1,system_template:"",from_image:"__default",security_policy:"",modules:new Map,module_cloud_init_admin_name:"root",module_cloud_init_admin_password:"",module_cloud_init_data_path:"/opt/data",priority:"medium",iops:0,inbound:0,outbound:0},c=s.a.useState(!1),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(!1),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(0),h=Object(n.a)(g,2),v=h[0],y=h[1],x=s.a.useState(!0),k=Object(n.a)(x,2),C=k[0],S=k[1],O=s.a.useState(""),j=Object(n.a)(O,2),w=j[0],_=j[1],N=s.a.useState(!1),R=Object(n.a)(N,2),T=R[0],I=R[1],D=s.a.useState(r),A=Object(n.a)(D,2),P=A[0],F=A[1],B=s.a.useState({pools:[],images:[],versions:[],policies:[]}),M=Object(n.a)(B,2),z=M[0],W=M[1],q=xc[a],H=q.title,L=s.a.useCallback((function(e){T&&(S(!0),_(e))}),[T]),U=function(){_(""),F(r),m(!1),b(!1),y(0)},V=function(e){T&&(S(!0),U(),o(e.id))},G=function(e){T&&(b(!0),y(0),setTimeout((function(){$(e)}),1e3))},$=function e(a){if(T){xa(a,V,L,(function(t){T&&(y(t),setTimeout((function(){e(a)}),1e3))}))}},Z=function(e){return function(a){if(T){var t=a.target.value;F((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}}},J=function(e){return function(a,t){T&&F((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}};s.a.useEffect((function(){if(t){var e=[],a=[{label:q.blankSystem,value:"__default"}],n=[],o=[];I(!0);var l=function(t){T&&(t.forEach((function(e){var a=e.id,t=e.name;o.push({label:t,value:a})})),W({pools:e,images:a,versions:n,policies:o}),m(!0))},r=function(e){T&&(e.forEach((function(e){var a=e.id,t=e.name;n.push({label:t,value:a})})),Ma("","",!0,!0,l,L))},c=function(e){T&&(e.forEach((function(e){var t=e.name,n=e.id;a.push({label:t,value:n})})),Ba(r,L))};return fa((function(a){T&&(a.forEach((function(a){var t=a.name;e.push({label:t,value:t})})),Ra(c,L))}),L),function(){I(!1)}}}),[T,t,q.blankSystem,L]);var Q,X,ee=[{color:"transparent",label:q.cancel,onClick:function(){U(),l()}}];if(u)if(f)Q=s.a.createElement(De.a,{container:!0},s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12},s.a.createElement(Fl.a,{variant:"determinate",value:v}))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12},s.a.createElement(oe.a,{align:"center"},v.toFixed(2)+"%"))));else{var ae=[];[1,2,4,8,16].forEach((function(e){ae.push({label:e.toString(),value:e.toString()})}));var te,ne=[];[1,2,4,8,16,32].forEach((function(e){var a,t=512*e*(1<<20);a=t>=1<<30?t/(1<<30)+" GB":t/(1<<20)+" MB",ne.push({label:a,value:t.toString()})}));var le,re=[];[5,60,30].forEach((function(e){re.push({value:e,label:e+" GB"})})),te={type:"slider",label:q.systemDisk,onChange:J("system_disk"),value:P.system_disk,oneRow:!0,maxStep:60,minStep:5,step:1,marks:re,xs:12,sm:6,md:4};var ce,ie=[{value:0,label:q.noDataDisk},{value:10,label:"10 GB"},{value:20,label:"20 GB"}];if(le={type:"slider",label:q.dataDisk,onChange:J("data_disk"),value:P.data_disk,oneRow:!0,maxStep:20,minStep:0,step:2,marks:ie,xs:12,sm:6,md:4},P.system_template&&"__default"!==P.from_image){var se,ue=[{value:"qemu",label:"QEMU-Guest-Agent"},{value:"cloud-init",label:"CloudInit"}];if(P.modules.get("cloud-init")){var me=[{type:"text",label:q.adminName,onChange:Z("module_cloud_init_admin_name"),value:P.module_cloud_init_admin_name,oneRow:!0,xs:12},{type:"text",label:q.adminPassword,onChange:Z("module_cloud_init_admin_password"),helper:q.blankHelper,oneRow:!0,xs:12},{type:"text",label:q.dataPath,onChange:Z("module_cloud_init_data_path"),value:P.module_cloud_init_data_path,disabled:!0,oneRow:!0,xs:12}];se=s.a.createElement(Qe,{xs:12,sm:8,md:6},s.a.createElement(Ie.a,{component:"legend"},q.ciOptions),s.a.createElement(ze,{inputs:me}))}else se=s.a.createElement(Qe,null);ce=s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(Te.a,{component:"fieldset",fullWidth:!0},s.a.createElement(Ie.a,{component:"legend"},q.modules),s.a.createElement(Ae.a,null,s.a.createElement(De.a,{container:!0},ue.map((function(e){var a,t,n;return a=!!P.modules.has(e.value)&&P.modules.get(e.value),s.a.createElement(Qe,{xs:12,sm:6,md:4,key:e.value},s.a.createElement(Re.a,{control:s.a.createElement(Pe.a,{checked:a,onChange:(t="modules",n=e.value,function(e){if(T){var a=e.target.checked;F((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},t,e[t].set(n,a)))}))}}),value:e.value}),label:e.label}))}))))))),se)}else ce=s.a.createElement(Qe,null);var de=[{type:"text",label:q.name,onChange:Z("name"),value:P.name,oneRow:!0,required:!0,xs:12,sm:6,md:4},{type:"select",label:q.resourcePool,onChange:Z("pool"),value:P.pool,oneRow:!0,options:z.pools,required:!0,xs:10,sm:4,md:3},{type:"radio",label:q.core,onChange:Z("cores"),value:P.cores,oneRow:!0,options:ae,required:!0,xs:12},{type:"radio",label:q.memory,onChange:Z("memory"),value:P.memory,oneRow:!0,options:ne,required:!0,xs:12},te,le,{type:"select",label:q.sourceImage,onChange:Z("from_image"),value:P.from_image,oneRow:!0,options:z.images,xs:10,sm:6,md:5},{type:"select",label:q.systemVersion,onChange:Z("system_template"),value:P.system_template,oneRow:!0,options:z.versions,xs:10,sm:5,md:4},{type:"switch",label:q.autoStartup,onChange:(X="auto_start",function(e){if(T){var a=e.target.checked;F((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},X,a))}))}}),value:P.auto_start,on:q.on,off:q.off,oneRow:!0,xs:8,sm:6,md:4}];z.policies&&0!==z.policies.length&&de.push({type:"select",label:q.securityPolicy,onChange:Z("security_policy"),value:P.security_policy,oneRow:!0,options:z.policies,xs:10,sm:5,md:4});var pe=[{value:"high",label:q.cpuPriorityHigh},{value:"medium",label:q.cpuPriorityMedium},{value:"low",label:q.cpuPriorityLow}],fe=[{type:"radio",label:q.cpuPriority,onChange:Z("priority"),value:P.priority,oneRow:!0,options:pe,xs:12},{type:"slider",label:q.iops,onChange:J("iops"),value:P.iops,oneRow:!0,maxStep:2e3,minStep:0,step:10,marks:[{value:0,label:q.noLimit},{value:2e3,label:"2000"}],xs:12},{type:"slider",label:q.inbound,onChange:J("inbound"),value:P.inbound,oneRow:!0,maxStep:20,minStep:0,step:2,marks:[{value:0,label:q.noLimit},{value:20,label:"20 Mbit/s"}],xs:12},{type:"slider",label:q.outbound,onChange:J("outbound"),value:P.outbound,oneRow:!0,maxStep:20,minStep:0,step:2,marks:[{value:0,label:q.noLimit},{value:20,label:"20 Mbit/s"}],xs:12}];Q=s.a.createElement(De.a,{container:!0},s.a.createElement(K.a,{m:1,pt:2},s.a.createElement(ze,{inputs:de})),ce,s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:9,md:7},s.a.createElement(K.a,{m:0,pb:2},s.a.createElement(fc.a,null,s.a.createElement(bc.a,{expandIcon:s.a.createElement(hc.a,null)},q.qos),s.a.createElement(gc.a,null,s.a.createElement(K.a,{m:1,pt:2},s.a.createElement(ze,{inputs:fe})))))))),ee.push({color:"info",label:q.confirm,onClick:function(){if(_(""),S(!1),P.name)if(P.pool){var e=Number(P.cores);if(Number.isNaN(e))L("invalid cores: "+P.cores);else{var a=Number(P.memory);if(Number.isNaN(a))L("invalid memory: "+P.memory);else{var t=[P.system_disk*(1<<30)];0!==P.data_disk&&t.push(P.data_disk*(1<<30));var n,o=P.system_template;n="__default"===P.from_image?"":P.from_image;var l=[],r=!1;P.modules.forEach((function(e,a){e&&(l.push(a),"cloud-init"===a&&(r=!0))}));var c=null;r&&(c={admin_name:P.module_cloud_init_admin_name,admin_secret:P.module_cloud_init_admin_password,data_path:P.module_cloud_init_data_path});var i={cpu_priority:P.priority,write_iops:P.iops,read_iops:P.iops,receive_speed:P.inbound*(1<<17),send_speed:P.outbound*(1<<17)};!function(e,a,t,n,o,l,r,c,i,s,u,m,d,p,f){var b=ca();if(null!==b){var g={name:e,owner:b.user,group:b.group,pool:a,cores:t,memory:n,disks:o,auto_start:l,from_image:r,template:c};i&&(g.modules=i),s&&(g.cloud_init=s),u&&(g.qos=u),m&&(g.security_policy_group=m);Va("/guests/",g,(function(e,a){202===e?d(a.id):200===e?p(a.id):f("unexpected status "+e.toString())}),f)}else f("session expired")}(P.name,P.pool,e,a,t,P.auto_start,n,o,l,c,i,P.security_policy,G,V,L)}}}else L("must specify target pool");else L("instance name required")}})}else Q=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"md",open:t,prompt:w,promptPosition:"top",hideBackdrop:!0,title:H,buttons:ee,content:Q,operatable:C})}var Cc={en:{title:"Start Instance With Media",name:"Media Name",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4ece\u5f15\u5bfc\u5149\u76d8\u542f\u52a8\u4e91\u4e3b\u673a",name:"\u955c\u50cf\u540d\u79f0",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function Sc(e){var a={image:""},t=e.lang,o=e.instanceID,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!1),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!0),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(""),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(!1),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState(a),w=Object(n.a)(j,2),_=w[0],N=w[1],R=s.a.useState({images:[]}),T=Object(n.a)(R,2),I=T[0],D=T[1],A=Cc[t],P=A.title,F=s.a.useCallback((function(e){S&&(g(!0),x(e))}),[S]),B=function(){x(""),N(a),d(!1)},M=function(e){S&&(g(!0),B(),r(e))};s.a.useEffect((function(){if(l){O(!0);return _a((function(e){if(S){var a=[];e.forEach((function(e){a.push({value:e.id,label:e.name})})),D({images:a}),d(!0)}}),F),function(){return O(!1)}}}),[S,l,F]);var z,W,q=[{color:"transparent",label:A.cancel,onClick:function(){B(),c()}}];if(m){var H=[{type:"select",label:A.name,onChange:(W="image",function(e){if(S){var a=e.target.value;N((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},W,a))}))}}),value:_.image,options:I.images,required:!0,oneRow:!0,xs:12,sm:10}];z=s.a.createElement(ze,{inputs:H}),q.push({color:"info",label:A.confirm,onClick:function(){x(""),g(!1);var e=_.image;""!==e?function(e,a,t,n){Ua("/instances/"+e,{from_media:!0,source:a},(function(){t(e)}),n)}(o,e,M,F):F("select a media image")}})}else z=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"xs",open:l,prompt:y,title:P,buttons:q,content:z,operatable:b})}var Oc={en:{title:"Insert Media Into Instance",name:"Media Name",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5411\u4e91\u4e3b\u673a\u63d2\u5165\u5149\u76d8\u955c\u50cf",name:"\u955c\u50cf\u540d\u79f0",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function jc(e){var a={image:""},t=e.lang,o=e.instanceID,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!1),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!0),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(""),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(!1),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState(a),w=Object(n.a)(j,2),_=w[0],N=w[1],R=s.a.useState({images:[]}),T=Object(n.a)(R,2),I=T[0],D=T[1],A=Oc[t],P=A.title,F=s.a.useCallback((function(e){S&&(g(!0),x(e))}),[S]),B=function(){x(""),N(a),d(!1)},M=function(e){S&&(g(!0),B(),r(e))};s.a.useEffect((function(){if(l){O(!0);return _a((function(e){if(S){var a=[];e.forEach((function(e){a.push({value:e.id,label:e.name})})),D({images:a}),d(!0)}}),F),function(){return O(!1)}}}),[S,l,F]);var z,W,q=[{color:"transparent",label:A.cancel,onClick:function(){B(),c()}}];if(m){var H=[{type:"select",label:A.name,onChange:(W="image",function(e){if(S){var a=e.target.value;N((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},W,a))}))}}),value:_.image,options:I.images,required:!0,oneRow:!0,xs:12,sm:10}];z=s.a.createElement(ze,{inputs:H}),q.push({color:"info",label:A.confirm,onClick:function(){x(""),g(!1);var e=_.image;""!==e?function(e,a,t,n){Ua("/instances/"+e+"/media",{source:a},(function(){t(e)}),n)}(o,e,M,F):F("select a media image")}})}else z=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"xs",open:l,prompt:y,title:P,buttons:q,content:z,operatable:b})}var wc={en:{title:"Build New Image",name:"Image Name",description:"Description",tags:"Tags",cancel:"Cancel",confirm:"Build"},cn:{title:"\u6784\u5efa\u65b0\u955c\u50cf",name:"\u955c\u50cf\u540d\u79f0",description:"\u63cf\u8ff0",tags:"\u6807\u7b7e",cancel:"\u53d6\u6d88",confirm:"\u6784\u5efa"}};function _c(e){var a={name:"",description:"",tags:new Map},t=e.lang,o=e.instanceID,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!1),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!1),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(0),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(!0),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState(""),w=Object(n.a)(j,2),_=w[0],N=w[1],R=s.a.useState(!1),T=Object(n.a)(R,2),I=T[0],D=T[1],A=s.a.useState(a),P=Object(n.a)(A,2),F=P[0],B=P[1],M=s.a.useState({tags:[]}),z=Object(n.a)(M,2),W=z[0],q=z[1],H=wc[t],L=H.title,U=s.a.useCallback((function(e){I&&(O(!0),N(e))}),[I]),V=function(e){return function(a){Da(e),U(a)}},G=function(){N(""),B(a),d(!1),g(!1),x(0)},$=function e(a,t){return function(n){I&&(n.created?function(e){I&&(O(!0),G(),r(e))}(t):(x(n.progress),setTimeout((function(){Ta(a,e(a,t),V(a))}),1e3)))}},Z=function(e){return function(a){if(I){var t=a.target.value;B((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}}};s.a.useEffect((function(){if(l){D(!0);var e=[];return[["linux","Linux"],["windows","Windows"],["centos","Centos"],["ubuntu","Ubuntu"],["64bit","64Bit"],["32bit","32Bit"]].forEach((function(a){e.push({label:a[1],value:a[0]})})),q({tags:e}),d(!0),function(){D(!1)}}}),[l]);var J,Q=[{color:"transparent",label:H.cancel,onClick:function(){G(),c()}}];if(m)if(b)J=s.a.createElement(De.a,{container:!0},s.a.createElement(De.a,{item:!0,xs:12},s.a.createElement(Fl.a,{variant:"determinate",value:y})),s.a.createElement(De.a,{item:!0,xs:12},s.a.createElement(oe.a,{align:"center"},y.toFixed(2)+"%")));else{var K=[{type:"text",label:H.name,value:F.name,onChange:Z("name"),required:!0,oneRow:!0,xs:8},{type:"textarea",label:H.description,value:F.description,onChange:Z("description"),required:!0,oneRow:!0,rows:4,xs:12},{type:"checkbox",label:H.tags,onChange:function(e){return function(a){if(I){var t=a.target.checked;B((function(a){return Object(E.a)(Object(E.a)({},a),{},{tags:a.tags.set(e,t)})}))}}},value:F.tags,options:W.tags,required:!0,oneRow:!0,xs:10}];J=s.a.createElement(ze,{inputs:K}),Q.push({color:"info",label:H.confirm,onClick:function(){if(N(""),O(!1),F.name)if(F.description)if(F.tags){var e=[];if(F.tags.forEach((function(a,t){a&&e.push(t)})),0!==e.length){var a=F.name;Ia(a,o,F.description,e,function(e){return function(a){I&&(g(!0),setTimeout((function(){Ta(a,$(a,e),V(a))}),1e3))}}(a),U)}else U("image tags required")}else U("image tags required");else U("desciption required");else U("must specify image name")}})}else J=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:l,prompt:_,hideBackdrop:!0,title:L,buttons:Q,content:J,operatable:S})}var Nc={en:{title:"Reset Instance System",name:"Target System",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u91cd\u7f6e\u4e91\u4e3b\u673a\u7cfb\u7edf",name:"\u76ee\u6807\u7cfb\u7edf",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function Rc(e){var a={image:""},t=e.lang,o=e.instanceID,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!1),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!1),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(0),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(!0),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState(""),w=Object(n.a)(j,2),_=w[0],N=w[1],R=s.a.useState(!1),T=Object(n.a)(R,2),I=T[0],D=T[1],A=s.a.useState(a),P=Object(n.a)(A,2),F=P[0],B=P[1],M=s.a.useState({images:[]}),z=Object(n.a)(M,2),W=z[0],q=z[1],H=Nc[t],L=H.title,U=s.a.useCallback((function(e){I&&(O(!0),N(e))}),[I]),V=function(){N(""),B(a),d(!1)},G=function(e){I&&(O(!0),V(),r(e.id))},$=function(e){I&&(g(!0),x(0),setTimeout((function(){Z(e)}),1e3))},Z=function e(a){if(I){xa(a,G,U,(function(t){I&&(x(t),setTimeout((function(){e(a)}),1e3))}))}};s.a.useEffect((function(){if(l){D(!0);return Ra((function(e){if(I){var a=[];e.forEach((function(e){var t=e.id,n=e.name;a.push({value:t,label:n})})),q({images:a}),d(!0)}}),U),function(){D(!1)}}}),[l,I,U]);var J,Q,K=[{color:"transparent",label:H.cancel,onClick:function(){V(),c()}}];if(m)if(b)J=s.a.createElement(De.a,{container:!0},s.a.createElement(De.a,{item:!0,xs:12},s.a.createElement(Fl.a,{variant:"determinate",value:y})),s.a.createElement(De.a,{item:!0,xs:12},s.a.createElement(oe.a,{align:"center"},y.toFixed(2)+"%")));else{var X=[{type:"select",label:H.name,onChange:(Q="image",function(e){if(I){var a=e.target.value;B((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},Q,a))}))}}),value:F.image,options:W.images,required:!0,oneRow:!0,xs:12,sm:10}];J=s.a.createElement(ze,{inputs:X}),K.push({color:"info",label:H.confirm,onClick:function(){N(""),O(!1);var e=F.image;""!==e?function(e,a,t,n){$a("/guests/"+e+"/system/",{from_image:a},(function(){t(e)}),n)}(o,e,$,U):U("select a target system")}})}else J=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"xs",open:l,prompt:_,hideBackdrop:!0,title:L,buttons:K,content:J,operatable:S})}var Tc={en:{title:"Migrate Single Instance",sourcePool:"Source Pool",sourceCell:"Source Cell",targetCell:"Target Cell",offline:"Offline",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u8fc1\u79fb\u4e91\u4e3b\u673a\u5b9e\u4f8b",sourcePool:"\u6e90\u8d44\u6e90\u6c60",sourceCell:"\u6e90\u8282\u70b9",targetCell:"\u76ee\u6807\u8282\u70b9",offline:"\u79bb\u7ebf",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function Ic(e){var a={targetCell:""},t=e.lang,o=e.instanceID,l=e.open,r=e.sourcePool,c=e.sourceCell,i=e.onSuccess,u=e.onCancel,m=s.a.useState(!1),d=Object(n.a)(m,2),p=d[0],f=d[1],b=s.a.useState(!0),g=Object(n.a)(b,2),h=g[0],v=g[1],y=s.a.useState(""),x=Object(n.a)(y,2),k=x[0],C=x[1],S=s.a.useState(!1),O=Object(n.a)(S,2),j=O[0],w=O[1],_=s.a.useState(a),N=Object(n.a)(_,2),R=N[0],T=N[1],I=s.a.useState({cells:[]}),D=Object(n.a)(I,2),A=D[0],P=D[1],F=Tc[t],B=F.title,M=s.a.useCallback((function(e){j&&(v(!0),C(e))}),[j]),z=function(){C(""),T(a),f(!1)},W=function(e){j&&(v(!0),z(),i(e))};s.a.useEffect((function(){if(l){w(!0);return ba(r,(function(e){if(j){var a=[];e.forEach((function(e){var t;e.name!==c&&(t=e.alive?e.name+"("+e.address+")":e.name+"("+F.offline+")",a.push({label:t,value:e.name,disabled:!e.alive}))})),0!==a.length?(P({cells:a}),f(!0)):M("no target cell available")}}),M),function(){w(!1)}}}),[j,l,r,c,M,F.offline]);var q,H,L=[{color:"transparent",label:F.cancel,onClick:function(){z(),u()}}];if(p){var U=[{type:"text",label:F.sourcePool,value:r,disabled:!0,oneRow:!0,xs:12,sm:8},{type:"text",label:F.sourceCell,value:c,disabled:!0,oneRow:!0,xs:12,sm:8},{type:"select",label:F.targetCell,onChange:(H="targetCell",function(e){if(j){var a=e.target.value;T((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},H,a))}))}}),value:R.targetCell,options:A.cells,required:!0,oneRow:!0,xs:12,sm:10}];q=s.a.createElement(ze,{inputs:U}),L.push({color:"info",label:F.confirm,onClick:function(){var e=R.targetCell;""!==e?(C(""),v(!1),function(e,a,t,n,o,l){Ua("/migrations/",{source_pool:e,source_cell:a,target_cell:t,instances:[n]},(function(){o(n)}),l)}(r,c,e,o,W,M)):M("select a target cell")}})}else q=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"xs",open:l,prompt:k,title:B,buttons:L,content:q,operatable:h})}var Dc=t(601),Ac={en:{title:"Batch Stopping Instance",content1:"Are you sure to stop ",content2:" instance(s)",cancel:"Cancel",confirm:"Confirm",finish:"Finish",fail:"Fail",complete:"Stopped",processing:"Stopping",instance:"Instance",result:"Result"},cn:{title:"\u6279\u91cf\u505c\u6b62\u4e91\u4e3b\u673a",content1:"\u662f\u5426\u505c\u6b62 ",content2:" \u4e2a\u4e91\u4e3b\u673a",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a",finish:"\u5b8c\u6210",fail:"\u5931\u8d25",complete:"\u5df2\u505c\u6b62",processing:"\u505c\u6b62\u4e2d",instance:"\u4e91\u4e3b\u673a",result:"\u5904\u7406\u7ed3\u679c"}};function Pc(e){var a,t,o=0,l=1,r=2,c=e.lang,i=e.targets,u=e.open,m=e.onSuccess,d=e.onCancel,p=s.a.useState(o),f=Object(n.a)(p,2),b=f[0],g=f[1],E=s.a.useState(null),h=Object(n.a)(E,2),v=h[0],y=h[1],x=s.a.useState(!0),k=Object(n.a)(x,2),C=k[0],S=k[1],O=s.a.useState(""),j=Object(n.a)(O,2),w=j[0],_=j[1],N=s.a.useState(!1),R=Object(n.a)(N,2),T=R[0],I=R[1],D=Ac[c],A=D.title,P=s.a.useCallback((function(e){T&&(S(!0),_(e))}),[T]),F={color:"transparent",label:D.cancel,onClick:function(){_(""),g(o),d()}},B={color:"info",label:D.confirm,onClick:function(){if(_(""),S(!1),i&&0!==i.length){var e,a=function a(n){T&&(y(n),setTimeout((function(){Fa(e,a,t,P)}),2e3))},t=function(e){T&&(y(e),r!==b&&(S(!0),g(r)))};!function(e,a,t){if(e&&0!==e.length){Va("/batch/stop_guest/",{guest:e},(function(e,n){202===e?a(n.id):t("unexpected status "+e.toString())}),t)}else t("target is empty")}(i,(function(n){T&&(o===b&&g(l),Fa(e=n,a,t,P))}),P)}else P("no target selecetd")}},M={color:"info",label:D.finish,onClick:function(){T&&(g(o),_(""),m())}},z=function(e){var a=[];return e?(e.forEach((function(e,t){"fail"===e.status?a.push({id:e.id,text:D.fail+":"+e.error}):"stopped"===e.status?a.push({id:e.id,text:D.complete}):a.push({id:e.id,text:D.processing})})),s.a.createElement(Dc.a,{component:ye.a},s.a.createElement(hn.a,null,s.a.createElement(vn.a,null,s.a.createElement(yn.a,null,s.a.createElement(kn.a,null,D.instance),s.a.createElement(kn.a,null,D.result))),s.a.createElement(xn.a,null,a.map((function(e){return s.a.createElement(yn.a,{key:e.id},s.a.createElement(kn.a,{component:"th",scope:"row"},e.id),s.a.createElement(kn.a,null,e.text))})))))):s.a.createElement("div",null)};switch(s.a.useEffect((function(){return I(!0),function(){return I(!1)}}),[]),b){case l:a=z(v),t=w?[F]:[];break;case r:a=z(v),t=[M];break;default:a=D.content1+i.length.toString()+D.content2,t=[F,B]}return s.a.createElement(oa,{size:"sm",open:u,prompt:w,promptPosition:"top",hideBackdrop:!0,title:A,buttons:t,content:a,operatable:C})}var Fc={en:{title:"Batch Deleting Instance",content1:"Are you sure to delete ",content2:" instance(s)",cancel:"Cancel",confirm:"Confirm",finish:"Finish",fail:"Fail",complete:"Deleted",processing:"Deleting",instance:"Instance",result:"Result"},cn:{title:"\u6279\u91cf\u5220\u9664\u4e91\u4e3b\u673a",content1:"\u662f\u5426\u5220\u9664 ",content2:" \u4e2a\u4e91\u4e3b\u673a",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a",finish:"\u5b8c\u6210",fail:"\u5931\u8d25",complete:"\u5df2\u5220\u9664",processing:"\u5220\u9664\u4e2d",instance:"\u4e91\u4e3b\u673a",result:"\u5904\u7406\u7ed3\u679c"}};function Bc(e){var a,t,o=0,l=1,r=2,c=e.lang,i=e.targets,u=e.open,m=e.onSuccess,d=e.onCancel,p=s.a.useState(o),f=Object(n.a)(p,2),b=f[0],g=f[1],E=s.a.useState(null),h=Object(n.a)(E,2),v=h[0],y=h[1],x=s.a.useState(!0),k=Object(n.a)(x,2),C=k[0],S=k[1],O=s.a.useState(""),j=Object(n.a)(O,2),w=j[0],_=j[1],N=s.a.useState(!1),R=Object(n.a)(N,2),T=R[0],I=R[1],D=Fc[c],A=D.title,P=s.a.useCallback((function(e){T&&(S(!0),_(e))}),[T]),F={color:"transparent",label:D.cancel,onClick:function(){_(""),g(o),d()}},B={color:"info",label:D.confirm,onClick:function(){if(_(""),S(!1),i&&0!==i.length){var e,a=function a(n){T&&(y(n),setTimeout((function(){Pa(e,a,t,P)}),2e3))},t=function(e){T&&(y(e),r!==b&&(S(!0),g(r)))};!function(e,a,t){if(e&&0!==e.length){Va("/batch/delete_guest/",{guest:e},(function(e,n){202===e?a(n.id):t("unexpected status "+e.toString())}),t)}else t("target is empty")}(i,(function(n){T&&(o===b&&g(l),Pa(e=n,a,t,P))}),P)}else P("no target selecetd")}},M={color:"info",label:D.finish,onClick:function(){T&&(g(o),_(""),m())}},z=function(e){var a=[];return e?(e.forEach((function(e,t){"fail"===e.status?a.push({id:e.id,text:D.fail+":"+e.error}):"deleted"===e.status?a.push({id:e.id,text:D.complete}):a.push({id:e.id,text:D.processing})})),s.a.createElement(Dc.a,{component:ye.a},s.a.createElement(hn.a,null,s.a.createElement(vn.a,null,s.a.createElement(yn.a,null,s.a.createElement(kn.a,null,D.instance),s.a.createElement(kn.a,null,D.result))),s.a.createElement(xn.a,null,a.map((function(e){return s.a.createElement(yn.a,{key:e.id},s.a.createElement(kn.a,{component:"th",scope:"row"},e.id),s.a.createElement(kn.a,null,e.text))})))))):s.a.createElement("div",null)};switch(s.a.useEffect((function(){return I(!0),function(){return I(!1)}}),[]),b){case l:a=z(v),t=w?[F]:[];break;case r:a=z(v),t=[M];break;default:a=D.content1+i.length.toString()+D.content2,t=[F,B]}return s.a.createElement(oa,{size:"sm",open:u,prompt:w,promptPosition:"top",hideBackdrop:!0,title:A,buttons:t,content:a,operatable:C})}var Mc={en:{title:"Batch Creating Instances",rule:"Name Rule",ruleOrder:"By Order",ruleMAC:"By MAC",ruleAddress:"By Guest Address",prefix:"Prefix",count:"Create Quantity",resourcePool:"Resource Pool",core:"Core",memory:"Memory",systemDisk:"System Disk Size",dataDisk:"Data Disk Size",autoStartup:"Automatic Startup",systemVersion:"System Version",sourceImage:"Source Image",blankSystem:"Blank System",qos:"QoS Options (Optional)",cpuPriority:"CPU Priority",iops:"IOPS",outbound:"Outband Bandwidth",inbound:"Inbound Bandwidth",noDataDisk:"Don't use data disk",noLimit:"No Limit",cpuPriorityHigh:"High",cpuPriorityMedium:"Medium",cpuPriorityLow:"Low",modules:"Pre-Installed Modules",adminName:"Admin Name",adminPassword:"Admin Password",blankHelper:"Leave blank to generate",dataPath:"Data Path",off:"Off",on:"On",cancel:"Cancel",confirm:"Confirm",finish:"Finish",fail:"Fail",complete:"Created",processing:"Creating",instance:"Instance",result:"Result"},cn:{title:"\u6279\u91cf\u521b\u5efa\u4e91\u4e3b\u673a",rule:"\u5b9e\u4f8b\u547d\u540d\u89c4\u5219",ruleOrder:"\u987a\u5e8f\u9012\u589e",ruleMAC:"\u6309MAC\u5730\u5740",ruleAddress:"\u6309\u5b9e\u4f8b\u5730\u5740",prefix:"\u5b9e\u4f8b\u540d\u524d\u7f00",count:"\u521b\u5efa\u6570\u91cf",resourcePool:"\u8ba1\u7b97\u8d44\u6e90\u6c60",core:"\u6838\u5fc3\u6570",memory:"\u5185\u5b58",systemDisk:"\u7cfb\u7edf\u78c1\u76d8\u5bb9\u91cf",dataDisk:"\u6570\u636e\u78c1\u76d8\u5bb9\u91cf",autoStartup:"\u81ea\u52a8\u542f\u52a8",systemVersion:"\u7cfb\u7edf\u7248\u672c",sourceImage:"\u6765\u6e90\u955c\u50cf",blankSystem:"\u7a7a\u767d\u7cfb\u7edf",qos:"QoS\u9009\u9879 (\u53ef\u9009)",cpuPriority:"CPU\u4f18\u5148\u7ea7",iops:"\u78c1\u76d8\u8bfb\u5199\u9650\u5236",outbound:"\u4e0a\u884c\u5e26\u5bbd",inbound:"\u4e0b\u884c\u5e26\u5bbd",noDataDisk:"\u4e0d\u4f7f\u7528\u6570\u636e\u78c1\u76d8",noLimit:"\u65e0\u9650\u5236",cpuPriorityHigh:"\u9ad8",cpuPriorityMedium:"\u4e2d",cpuPriorityLow:"\u4f4e",modules:"\u9884\u88c5\u6a21\u5757",adminName:"\u7ba1\u7406\u5458\u8d26\u53f7",adminPassword:"\u7ba1\u7406\u5458\u5bc6\u7801",blankHelper:"\u7559\u7a7a\u5219\u81ea\u52a8\u751f\u6210\u65b0\u5bc6\u7801",dataPath:"\u6302\u8f7d\u6570\u636e\u8def\u5f84",off:"\u5173\u95ed",on:"\u5f00\u542f",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a",finish:"\u5b8c\u6210",fail:"\u5931\u8d25",complete:"\u521b\u5efa\u5b8c\u6210",processing:"\u521b\u5efa\u4e2d",instance:"\u4e91\u4e3b\u673a",result:"\u5904\u7406\u7ed3\u679c"}};function zc(e){var a=0,t=1,o=2,l="order",r="MAC",c="address",i=e.lang,u=e.open,m=e.onSuccess,d=e.onCancel,p={rule:l,prefix:"",count:1,name:"",pool:"",cores:1..toString(),memory:(1<<30).toString(),system_disk:5,data_disk:0,auto_start:!1,system_template:"",from_image:"__default",modules:new Map,module_cloud_init_admin_name:"root",module_cloud_init_admin_password:"",module_cloud_init_data_path:"/opt/data",priority:"medium",iops:0,inbound:0,outbound:0},f=s.a.useState(!1),b=Object(n.a)(f,2),g=b[0],h=b[1],v=s.a.useState(a),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState(null),S=Object(n.a)(C,2),O=S[0],j=S[1],w=s.a.useState(!0),_=Object(n.a)(w,2),N=_[0],R=_[1],T=s.a.useState(""),I=Object(n.a)(T,2),D=I[0],A=I[1],P=s.a.useState(!1),F=Object(n.a)(P,2),B=F[0],M=F[1],z=s.a.useState(p),W=Object(n.a)(z,2),q=W[0],H=W[1],L=s.a.useState({pools:[],images:[],versions:[]}),U=Object(n.a)(L,2),V=U[0],G=U[1],$=Mc[i],Z=$.title,J=s.a.useCallback((function(e){B&&(R(!0),A(e))}),[B]),Q=function(){A(""),H(p),h(!1),k(a)},X=function(e){B&&(A(""),a===x&&k(t),Aa(e,ee(e),ae(e),J))},ee=function e(a){return function(t){B&&(j(t),setTimeout((function(){Aa(a,e(a),ae(a),J)}),1e3))}},ae=function(e){return function(e){B&&(j(e),o!==x&&(R(!0),k(o)))}},te=function(e){return function(a){if(B){var t=a.target.value;H((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}}},ne=function(e){return function(a,t){B&&H((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}};s.a.useEffect((function(){if(u){var e=[],a=[{label:$.blankSystem,value:"__default"}],t=[];M(!0);var n=function(n){B&&(n.forEach((function(e){var a=e.id,n=e.name;t.push({label:n,value:a})})),G({pools:e,images:a,versions:t}),h(!0))},o=function(e){B&&(e.forEach((function(e){var t=e.name,n=e.id;a.push({label:t,value:n})})),Ba(n,J))};return fa((function(a){B&&(a.forEach((function(a){var t=a.name;e.push({label:t,value:t})})),Ra(o,J))}),J),function(){M(!1)}}}),[B,u,$.blankSystem,J]);var le,re,ce,ie=function(e){if(B){var a=[];return e?(e.forEach((function(e,t){var n;if("fail"===e.status)n=$.fail+":"+e.error;else if("created"===e.status)n=$.complete;else{var o=e.progress;n=s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:8,sm:9,md:10},s.a.createElement(Fl.a,{variant:"determinate",value:o})),s.a.createElement(Qe,{xs:4,sm:3,md:2},s.a.createElement(oe.a,{align:"center"},o.toFixed(2)+"%")))}a.push({name:e.name,content:n})})),s.a.createElement(Dc.a,{component:ye.a},s.a.createElement(hn.a,null,s.a.createElement(vn.a,null,s.a.createElement(yn.a,null,s.a.createElement(kn.a,null,$.instance),s.a.createElement(kn.a,null,$.result))),s.a.createElement(xn.a,null,a.map((function(e){return s.a.createElement(yn.a,{key:e.name},s.a.createElement(kn.a,{component:"th",scope:"row"},e.name),s.a.createElement(kn.a,null,e.content))})))))):s.a.createElement("div",null)}},se={color:"transparent",label:$.cancel,onClick:function(){Q(),d()}},ue={color:"info",label:$.confirm,onClick:function(){if(A(""),R(!1),q.prefix){var e=Number.parseInt(q.count);if(Number.isNaN(e))J("invalid count: "+q.count);else if(q.pool){var a=Number.parseInt(q.cores);if(Number.isNaN(a))J("invalid cores: "+q.cores);else{var t=Number.parseInt(q.memory);if(Number.isNaN(t))J("invalid memory: "+q.memory);else{var n=[q.system_disk*(1<<30)];0!==q.data_disk&&n.push(q.data_disk*(1<<30));var o,l=q.system_template;o="__default"===q.from_image?"":q.from_image;var r=[],c=!1;q.modules.forEach((function(e,a){e&&(r.push(a),"cloud-init"===a&&(c=!0))}));var i=null;c&&(i={admin_name:q.module_cloud_init_admin_name,admin_secret:q.module_cloud_init_admin_password,data_path:q.module_cloud_init_data_path});var s={cpu_priority:q.priority,write_iops:q.iops,read_iops:q.iops,receive_speed:q.inbound*(1<<17),send_speed:q.outbound*(1<<17)};!function(e,a,t,n,o,l,r,c,i,s,u,m,d,p,f){var b=ca();if(null!==b){var g={name_rule:e,count:t,owner:b.user,group:b.group,pool:n,cores:o,memory:l,disks:r,auto_start:c,from_image:i,template:s};a&&(g.name_prefix=a),u&&(g.modules=u),m&&(g.cloud_init=m),d&&(g.qos=d);Va("/batch/create_guest/",g,(function(e,a){202===e?p(a.id):f("unexpected status "+e.toString())}),f)}else f("session expired")}(q.rule,q.prefix,e,q.pool,a,t,n,q.auto_start,o,l,r,i,s,X,J)}}}else J("must specify target pool")}else J("prefix required")}},me={color:"info",label:$.finish,onClick:function(){B&&(Q(),m())}};if(g)switch(x){case t:le=ie(O),re=D?[se]:[];break;case o:le=ie(O),re=[me];break;default:re=[se,ue];var de=[];[1,2,4,8,16].forEach((function(e){de.push(e.toString())}));var pe,fe=[];[1,2,4,8,16,32].forEach((function(e){var a,t=512*e*(1<<20);a=t>=1<<30?t/(1<<30)+" GB":t/(1<<20)+" MB",fe.push({name:a,value:t.toString()})}));var be,ge=[];[5,60,30].forEach((function(e){ge.push({value:e,label:e+" GB"})})),pe=s.a.createElement(Fe.a,{color:"secondary",value:q.system_disk,max:60,min:5,step:1,valueLabelDisplay:"auto",marks:ge,onChange:ne("system_disk")});var he,ve=[{value:0,label:$.noDataDisk},{value:10,label:"10 GB"},{value:20,label:"20 GB"}];if(be=s.a.createElement(Fe.a,{color:"secondary",value:q.data_disk,max:20,min:0,step:2,valueLabelDisplay:"auto",marks:ve,onChange:ne("data_disk")}),q.system_template&&"__default"!==q.from_image){var xe,ke=[{value:"qemu",label:"QEMU-Guest-Agent"},{value:"cloud-init",label:"CloudInit"}];xe=q.modules.get("cloud-init")?s.a.createElement(Qe,{xs:12,sm:8,md:6},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(Te.a,{component:"fieldset",fullWidth:!0},s.a.createElement(Ie.a,{component:"legend"},"Cloud Init Options"),s.a.createElement(yc,null,s.a.createElement(we.a,{label:$.adminName,onChange:te("module_cloud_init_admin_name"),value:q.module_cloud_init_admin_name,margin:"normal",fullWidth:!0})),s.a.createElement(yc,null,s.a.createElement(we.a,{label:$.adminPassword,onChange:te("module_cloud_init_admin_password"),helperText:$.blankHelper,margin:"normal",fullWidth:!0})),s.a.createElement(yc,null,s.a.createElement(we.a,{label:$.dataPath,onChange:te("module_cloud_init_data_path"),value:q.module_cloud_init_data_path,margin:"normal",disabled:!0,fullWidth:!0}))))):s.a.createElement(Qe,null),he=s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(Te.a,{component:"fieldset",fullWidth:!0},s.a.createElement(Ie.a,{component:"legend"},$.modules),s.a.createElement(Ae.a,null,s.a.createElement(De.a,{container:!0},ke.map((function(e){var a,t,n;return a=!!q.modules.has(e.value)&&q.modules.get(e.value),s.a.createElement(Qe,{xs:12,sm:6,md:4,key:e.value},s.a.createElement(Re.a,{control:s.a.createElement(Pe.a,{checked:a,onChange:(t="modules",n=e.value,function(e){if(B){var a=e.target.checked;H((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},t,e[t].set(n,a)))}))}}),value:e.value}),label:e.label}))}))))))),xe)}else he=s.a.createElement(Qe,null);le=s.a.createElement(De.a,{container:!0},s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(Te.a,{component:"fieldset",fullWidth:!0},s.a.createElement(Ie.a,{component:"legend"},$.rule),s.a.createElement(Ne.a,{"aria-label":$.rule,name:"rule",value:q.rule,onChange:te("rule"),row:!0},s.a.createElement(K.a,{display:"flex"},s.a.createElement(K.a,null,s.a.createElement(Re.a,{value:l,control:s.a.createElement(_e.a,null),label:$.ruleOrder})),s.a.createElement(K.a,null,s.a.createElement(Re.a,{value:r,control:s.a.createElement(_e.a,{disabled:!0}),label:$.ruleMAC})),s.a.createElement(K.a,null,s.a.createElement(Re.a,{value:c,control:s.a.createElement(_e.a,{disabled:!0}),label:$.ruleAddress})))))))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:6},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(Ie.a,{component:"legend"},$.count),s.a.createElement(Fe.a,{color:"secondary",value:q.count,max:20,min:1,step:1,valueLabelDisplay:"auto",marks:[{value:1,label:"1"},{value:10,label:"10"},{value:20,label:"20"}],onChange:ne("count")})))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:6,md:4},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:$.prefix,onChange:te("prefix"),value:q.prefix,margin:"normal",required:!0,fullWidth:!0})))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:10,sm:4,md:3},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(je.a,{htmlFor:"pool"},$.resourcePool),s.a.createElement(Se.a,{value:q.pool,onChange:te("pool"),inputProps:{name:"pool",id:"pool"},required:!0,fullWidth:!0},V.pools.map((function(e,a){return s.a.createElement(Ee.a,{value:e.value,key:a},e.label)})))))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(Te.a,{component:"fieldset",fullWidth:!0},s.a.createElement(Ie.a,{component:"legend"},$.core),s.a.createElement(Ne.a,{"aria-label":$.core,name:"cores",value:q.cores,onChange:te("cores"),row:!0},s.a.createElement(De.a,{container:!0},de.map((function(e){return s.a.createElement(Qe,{xs:3,sm:2,md:1,key:e},s.a.createElement(Re.a,{value:e,control:s.a.createElement(_e.a,null),label:e}))}))))))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(Te.a,{component:"fieldset",fullWidth:!0},s.a.createElement(Ie.a,{component:"legend"},$.memory),s.a.createElement(Ne.a,{"aria-label":$.memory,name:"memory",value:q.memory,onChange:te("memory"),row:!0},s.a.createElement(De.a,{container:!0},fe.map((function(e){return s.a.createElement(Qe,{xs:6,sm:3,md:2,key:e.value},s.a.createElement(Re.a,{value:e.value,control:s.a.createElement(_e.a,null),label:e.name}))})))))))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:6,md:4},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(Ie.a,{component:"legend"},$.systemDisk),pe))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:6,md:4},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(Ie.a,{component:"legend"},$.dataDisk),be))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:10,sm:6,md:5},s.a.createElement(K.a,{m:0,pb:2},s.a.createElement(je.a,{htmlFor:"image"},$.sourceImage),s.a.createElement(Se.a,{value:q.from_image,onChange:te("from_image"),inputProps:{name:"image",id:"image"},fullWidth:!0},V.images.map((function(e,a){return s.a.createElement(Ee.a,{value:e.value,key:a},e.label)})))))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:10,sm:5,md:4},s.a.createElement(K.a,{m:0,pb:2},s.a.createElement(je.a,{htmlFor:"version"},$.systemVersion),s.a.createElement(Se.a,{value:q.system_template,onChange:te("system_template"),inputProps:{name:"version",id:"version"},fullWidth:!0},V.versions.map((function(e,a){return s.a.createElement(Ee.a,{value:e.value,key:a},e.label)})))))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:8,sm:6,md:4},s.a.createElement(K.a,{m:0,pb:2},s.a.createElement(je.a,{htmlFor:"auto_start"},$.autoStartup),$.off,s.a.createElement(Oe.a,{checked:q.failover,onChange:(ce="auto_start",function(e){if(B){var a=e.target.checked;H((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},ce,a))}))}}),color:"primary",inputProps:{name:"auto_start",id:"auto_start"}}),$.on))),he,s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:8,md:6},s.a.createElement(K.a,{m:0,pb:2},s.a.createElement(fc.a,null,s.a.createElement(bc.a,{expandIcon:s.a.createElement(hc.a,null)},$.qos),s.a.createElement(gc.a,null,s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:1,p:2},s.a.createElement(Te.a,{component:"fieldset",fullWidth:!0},s.a.createElement(Ie.a,{component:"legend"},$.cpuPriority),s.a.createElement(Ne.a,{"aria-label":$.cpuPriority,value:q.priority,onChange:te("priority"),row:!0},s.a.createElement(Re.a,{value:"high",control:s.a.createElement(_e.a,null),label:$.cpuPriorityHigh,key:"high"}),s.a.createElement(Re.a,{value:"medium",control:s.a.createElement(_e.a,null),label:$.cpuPriorityMedium,key:"medium"}),s.a.createElement(Re.a,{value:"low",control:s.a.createElement(_e.a,null),label:$.cpuPriorityLow,key:"low"}))))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:1,p:2},s.a.createElement(Ie.a,{component:"legend"},$.iops),s.a.createElement(Fe.a,{color:"secondary",value:q.iops,max:2e3,min:0,step:10,valueLabelDisplay:"auto",marks:[{value:0,label:$.noLimit},{value:2e3,label:2e3}],onChange:ne("iops")}))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:1,p:2},s.a.createElement(Ie.a,{component:"legend"},$.inbound),s.a.createElement(Fe.a,{color:"secondary",value:q.inbound,max:20,min:0,step:2,valueLabelDisplay:"auto",marks:[{value:0,label:$.noLimit},{value:20,label:"20 Mbit/s"}],onChange:ne("inbound")}))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:1,p:2},s.a.createElement(Ie.a,{component:"legend"},$.outbound),s.a.createElement(Fe.a,{color:"secondary",value:q.outbound,max:20,min:0,step:2,valueLabelDisplay:"auto",marks:[{value:0,label:$.noLimit},{value:20,label:"20 Mbit/s"}],onChange:ne("outbound")}))))))))))}else le=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}}),re=[se];return s.a.createElement(oa,{size:"md",open:u,prompt:D,promptPosition:"top",hideBackdrop:!0,title:Z,buttons:re,content:le,operatable:N})}var Wc=Object(g.a)({cardCategoryWhite:{"&,& a,& a:hover,& a:focus":{color:"rgba(255,255,255,.62)",margin:"0",fontSize:"14px",marginTop:"0",marginBottom:"0"},"& a,& a:hover,& a:focus":{color:"#FFFFFF"}},cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),qc={en:{createButton:"Create New Instance",batchCreate:"Batch Create",batchDelete:"Batch Delete",batchStop:"Batch Stop",enterBatch:"Enter Batch Mode",exitBatch:"Exit Batch Mode",tableTitle:"Cloud Instances",name:"Name",cell:"Host Cell",address:"Address",core:"Core",memory:"Memory",disk:"Disk",status:"Status",operates:"Operates",noResource:"No instances available",computePools:"Compute Pools"},cn:{createButton:"\u521b\u5efa\u4e91\u4e3b\u673a",batchCreate:"\u6279\u91cf\u521b\u5efa",batchDelete:"\u6279\u91cf\u5220\u9664",batchStop:"\u6279\u91cf\u505c\u6b62",enterBatch:"\u8fdb\u5165\u6279\u91cf\u6a21\u5f0f",exitBatch:"\u9000\u51fa\u6279\u91cf\u6a21\u5f0f",tableTitle:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",name:"\u540d\u79f0",cell:"\u627f\u8f7d\u8282\u70b9",address:"\u5730\u5740",core:"\u6838\u5fc3",memory:"\u5185\u5b58",disk:"\u78c1\u76d8",status:"\u72b6\u6001",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u4e91\u4e3b\u673a\u5b9e\u4f8b",computePools:"\u8ba1\u7b97\u8d44\u6e90\u6c60"}};function Hc(e){var a=Wc(),t=s.a.useState(null),o=Object(n.a)(t,2),l=o[0],r=o[1],c=s.a.useState(new Map),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(!1),p=Object(n.a)(d,2),f=p[0],g=p[1],E=Object(b.g)(),h=new URLSearchParams(E.search),v=h.get("pool"),y=h.get("cell"),x=s.a.useState(!1),k=Object(n.a)(x,2),C=k[0],S=k[1],O=s.a.useState(!1),j=Object(n.a)(O,2),w=j[0],_=j[1],N=s.a.useState(!1),R=Object(n.a)(N,2),T=R[0],I=R[1],D=s.a.useState(!1),A=Object(n.a)(D,2),P=A[0],F=A[1],B=s.a.useState(!1),M=Object(n.a)(B,2),z=M[0],W=M[1],q=s.a.useState(!1),H=Object(n.a)(q,2),L=H[0],U=H[1],V=s.a.useState(!1),G=Object(n.a)(V,2),$=G[0],Z=G[1],Y=s.a.useState(!1),J=Object(n.a)(Y,2),Q=J[0],X=J[1],ee=s.a.useState(!1),ae=Object(n.a)(ee,2),te=ae[0],ne=ae[1],le=s.a.useState(!1),re=Object(n.a)(le,2),ce=re[0],ie=re[1],se=s.a.useState(""),ue=Object(n.a)(se,2),me=ue[0],de=ue[1],pe=s.a.useState(""),be=Object(n.a)(pe,2),ge=be[0],Ee=be[1],he=s.a.useState(""),ve=Object(n.a)(he,2),ye=ve[0],xe=ve[1],ke=s.a.useState("warning"),Se=Object(n.a)(ke,2),Oe=Se[0],je=Se[1],we=s.a.useState(""),_e=Object(n.a)(we,2),Ne=_e[0],Re=_e[1],Te=function(){Re("")},Ie=s.a.useCallback((function(e){je("warning"),Re(e),setTimeout(Te,3e3)}),[je,Re]),De=function(e){je("info"),Re(e),Wa(e),setTimeout(Te,3e3)},Ae=s.a.useCallback((function(){ya(v,y,(function(e){var a=u,t=!1;if(e){r(e);var n=[];a.forEach((function(a,t){e.some((function(e){return e.id===t}))||n.push(t)})),e.forEach((function(e){var n=e.id;a.has(n)||(a.set(n,!1),t||(t=!0))})),0!==n.length&&n.forEach((function(e){a.delete(e),t||(t=!0)}))}else r([]),0!==a.size&&(a.clear(),t=!0);t&&m(new Map(a))}),(function(e){Ie(e),ia()}))}),[v,y,u,Ie]),Fe=function(e){_(!0),de(e)},Be=function(){_(!1)},Me=function(e){I(!0),de(e)},ze=function(){I(!1)},We=function(e){F(!0),de(e)},qe=function(){F(!1)},He=function(e){W(!0),de(e)},Le=function(){W(!1)},Ue=function(e){U(!0),de(e)},Ve=function(){U(!1)},Ge=function(e,a,t){Z(!0),de(e),Ee(a),xe(t)},$e=function(){Z(!1)},Ze=function(){S(!1)},Ye=function(){X(!1)},Je=function(){ne(!1)},Ke=function(){ie(!1)},Xe=function(){Ae()},ea=function(e,a){var t=new Map(u);t.set(a,e),m(t)};if(s.a.useEffect((function(){var e=!0;Ae();var a=setInterval((function(){e&&Ae()}),5e3);return function(){e=!1,clearInterval(a)}}),[Ae]),!v)return console.log("pool name omit"),sa();if(null===ca())return sa();var aa,ta=e.lang,na=qc[ta];if(null===l)aa=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});else if(0===l.length)aa=s.a.createElement(K.a,{textAlign:"center"},s.a.createElement(fn,null,na.noResource));else{var oa;oa=f?s.a.createElement(K.a,{display:"flex",alignItems:"center"},s.a.createElement(K.a,null,s.a.createElement(Pe.a,{onChange:function(e){var a,t=e.target.checked,n=new Map,o=Object(Or.a)(u.keys());try{for(o.s();!(a=o.n()).done;){var l=a.value;n.set(l,t)}}catch(r){o.e(r)}finally{o.f()}m(n)}})),s.a.createElement(K.a,null,na.name)):na.name,aa=s.a.createElement(Dr,{color:"primary",headers:[oa,na.cell,na.address,na.core,na.memory,na.disk,na.status,na.operates],rows:l.map((function(e){var a=e.id;return s.a.createElement(mc,{key:e.id,instance:e,lang:ta,checked:!(!u||!u.has(a))&&u.get(a),checkable:f,onCheckStatusChanged:ea,onNotify:De,onError:Ie,onDelete:Fe,onMediaStart:Me,onInsertMedia:We,onResetSystem:He,onBuildImage:Ue,onStatusChange:Xe,onMigrateInstance:Ge})}))})}var la=[s.a.createElement(yt.a,{to:"/admin/compute_pools/",key:na.computePools},na.computePools)];y?(la.push(s.a.createElement(yt.a,{to:"/admin/instances/range/?pool="+v,key:v},v)),la.push(s.a.createElement(oe.a,{color:"textPrimary",key:y},y))):la.push(s.a.createElement(oe.a,{color:"textPrimary",key:v},v));var ra=[{label:na.createButton,icon:Tt.a,color:"info",onClick:function(){S(!0)}},{label:na.batchCreate,icon:Nr.a,color:"info",onClick:function(){ie(!0)}}];f?ra.push({label:na.batchDelete,icon:Wt.a,color:"danger",onClick:function(){ne(!0)}},{label:na.batchStop,icon:wr.a,color:"info",onClick:function(){X(!0)}},{label:na.exitBatch,icon:Tr.a,color:"success",onClick:function(){g(!1)}}):ra.push({label:na.enterBatch,icon:Qo.a,color:"info",onClick:function(){var e,a=new Map,t=Object(Or.a)(u.keys());try{for(t.s();!(e=t.n()).done;){var n=e.value;a.set(n,!1)}}catch(o){t.e(o)}finally{t.f()}m(a),g(!0)}});var ua=[];return u&&(u.forEach((function(e,a){e&&ua.push(a)})),ua.sort()),s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(It.a,{separator:"\u203a","aria-label":"breadcrumb"},la)),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{display:"flex"},ra.map((function(e,a){var t=e.label,n=e.color,o=e.icon,l=e.onClick;return s.a.createElement(K.a,{key:a,m:1},s.a.createElement(fe,{size:"sm",color:n,round:!0,onClick:l},s.a.createElement(o),t))})))),s.a.createElement(Qe,{xs:12,sm:12,md:12},s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",{className:a.cardTitleWhite},na.tableTitle)),s.a.createElement(dn,null,aa))),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:Oe,message:Ne,open:""!==Ne,closeNotification:Te,close:!0})),s.a.createElement(Qe,null,s.a.createElement(kc,{lang:ta,open:C,onSuccess:function(e){Ze(),De("new instance "+e+" created"),Ae()},onCancel:Ze})),s.a.createElement(Qe,null,s.a.createElement(pc,{lang:ta,instanceID:me,open:w,onSuccess:function(e){Be(),De("instance "+e+" deleted"),Ae()},onCancel:Be})),s.a.createElement(Qe,null,s.a.createElement(Sc,{lang:ta,instanceID:me,open:T,onSuccess:function(e){ze(),De("instance "+e+" started with media"),Ae()},onCancel:ze})),s.a.createElement(Qe,null,s.a.createElement(jc,{lang:ta,instanceID:me,open:P,onSuccess:function(e){qe(),De("instance "+e+" started with media"),Ae()},onCancel:qe})),s.a.createElement(Qe,null,s.a.createElement(Rc,{lang:ta,instanceID:me,open:z,onSuccess:function(e){Le(),De("guest system of "+e+" reset")},onCancel:Le})),s.a.createElement(Qe,null,s.a.createElement(_c,{lang:ta,instanceID:me,open:L,onSuccess:function(e){Ve(),De("new image "+e+" created from "+me)},onCancel:Ve})),s.a.createElement(Qe,null,s.a.createElement(Ic,{lang:ta,instanceID:me,sourcePool:ge,sourceCell:ye,open:$,onSuccess:function(e){$e(),De("instance "+e+" migrated"),Ae()},onCancel:$e})),s.a.createElement(Qe,null,s.a.createElement(Pc,{lang:ta,open:Q,targets:Q?ua:[],onSuccess:function(){Ye(),Ae()},onCancel:Ye})),s.a.createElement(Qe,null,s.a.createElement(Bc,{lang:ta,open:te,targets:te?ua:[],onSuccess:function(){Je(),Ae()},onCancel:Je})),s.a.createElement(Qe,null,s.a.createElement(zc,{lang:ta,open:ce,onSuccess:function(){Ke(),Ae()},onCancel:Ke})))}var Lc={en:{title:"Instance",cores:"Cores",memory:"Memory",disks:"Disk",autoStartup:"Auto Startup",internalAddress:"Internal Address",externalAddress:"External Address",ioUsage:"IO Usage",stopped:"Stopped ",running:"Running ",used:"Used ",available:"Available ",coresUsed:"Cores Used",throughput:"Throughput",receive:"Receive ",send:"Send ",write:"Write ",read:"Read ",flags:"Running Flags",mediaAttached:"Media Attached"},cn:{title:"\u4e91\u4e3b\u673a",cores:"\u6838\u5fc3\u6570",memory:"\u5185\u5b58",disks:"\u78c1\u76d8",autoStartup:"\u5f00\u673a\u542f\u52a8",internalAddress:"\u5185\u90e8\u5730\u5740",externalAddress:"\u5916\u90e8\u5730\u5740",ioUsage:"IO\u541e\u5410\u91cf",stopped:"\u505c\u6b62",running:"\u8fd0\u884c",used:"\u5df2\u7528",available:"\u53ef\u7528",coresUsed:"\u6838\u5fc3\u5df2\u5360\u7528",throughput:"\u541e\u5410\u91cf",receive:"\u63a5\u6536",send:"\u53d1\u9001",write:"\u5199\u5165",read:"\u8bfb\u53d6",flags:"\u8fd0\u884c\u6807\u5fd7",mediaAttached:"\u5df2\u6302\u8f7d\u5a92\u4f53"}},Uc={borderRadius:"3px",marginTop:"-20px",padding:"15px"},Vc=Object(E.a)(Object(E.a)(Object(E.a)({},Yn),U),{},{cardWithDivider:{borderTop:"1px solid "+_[10]},coresChart:Object(E.a)(Object(E.a)({},Uc),{},{background:O[0]}),memoryChart:Object(E.a)(Object(E.a)({},Uc),{},{background:j[0]}),networkChart:Object(E.a)(Object(E.a)({},Uc),{},{background:C[0]}),diskChart:Object(E.a)(Object(E.a)({},Uc),{},{background:w[0]}),disableChart:Object(E.a)(Object(E.a)({},Uc),{},{background:_[5]})}),Gc=Object(g.a)(Vc),$c=function(e){var a,t,n,o=e.lang,l=e.status,r=Gc(),c=Lc[o];if(a=l.running?s.a.createElement(oe.a,{component:"span",className:r.cardTitle},s.a.createElement(Pr.a,{className:r.successText}),c.title+": "+l.name+" ( "+c.running+" )"):s.a.createElement(oe.a,{component:"span",className:r.cardTitle},s.a.createElement(Br.a,{className:r.mutedText}),c.title+": "+l.name+" ( "+c.stopped+" )"),l.running){var i={label:c.coresUsed,color:"#FFF",data:[]},u=0;l.coreRecords.forEach((function(e){i.data.push(e.current),u=Math.max(u,e.max)}));var m=s.a.createElement(Qe,{xs:8,sm:6,md:3,key:"cores-usage"},s.a.createElement(K.a,{m:0,p:0,className:r.coresChart,boxShadow:2},s.a.createElement(Qn,{series:[i],minTickStep:1,maxValue:100,maxTicks:5,displayValue:function(e){return e.toString()+"%"}}))),d={label:c.used+c.memory,color:_[4],data:[]},p={label:c.available+c.memory,color:O[1],data:[]};l.memoryRecords.forEach((function(e){d.data.push(e.used),p.data.push(e.available)}));var f=s.a.createElement(Qe,{xs:8,sm:6,md:3,key:"memory-usage"},s.a.createElement(K.a,{m:0,p:0,className:r.memoryChart,boxShadow:2},s.a.createElement(Xn,{series:[d,p],minTickStep:1024,displayValue:function(e){return 0===e?"0":e>=1024?0===e%1024?(e/1024).toString()+" GB":(e/1024).toFixed(2)+" GB":e.toString()+" MB"}}))),b={label:c.receive+c.throughput,color:j[3],data:[]},g={label:c.send+c.throughput,color:k[1],data:[]};l.networkSpeed.forEach((function(e){b.data.push(da(e.receive/(1<<20),2)),g.data.push(da(e.send/(1<<20),2))}));var E=function(e){return e>=1024?0===e%1024?(e/1024).toString()+" GB/s":(e/1024).toFixed(2)+" GB/s":Number.isInteger(e)?e.toString()+" MB/s":e.toFixed(2)+" MB/s"},h=[b,g],v=s.a.createElement(Qe,{xs:8,sm:6,md:3,key:"network-usage"},s.a.createElement(K.a,{m:0,p:0,className:r.networkChart,boxShadow:2},s.a.createElement(ao,{series:h,displayValue:E,minTickStep:1}))),y={label:c.write+c.throughput,color:O[1],data:[]},x={label:c.read+c.throughput,color:j[3],data:[]};l.diskSpeed.forEach((function(e){y.data.push(da(e.write/(1<<20),2)),x.data.push(da(e.read/(1<<20),2))}));var C=[y,x];t=[m,f,v,s.a.createElement(Qe,{xs:8,sm:6,md:3,key:"io-usage"},s.a.createElement(K.a,{m:0,p:0,className:r.diskChart,boxShadow:2},s.a.createElement(ao,{series:C,displayValue:E,minTickStep:1})))];var S=s.a.createElement(K.a,{m:1,p:2,key:"core-label"},s.a.createElement(oe.a,{component:"span",className:r.cardTitle},c.cores+": "),s.a.createElement(oe.a,{component:"span"},l.cores)),w=s.a.createElement(K.a,{m:1,p:2,key:"memory-label"},s.a.createElement(oe.a,{component:"span",className:r.cardTitle},c.memory+": "),s.a.createElement(oe.a,{component:"span"},ua(l.memory))),N=[];l.disks.forEach((function(e){N.push(ua(e))}));var R=s.a.createElement(K.a,{m:1,p:2,key:"disk-label"},s.a.createElement(oe.a,{component:"span",className:r.cardTitle},c.disks+": "),s.a.createElement(oe.a,{component:"span"},N.join(" / "))),T=s.a.createElement(K.a,{m:1,p:2,key:"internal-label"},s.a.createElement(oe.a,{component:"span",className:r.cardTitle},c.internalAddress+": "),s.a.createElement(oe.a,{component:"span"},l.internal&&l.internal.network_address?l.internal.network_address:"")),I=s.a.createElement(K.a,{m:1,p:2,key:"external-label"},s.a.createElement(oe.a,{component:"span",className:r.cardTitle},c.externalAddress+": "),s.a.createElement(oe.a,{component:"span"},l.external&&l.external.network_address?l.external.network_address:"")),D=[];l.auto_start&&D.push(s.a.createElement(te.a,{title:c.autoStartup,placement:"top",key:c.autoStartup},s.a.createElement(cc.a,{className:r.infoText}))),l.media_attached&&D.push(s.a.createElement(te.a,{title:c.mediaAttached,placement:"top",key:c.mediaAttached},s.a.createElement(sc.a,{className:r.infoText}))),n=[S,w,R,T,I,s.a.createElement(K.a,{m:1,p:2,key:"flag-label"},s.a.createElement(oe.a,{component:"span",className:r.cardTitle},c.flags+": "),D)]}else t=new Array(4).fill(s.a.createElement(Qe,{xs:8,sm:6,md:3},s.a.createElement(K.a,{m:0,p:0,className:r.disableChart,boxShadow:2}))),n=[];return s.a.createElement(nn,{chart:!0},s.a.createElement(cn,null,s.a.createElement(Xt,null,t)),s.a.createElement(dn,null,a,s.a.createElement(K.a,{m:0,p:0,className:r.cardWithDivider,display:"flex",alignItems:"center"},n)))};function Zc(e){var a,t,o=e.match.params.id,l=s.a.useState(!1),r=Object(n.a)(l,2),c=r[0],i=r[1],u=s.a.useState(!1),m=Object(n.a)(u,2),d=m[0],p=m[1],f=s.a.useState(null),b=Object(n.a)(f,2),g=b[0],h=b[1],v=s.a.useState(""),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useCallback((function(){k("")}),[k]),S=s.a.useCallback((function(e){if(d){k(e),setTimeout(C,3e3)}}),[k,C,d]);if(s.a.useEffect((function(){p(!0);var e,a,t,n=new Array(5).fill({current:0,max:0}),l=new Array(5).fill({available:0,used:0}),r=new Array(5).fill({receive:0,send:0}),c=new Array(5).fill({write:0,read:0}),s=new Array(5).fill({receive:0,send:0}),u=new Array(5).fill({write:0,read:0}),m=!1,f=function(e){if(d){var o,p;if(n.shift(),n.push({current:da(e.cpu_usage,2),max:e.cores}),e.memory_available>e.memory?(S("abnormal available memory, "+e.memory_available+" > allocated "+e.memory),p=e.memory,o=0):(p=e.memory_available,o=e.memory-e.memory_available),l.shift(),l.push({available:da(p/(1<<20),2),used:da(o/(1<<20),2)}),r.shift(),r.push({receive:e.bytes_received,send:e.bytes_sent}),c.shift(),c.push({write:e.bytes_written,read:e.bytes_read}),m){var f=(r[r.length-1].receive-r[r.length-2].receive)/2,b=(r[r.length-1].send-r[r.length-2].send)/2,g=(c[c.length-1].write-c[c.length-2].write)/2,v=(c[c.length-1].read-c[c.length-2].read)/2;s.shift(),s.push({receive:f,send:b}),u.shift(),u.push({write:g,read:v})}else m=!0;var y=Object(E.a)(Object(E.a)({},e),{},{pool:a,cell:t,coreRecords:n,memoryRecords:l,networkRecords:r,diskRecords:c,networkSpeed:s,diskSpeed:u});h(y),i(!0)}};return xa(o,(function(n){if(d){a=n.pool,t=n.cell,ka(o,f,S);e=setInterval((function(){d&&ka(o,f,S)}),2e3)}}),S),function(){p(!1),e&&clearInterval(e)}}),[o,S,d,c]),c){a=s.a.createElement(Qe,{xs:12},s.a.createElement($c,{status:g,lang:e.lang}));var O=[s.a.createElement(yt.a,{to:"/admin/instances/range/?pool="+g.pool,key:g.pool},g.pool),s.a.createElement(yt.a,{to:"/admin/instances/range/?pool="+g.pool+"&cell="+g.cell,key:g.cell},g.cell),s.a.createElement(oe.a,{color:"textPrimary",key:g.name},g.name)];t=s.a.createElement(It.a,{separator:"\u203a","aria-label":"breadcrumb"},O)}else a=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}}),t=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},t),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),a,s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:"warning",message:x,open:""!==x,closeNotification:C,close:!0})))}var Yc=t(603),Jc=t(602),Qc=t(302),Kc=t.n(Qc),Xc=t(301),ei=t.n(Xc),ai=t(299),ti=t.n(ai),ni=t(300),oi=t.n(ni),li=t(303),ri=t.n(li),ci=t(224),ii={en:{title:"Create Snapshot",name:"Name",description:"Description",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u521b\u5efa\u4e91\u4e3b\u673a\u5feb\u7167",name:"\u540d\u79f0",description:"\u63cf\u8ff0",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function si(e){var a={name:"",description:""},t=e.lang,o=e.open,l=e.instanceID,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!0),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(""),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(a),v=Object(n.a)(h,2),y=v[0],x=v[1],k=ii[t],C=k.title,S=function(e){d(!0),g(e)},O=function(){g(""),x(a)},j=function(e){d(!0),O(),r(e)},w=function(e){return function(a){var t=a.target.value;x((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}},_=[{type:"text",label:k.name,onChange:w("name"),value:y.name,required:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"text",label:k.description,onChange:w("description"),value:y.description,required:!0,oneRow:!0,xs:12,sm:10,md:8}],N=s.a.createElement(ze,{inputs:_}),R=[{color:"transparent",label:k.cancel,onClick:function(){O(),c()}},{color:"info",label:k.confirm,onClick:function(){g(""),d(!1),y.name?y.description?function(e,a,t,n,o){Ua("/instances/"+e+"/snapshots/",{name:a,description:t},(function(){n(a,e)}),o)}(l,y.name,y.description,j,S):S("must specify description"):S("must specify snapshot name")}}];return s.a.createElement(oa,{size:"sm",open:o,prompt:b,title:C,buttons:R,content:N,operatable:m})}var ui={en:{title:"Delete Snapshot",content:"Are you sure to delete snapshot ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u4e91\u4e3b\u673a\u5feb\u7167",content:"\u662f\u5426\u5220\u9664\u4e91\u4e3b\u673a\u5feb\u7167 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function mi(e){var a=e.lang,t=e.instanceID,o=e.snapshotName,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!0),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(""),f=Object(n.a)(p,2),b=f[0],g=f[1],E=ui[a],h=E.title,v=E.content+o,y=function(e){d(!0),g(e)},x=function(){d(!0),g(""),r(o)},k=[{color:"transparent",label:E.cancel,onClick:function(){g(""),c()}},{color:"info",label:E.confirm,onClick:function(){d(!1),function(e,a,t,n){Za("/instances/"+e+"/snapshots/"+a,(function(){t(a,e)}),n)}(t,o,x,y)}}];return s.a.createElement(oa,{size:"sm",open:l,prompt:b,title:h,buttons:k,content:v,operatable:m})}var di={en:{title:"Revert Snapshot",content:"Are you sure to revert snapshot ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6062\u590d\u4e91\u4e3b\u673a\u5feb\u7167",content:"\u662f\u5426\u6062\u590d\u4e91\u4e3b\u673a\u5feb\u7167 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function pi(e){var a=e.lang,t=e.instanceID,o=e.snapshotName,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!0),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(""),f=Object(n.a)(p,2),b=f[0],g=f[1],E=di[a],h=E.title,v=E.content+o,y=function(e){d(!0),g(e)},x=function(){d(!0),g(""),r(o)},k=[{color:"transparent",label:E.cancel,onClick:function(){g(""),c()}},{color:"info",label:E.confirm,onClick:function(){d(!1),function(e,a,t,n){$a("/instances/"+e+"/snapshots/",{target:a},(function(){t(a,e)}),n)}(t,o,x,y)}}];return s.a.createElement(oa,{size:"sm",open:l,prompt:b,title:h,buttons:k,content:v,operatable:m})}var fi={panel:{background:t.n(ci).a[100]}},bi={en:{title:"Snapshots of ",noResource:"No snapshots created",back:"Back",create:"Create New Snapshot",delete:"Delete",revert:"Revert",current:"Current",name:"Name",description:"Description",createdTime:"Created Time",type:"Type",offline:"Offline Snapshot",realtime:"Realtime Snapshot"},cn:{title:"\u4e91\u4e3b\u673a\u5feb\u7167:",noResource:"\u5c1a\u672a\u521b\u5efa\u4e91\u4e3b\u673a\u5feb\u7167",back:"\u8fd4\u56de",create:"\u521b\u5efa\u65b0\u5feb\u7167",delete:"\u5220\u9664",revert:"\u6062\u590d",current:"\u5f53\u524d",name:"\u5feb\u7167\u540d\u79f0",description:"\u63cf\u8ff0",createdTime:"\u521b\u5efa\u65f6\u95f4",type:"\u7c7b\u578b",offline:"\u79bb\u7ebf\u5feb\u7167",realtime:"\u5b9e\u65f6\u5feb\u7167"}};function gi(e){var a=Object(g.a)(fi)(),t=e.match.params.id,o=s.a.useState(null),l=Object(n.a)(o,2),r=l[0],c=l[1],i=s.a.useState(null),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!1),f=Object(n.a)(p,2),b=f[0],h=f[1],v=s.a.useState(!1),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState(!1),S=Object(n.a)(C,2),O=S[0],j=S[1],w=s.a.useState("warning"),_=Object(n.a)(w,2),N=_[0],R=_[1],T=s.a.useState(""),I=Object(n.a)(T,2),D=I[0],A=I[1],P=function(){A("")},F=s.a.useCallback((function(e){R("warning"),A(e),setTimeout(P,3e3)}),[R,A]),B=function(e){R("info"),A(e),Wa(e),setTimeout(P,3e3)},M=s.a.useCallback((function(e){F(e)}),[F]),z=s.a.useCallback((function(e,a,t){var n=[];return t.get(e).forEach((function(e){var o={name:e};a===e&&(o.isCurrent=!0),t.has(e)&&(o.children=z(e,a,t)),n.push(o)})),n}),[]),W=s.a.useCallback((function(e){!function(e,a,t){Ha("/instances/"+e+"/snapshots/",a,t)}(t,(function(a){var t="",n="",o={},l=new Map;if(Object.keys(a).forEach((function(e){l.set(e,a[e])})),0!==l.length){var r=new Map;l.forEach((function(e,a){if(e.is_root&&(t=a),e.is_current&&(n=a),e.backing){var o=e.backing;r.has(o)?r.get(o).push(a):r.set(o,[a])}})),""!==t&&(o.name=t,t===n&&(o.isCurrent=!0),r.has(t)&&(o.children=z(t,n,r)))}c(e?{name:e,rootName:t,current:n,rootNode:o}:function(e){return Object(E.a)(Object(E.a)({},e),{},{rootName:t,current:n,rootNode:o})})}),M)}),[t,M,z]),q=function(){h(!1)},H=function(){k(!1)},L=function(){j(!1)},U=function(e){!function(e,a,t,n){Ha("/instances/"+e+"/snapshots/"+a,t,n)}(t,e,(function(a){d(Object(E.a)(Object(E.a)({},a),{},{name:e}))}),M)};if(s.a.useEffect((function(){xa(t,(function(e){W(e.name)}),M)}),[t,M,W]),null===ca())return sa();var V,G,$=e.lang,Z=bi[$],Y=[];if(null===r)V=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}}),G="";else{if(""===r.rootName)V=s.a.createElement(K.a,{display:"flex",justifyContent:"center"},Z.noResource);else{var J,Q=[],X=function e(a){var t;t=a.isCurrent?a.name+"( "+Z.current+" )":a.name,Q.push(a.name);var n={nodeId:a.name,label:t,key:a.name,onClick:function(e){e.preventDefault(),U(a.name)}};if(a.name===r.rootName?n.icon=s.a.createElement(ti.a,null):a.name===r.current&&(n.icon=s.a.createElement(oi.a,null)),a.children){var o=[];a.children.forEach((function(a){o.push(e(a))})),n.children=o}return s.a.createElement(Jc.a,n)}(r.rootNode),ee=s.a.createElement(Yc.a,{defaultCollapseIcon:s.a.createElement(ei.a,null),defaultExpandIcon:s.a.createElement(Kc.a,null),defaultEndIcon:s.a.createElement(qr.a,null),defaultExpanded:Q},X);if(m){var ae=[{title:Z.name,value:m.name},{title:Z.description,value:m.description},{title:Z.createdTime,value:m.create_time},{title:Z.type,value:m.running?Z.realtime:Z.offline}],te=[s.a.createElement(fe,{size:"sm",color:"info",onClick:function(){j(!0)}},s.a.createElement(ri.a,null),Z.revert),s.a.createElement(fe,{size:"sm",color:"info",onClick:function(){k(!0)}},s.a.createElement(Wt.a,null),Z.delete)];J=s.a.createElement(ye.a,{className:a.panel},s.a.createElement(K.a,{p:2,m:1},s.a.createElement(hn.a,{size:"small"},s.a.createElement(xn.a,null,ae.map((function(e){return s.a.createElement(yn.a,{key:e.title},s.a.createElement(kn.a,{component:"th"},s.a.createElement(oe.a,{component:"span",variant:"subtitle1"},e.title)),s.a.createElement(kn.a,null,s.a.createElement(oe.a,{component:"span"},e.value)))}))))),s.a.createElement(K.a,{display:"flex",m:2},te.map((function(e,a){return s.a.createElement(K.a,{key:a,m:2,p:1},e)}))))}else J=s.a.createElement("div",null);V=s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12,sm:6},ee),s.a.createElement(Qe,{xs:12,sm:6},J))}G=Z.title+r.name,Y=[s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){e.history.goBack()}},s.a.createElement(il.a,null),Z.back),s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){h(!0)}},s.a.createElement(Tt.a,null),Z.create)]}return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{display:"flex"},Y.map((function(e,a){return s.a.createElement(K.a,{key:a,pl:2,pr:2},e)})))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12,sm:12,md:12},s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},G),s.a.createElement(dn,null,V))),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:N,message:D,open:""!==D,closeNotification:P,close:!0})),s.a.createElement(Qe,null,s.a.createElement(si,{lang:$,open:b,instanceID:t,onSuccess:function(e){q(),B("new snapshot "+e+" created for "+r.name),W()},onCancel:q})),s.a.createElement(Qe,null,s.a.createElement(mi,{lang:$,open:x,instanceID:t,snapshotName:m?m.name:"",onSuccess:function(e){H(),B("snapshot "+e+" deleted"),W()},onCancel:H})),s.a.createElement(Qe,null,s.a.createElement(pi,{lang:$,open:O,instanceID:t,snapshotName:m?m.name:"",onSuccess:function(e){L(),B("restored to snapshot "+e),W()},onCancel:L})))}var Ei=t(604),hi=t(226),vi=t.n(hi),yi=t(227),xi=t.n(yi),ki=t(229),Ci=t.n(ki),Si=t(230),Oi=t.n(Si),ji=t(228),wi=t.n(ji),_i={en:{title:"Modify Instance Name",current:"Current Name",new:"New Name",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u4e91\u4e3b\u673a\u540d\u79f0",current:"\u5f53\u524d\u4e91\u4e3b\u673a\u540d",new:"\u65b0\u4e3b\u673a\u540d",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Ni(e){var a={name:""},t=e.lang,o=e.open,l=e.instanceID,r=e.current,c=e.onSuccess,i=e.onCancel,u=r?r.name.slice(r.group.length+1):"",m=s.a.useState(!0),d=Object(n.a)(m,2),p=d[0],f=d[1],b=s.a.useState(""),g=Object(n.a)(b,2),h=g[0],v=g[1],y=s.a.useState(!1),x=Object(n.a)(y,2),k=x[0],C=x[1],S=s.a.useState(a),O=Object(n.a)(S,2),j=O[0],w=O[1],_=_i[t],N=_.title,R=s.a.useCallback((function(e){k&&(f(!0),v(e))}),[k]),T=function(){v(""),w(a)},I=function(e){k&&(f(!0),T(),c(e,l))};s.a.useEffect((function(){if(o)return C(!0),function(){return C(!1)}}),[o]);var D,A=[{type:"text",label:_.current,value:u,disabled:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"text",label:_.new,onChange:(D="name",function(e){if(k){var a=e.target.value;w((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},D,a))}))}}),value:j.name,required:!0,oneRow:!0,xs:12,sm:10,md:8}],P=[{color:"transparent",label:_.cancel,onClick:function(){T(),i()}},{color:"info",label:_.confirm,onClick:function(){if(v(""),f(!1),j.name){var e=[r.group,j.name].join(".");u!==e?function(e,a,t,n){$a("/guests/"+e+"/name/",{name:a},(function(){t(a,e)}),n)}(l,e,I,R):R("no need to modify")}else R("must specify new instance name")}}],F=s.a.createElement(ze,{inputs:A});return s.a.createElement(oa,{size:"sm",open:o,prompt:h,title:N,buttons:P,content:F,operatable:p})}var Ri={en:{title:"Modify Instance Cores",current:"Current Cores",new:"New Cores",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u6838\u5fc3\u6570",current:"\u5f53\u524d\u6838\u5fc3\u6570",new:"\u65b0\u6838\u5fc3\u6570",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Ti(e){var a={cores:""},t=e.lang,o=e.open,l=e.instanceID,r=e.current,c=e.onSuccess,i=e.onCancel,u=r?r.cores:0,m=s.a.useState(!0),d=Object(n.a)(m,2),p=d[0],f=d[1],b=s.a.useState(""),g=Object(n.a)(b,2),h=g[0],v=g[1],y=s.a.useState(!1),x=Object(n.a)(y,2),k=x[0],C=x[1],S=s.a.useState(a),O=Object(n.a)(S,2),j=O[0],w=O[1],_=Ri[t],N=_.title,R=s.a.useCallback((function(e){k&&(f(!0),v(e))}),[k]),T=function(){v(""),w(a)},I=function(e){k&&(f(!0),T(),c(e,l))};s.a.useEffect((function(){if(o)return C(!0),function(){return C(!1)}}),[o]);var D,A=[{type:"text",label:_.current,value:u.toString(),disabled:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"text",label:_.new,onChange:(D="cores",function(e){if(k){var a=e.target.value;w((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},D,a))}))}}),value:j.cores,required:!0,oneRow:!0,xs:12,sm:10,md:8}],P=[{color:"transparent",label:_.cancel,onClick:function(){T(),i()}},{color:"info",label:_.confirm,onClick:function(){if(j.cores){var e=Number.parseInt(j.cores);Number.isNaN(e)?R("invalid cores number: "+j.cores):u!==e?(v(""),f(!1),function(e,a,t,n){$a("/guests/"+e+"/cores",{cores:a},(function(){t(a,e)}),n)}(l,e,I,R)):R("no need to modify")}else R("must specify new instance cores")}}],F=s.a.createElement(ze,{inputs:A});return s.a.createElement(oa,{size:"sm",open:o,prompt:h,title:N,buttons:P,content:F,operatable:p})}var Ii={en:{title:"Modify Memory of Instance",current:"Current Memory Size",new:"New Memory Size (MB)",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u5185\u5b58\u5927\u5c0f",current:"\u5f53\u524d\u5185\u5b58\u5bb9\u91cf",new:"\u65b0\u5185\u5b58\u5bb9\u91cf(MB)",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Di(e){var a={memory:""},t=e.lang,o=e.open,l=e.instanceID,r=e.current,c=e.onSuccess,i=e.onCancel,u=r?r.memory:0,m=ua(u),d=s.a.useState(!0),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(""),h=Object(n.a)(g,2),v=h[0],y=h[1],x=s.a.useState(!1),k=Object(n.a)(x,2),C=k[0],S=k[1],O=s.a.useState(a),j=Object(n.a)(O,2),w=j[0],_=j[1],N=Ii[t],R=N.title,T=s.a.useCallback((function(e){C&&(b(!0),y(e))}),[C]),I=function(){y(""),_(a)},D=function(e){C&&(b(!0),I(),c(e,l))};s.a.useEffect((function(){if(o)return S(!0),function(){return S(!1)}}),[o]);var A,P=[{type:"text",label:N.current,value:m,disabled:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"text",label:N.new,onChange:(A="memory",function(e){if(C){var a=e.target.value;_((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},A,a))}))}}),value:w.memory,required:!0,oneRow:!0,xs:12,sm:10,md:8}],F=[{color:"transparent",label:N.cancel,onClick:function(){I(),i()}},{color:"info",label:N.confirm,onClick:function(){if(w.memory){var e=Number.parseInt(w.memory);if(Number.isNaN(e))T("invalid memory size: "+w.memory);else{var a=e*(1<<20);u!==a?(y(""),b(!1),function(e,a,t,n){$a("/guests/"+e+"/memory",{memory:a},(function(){t(a,e)}),n)}(l,a,D,T)):T("no need to modify")}}else T("must specify new memory size")}}],B=s.a.createElement(ze,{inputs:P});return s.a.createElement(oa,{size:"sm",open:o,prompt:v,title:R,buttons:F,content:B,operatable:f})}var Ai={en:{title:"Modify Admin Password",name:"Admin Name",new:"New Password (generate a new one when blank)",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u7ba1\u7406\u5458\u5bc6\u7801",name:"\u5f53\u524d\u7ba1\u7406\u5458",new:"\u65b0\u5bc6\u7801(\u7559\u7a7a\u5219\u81ea\u52a8\u751f\u6210)",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Pi(e){var a={user:"",password:""},t=e.lang,o=e.open,l=e.instanceID,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!1),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!0),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(""),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(!1),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState(a),w=Object(n.a)(j,2),_=w[0],N=w[1],R=Ai[t],T=R.title,I=s.a.useCallback((function(e){S&&(g(!0),x(e))}),[S]),D=function(){x(""),N(a),d(!1)},A=function(e){S&&(g(!0),D(),r(e,l))};s.a.useEffect((function(){if(o&&l){O(!0);return wa(l,(function(e,a){S&&(N({user:e,password:a}),d(!0))}),I),function(){O(!1)}}}),[S,o,l,I]);var P,F,B=[{color:"transparent",label:R.cancel,onClick:function(){D(),c()}}];if(m){var M=[{type:"text",label:R.name,value:_.user,disabled:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"password",label:R.new,value:_.password,onChange:(F="password",function(e){if(S){var a=e.target.value;N((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},F,a))}))}}),required:!0,oneRow:!0,xs:12,sm:10,md:8}];P=s.a.createElement(ze,{inputs:M}),B.push({color:"info",label:R.confirm,onClick:function(){x(""),g(!1),function(e,a,t,n,o){var l={};a&&(l.user=a),t&&(l.password=t),$a("/guests/"+e+"/auth",l,(function(a){var t=a.user,o=a.password;n(t,o,e)}),o)}(l,_.user,_.password,A,I)}})}else P=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:o,prompt:y,title:T,buttons:B,content:P,operatable:b})}var Fi={en:{title:"Extend Disk Size",current:"Current Disk Size",new:"New Disk Size (GB)",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6269\u5c55\u78c1\u76d8\u5bb9\u91cf",current:"\u5f53\u524d\u78c1\u76d8\u5bb9\u91cf",new:"\u65b0\u78c1\u76d8\u5bb9\u91cf(GB)",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Bi(e){var a={size:""},t=e.lang,o=e.open,l=e.instanceID,r=e.current,c=e.index,i=e.onSuccess,u=e.onCancel,m=r?r.disks[c]:0,d=ua(m),p=s.a.useState(!0),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(""),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(!1),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState(a),w=Object(n.a)(j,2),_=w[0],N=w[1],R=Fi[t],T=R.title,I=s.a.useCallback((function(e){S&&(g(!0),x(e))}),[S]),D=function(){x(""),N(a)},A=function(e,a){S&&(g(!0),D(),i(e,a,l))};s.a.useEffect((function(){if(o)return O(!0),function(){return O(!1)}}),[o]);var P,F=[{type:"text",label:R.current,value:d,disabled:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"text",label:R.new,onChange:(P="size",function(e){if(S){var a=e.target.value;N((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},P,a))}))}}),value:_.size,required:!0,oneRow:!0,xs:12,sm:10,md:8}],B=[{color:"transparent",label:R.cancel,onClick:function(){D(),u()}},{color:"info",label:R.confirm,onClick:function(){if(_.size){var e=Number.parseInt(_.size);if(Number.isNaN(e))I("invalid disk size: "+_.size);else{var a=e*(1<<30);m!==a?(x(""),g(!1),function(e,a,t,n,o){var l={size:t};$a("/guests/"+e+"/disks/resize/"+a.toString(),l,(function(){n(a,t,e)}),o)}(l,c,a,A,I)):I("no need to modify")}}else I("must specify new disk size")}}],M=s.a.createElement(ze,{inputs:F});return s.a.createElement(oa,{size:"sm",open:o,prompt:y,title:T,buttons:B,content:M,operatable:b})}var Mi={en:{title:"Shrink Disk Size",content1:"Are you sure to shrink size of ",content2:" ? it will take a long time, please be patient and ignore the timeout warning.",cancel:"Cancel",confirm:"Confirm",systemDisk:"System Disk",dataDisk:"Data Disk"},cn:{title:"\u538b\u7f29\u78c1\u76d8\u5bb9\u91cf",content1:"\u662f\u5426\u538b\u7f29 ",content2:" \u7684\u78c1\u76d8\u7a7a\u95f4\uff0c\u8fd9\u4f1a\u5360\u7528\u5f88\u957f\u65f6\u95f4\uff0c\u8bf7\u5ffd\u7565\u8d85\u65f6\u63d0\u793a\u5e76\u8010\u5fc3\u7b49\u5f85",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a",systemDisk:"\u7cfb\u7edf\u78c1\u76d8",dataDisk:"\u6570\u636e\u78c1\u76d8"}};function zi(e){var a,t=e.lang,o=e.instanceID,l=e.index,r=e.open,c=e.onSuccess,i=e.onCancel,u=s.a.useState(!0),m=Object(n.a)(u,2),d=m[0],p=m[1],f=s.a.useState(""),b=Object(n.a)(f,2),g=b[0],E=b[1],h=s.a.useState(!1),v=Object(n.a)(h,2),y=v[0],x=v[1],k=Mi[t],C=k.title,S=s.a.useCallback((function(e){y&&(p(!0),E(e))}),[y]),O=function(e){y&&(p(!0),E(""),c(e,o))};s.a.useEffect((function(){if(r)return x(!0),function(){return x(!1)}}),[r]),a=0===l?k.content1+k.systemDisk+k.content2:k.content1+k.dataDisk+l.toString()+k.content2;var j=[{color:"transparent",label:k.cancel,onClick:function(){E(""),i()}},{color:"info",label:k.confirm,onClick:function(){E(""),p(!1),function(e,a,t,n){$a("/guests/"+e+"/disks/shrink/"+a.toString(),{immediate:!1},(function(){t(a,e)}),n)}(o,l,O,S)}}];return s.a.createElement(oa,{size:"sm",open:r,prompt:g,title:C,buttons:j,content:a,operatable:d})}var Wi={en:{title:"Modify CPU Priority",label:"CPU Priority",cpuPriorityHigh:"High",cpuPriorityMedium:"Medium",cpuPriorityLow:"Low",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539CPU\u4f18\u5148\u7ea7",label:"CPU\u4f18\u5148\u7ea7",cpuPriorityHigh:"\u9ad8",cpuPriorityMedium:"\u4e2d",cpuPriorityLow:"\u4f4e",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function qi(e){var a=e.lang,t=e.open,o=e.instanceID,l=e.onSuccess,r=e.onCancel,c={priority:"medium"},i=s.a.useState(!0),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!1),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(""),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(!1),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState(c),w=Object(n.a)(j,2),_=w[0],N=w[1],R=Wi[a],T=R.title,I=s.a.useCallback((function(e){S&&(d(!0),x(e))}),[S]),D=function(){x(""),N(c),g(!1)},A=function(e){S&&(d(!0),D(),l(e,o))};s.a.useEffect((function(){if(t&&o){O(!0);return xa(o,(function(e){if(S){var a="medium";e.qos&&(a=e.qos.cpu_priority),N({priority:a}),g(!0)}}),I),function(){return O(!1)}}}),[t,o,S,I]);var P,F,B=[{color:"transparent",label:R.cancel,onClick:function(){D(),r()}}];if(b){var M=[{label:R.cpuPriorityHigh,value:"high"},{label:R.cpuPriorityMedium,value:"medium"},{label:R.cpuPriorityLow,value:"low"}],z=[{type:"radio",label:R.label,onChange:(F="priority",function(e){if(S){var a=e.target.value;N((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},F,a))}))}}),value:_.priority,options:M,required:!0,xs:12}];P=s.a.createElement(ze,{inputs:z}),B.push({color:"info",label:R.confirm,onClick:function(){_.priority?(x(""),d(!1),function(e,a,t,n){$a("/guests/"+e+"/qos/cpu",{priority:a},(function(){t(a,e)}),n)}(o,_.priority,A,I)):I("invalid priority value")}})}else P=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:t,prompt:y,title:T,buttons:B,content:P,operatable:m})}var Hi={en:{title:"Modify Disk IOPS",label:"IOPS",noLimit:"No Limit",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u78c1\u76d8\u8bfb\u5199\u9650\u5236",label:"\u78c1\u76d8\u8bfb\u5199\u9650\u5236",noLimit:"\u65e0\u9650\u5236",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Li(e){var a={iops:0},t=e.lang,o=e.open,l=e.instanceID,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!0),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!1),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(""),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(!1),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState(a),w=Object(n.a)(j,2),_=w[0],N=w[1],R=Hi[t],T=R.title,I=s.a.useCallback((function(e){S&&(d(!0),x(e))}),[S]),D=function(){x(""),N(a),g(!1)},A=function(e){S&&(d(!0),D(),r(e,l))};s.a.useEffect((function(){if(o&&l){O(!0);return xa(l,(function(e){if(S){var a=0;e.qos&&(a=e.qos.write_iops),N({iops:a}),g(!0)}}),I),function(){return O(!1)}}}),[o,l,S,I]);var P,F,B=[{color:"transparent",label:R.cancel,onClick:function(){D(),c()}}];if(b){var M=[{value:0,label:R.noLimit},{value:2e3,label:2e3}],z=[{type:"slider",label:R.label,onChange:(F="iops",function(e,a){S&&N((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},F,a))}))}),value:_.iops,marks:M,step:10,maxStep:2e3,minStep:0,required:!0,xs:12}];P=s.a.createElement(ze,{inputs:z}),B.push({color:"info",label:R.confirm,onClick:function(){x(""),d(!1),function(e,a,t,n){$a("/guests/"+e+"/qos/disk",{write_iops:a,read_iops:a},(function(){t(a,e)}),n)}(l,_.iops,A,I)}})}else P=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:o,prompt:y,title:T,buttons:B,content:P,operatable:m})}var Ui={en:{title:"Modify Network Bandwidth",outbound:"Outband Bandwidth",inbound:"Inbound Bandwidth",noLimit:"No Limit",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u7f51\u7edc\u5e26\u5bbd\u9650\u5236",outbound:"\u4e0a\u884c\u5e26\u5bbd",inbound:"\u4e0b\u884c\u5e26\u5bbd",noLimit:"\u65e0\u9650\u5236",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Vi(e){var a={inbound:0,outbound:0},t=e.lang,o=e.open,l=e.instanceID,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!0),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!1),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(""),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(!1),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState(a),w=Object(n.a)(j,2),_=w[0],N=w[1],R=Ui[t],T=R.title,I=s.a.useCallback((function(e){S&&(d(!0),x(e))}),[S]),D=function(){x(""),N(a),g(!1)},A=function(e,a){S&&(d(!0),D(),r(e,a,l))},P=function(e){return function(a,t){S&&N((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}};s.a.useEffect((function(){if(o&&l){O(!0);return xa(l,(function(e){if(S){var t=a;e.qos&&e.qos.receive_speed&&(t.inbound=e.qos.receive_speed/(1<<17)),e.qos&&e.qos.send_speed&&(t.outbound=e.qos.send_speed/(1<<17)),N(t),g(!0)}}),I),function(){return O(!1)}}}),[o,l,S,I,1<<17,a]);var F,B=[{color:"transparent",label:R.cancel,onClick:function(){D(),c()}}];if(b){var M=[{value:0,label:R.noLimit},{value:20,label:"20 Mbit/s"}],z=[{type:"slider",label:R.inbound,onChange:P("inbound"),value:_.inbound,marks:M,step:2,maxStep:20,minStep:0,required:!0,xs:12},{type:"slider",label:R.outbound,onChange:P("outbound"),value:_.outbound,marks:M,step:2,maxStep:20,minStep:0,required:!0,xs:12}];F=s.a.createElement(ze,{inputs:z}),B.push({color:"info",label:R.confirm,onClick:function(){x(""),d(!1),function(e,a,t,n,o){$a("/guests/"+e+"/qos/network",{receive_speed:a,send_speed:t},(function(){n(a,t,e)}),o)}(l,_.inbound*(1<<17),_.outbound*(1<<17),A,I)}})}else F=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:o,prompt:y,title:T,buttons:B,content:F,operatable:m})}var Gi={en:{title:"Reset Monitor Secret",content:"Are you sure to reset monitor secret of instance ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u91cd\u7f6e\u76d1\u63a7\u5bc6\u7801",content:"\u662f\u5426\u91cd\u7f6e\u4e91\u4e3b\u673a\u76d1\u63a7\u5bc6\u7801 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function $i(e){var a=e.lang,t=e.guestID,o=e.guestName,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!0),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(""),f=Object(n.a)(p,2),b=f[0],g=f[1],E=Gi[a],h=E.title,v=E.content+o,y=function(e){d(!0),g(e)},x=function(){d(!0),g(""),r(t)},k=[{color:"transparent",label:E.cancel,onClick:function(){g(""),c()}},{color:"info",label:E.confirm,onClick:function(){d(!1),function(e,a,t){$a("/guests/"+e+"/monitor/secret","",(function(){a(e)}),t)}(t,x,y)}}];return s.a.createElement(oa,{size:"sm",open:l,prompt:b,title:h,buttons:k,content:v,operatable:m})}var Zi={en:{title:"Details of Instance ",back:"Back",name:"Name",id:"ID",cores:"Cores",memory:"Memory",adminPassword:"Admin Password",monitorAddress:"Monitor Address",monitorSecret:"Monitor Secret",systemDisk:"System Disk",dataDisk:"Data Disk",ethernetAddress:"Ethernet Address",internalAddress:"Internal Address",allocatedAddress:"Allocated Address",externalAddress:"External Address",operatingSystem:"Operating System",autoStartup:"Auto Startup",enable:"Enable",disable:"Disable",pool:"Host Pool",cell:"Host Cell",owner:"Owner",group:"Group",cpuPriority:"CPU Priority",iops:"Disk IOPS",bandwidth:"Inbound/Outbound Bandwidth",noLimit:"No Limit",cpuPriorityHigh:"High",cpuPriorityMedium:"Medium",cpuPriorityLow:"Low",createdTime:"Created Time",disabledWhenRunning:"Disabled When Running",disabledWhenStopped:"Disabled When Stopped",status:"Status",running:"Running",stopped:"Stopped",display:"Display",hide:"Hide",modify:"Modify",extendDisk:"Extend Disk Size",shrinkDisk:"Shrink Disk Size",resetSecret:"Reset Monitor Secret"},cn:{title:"\u4e91\u4e3b\u673a\u8be6\u60c5 ",back:"\u8fd4\u56de",name:"\u4e3b\u673a\u540d",id:"ID",cores:"\u6838\u5fc3\u6570",memory:"\u5185\u5b58",adminPassword:"\u7ba1\u7406\u5458\u5bc6\u7801",monitorAddress:"\u76d1\u63a7\u5730\u5740",monitorSecret:"\u76d1\u63a7\u5bc6\u7801",systemDisk:"\u7cfb\u7edf\u78c1\u76d8",dataDisk:"\u6570\u636e\u78c1\u76d8",ethernetAddress:"MAC\u5730\u5740",internalAddress:"\u5185\u90e8\u5730\u5740",allocatedAddress:"\u5df2\u5206\u914d\u5730\u5740",externalAddress:"\u5916\u90e8\u5730\u5740",operatingSystem:"\u64cd\u4f5c\u7cfb\u7edf",autoStartup:"\u5f00\u673a\u542f\u52a8",enable:"\u542f\u7528",disable:"\u672a\u542f\u7528",pool:"\u6240\u5c5e\u8d44\u6e90\u6c60",cell:"\u627f\u8f7d\u8d44\u6e90\u8282\u70b9",owner:"\u6240\u5c5e\u7528\u6237",group:"\u6240\u5c5e\u7528\u6237\u7ec4",cpuPriority:"CPU\u4f18\u5148\u7ea7",iops:"\u78c1\u76d8 IOPS",bandwidth:"\u4e0b/\u4e0a\u884c\u5e26\u5bbd",noLimit:"\u65e0\u9650\u5236",cpuPriorityHigh:"\u9ad8",cpuPriorityMedium:"\u4e2d",cpuPriorityLow:"\u4f4e",createdTime:"\u521b\u5efa\u65f6\u95f4",disabledWhenRunning:"\u8fd0\u884c\u65f6\u7981\u7528",disabledWhenStopped:"\u505c\u673a\u65f6\u7981\u7528",status:"\u72b6\u6001",running:"\u8fd0\u884c\u4e2d",stopped:"\u5df2\u505c\u673a",display:"\u663e\u793a",hide:"\u9690\u85cf",modify:"\u4fee\u6539",extendDisk:"\u6269\u5c55\u78c1\u76d8\u5bb9\u91cf",shrinkDisk:"\u7f29\u51cf\u78c1\u76d8\u7a7a\u95f4",resetSecret:"\u91cd\u7f6e\u76d1\u63a7\u5bc6\u7801"}};function Yi(e){var a=e.match.params.id,t=s.a.useState(null),o=Object(n.a)(t,2),l=o[0],r=o[1],c=s.a.useState(null),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(!1),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(!1),E=Object(n.a)(g,2),h=E[0],v=E[1],y=s.a.useState(!1),x=Object(n.a)(y,2),k=x[0],C=x[1],S=s.a.useState(!1),O=Object(n.a)(S,2),j=O[0],w=O[1],_=s.a.useState(!1),N=Object(n.a)(_,2),R=N[0],T=N[1],I=s.a.useState(!1),D=Object(n.a)(I,2),A=D[0],P=D[1],F=s.a.useState(!1),B=Object(n.a)(F,2),M=B[0],z=B[1],W=s.a.useState(!1),q=Object(n.a)(W,2),H=q[0],L=q[1],U=s.a.useState(!1),V=Object(n.a)(U,2),G=V[0],$=V[1],Z=s.a.useState(!1),Y=Object(n.a)(Z,2),J=Y[0],Q=Y[1],X=s.a.useState(!1),ee=Object(n.a)(X,2),ae=ee[0],te=ee[1],ne=s.a.useState(0),oe=Object(n.a)(ne,2),le=oe[0],re=oe[1],ce=s.a.useState("warning"),ie=Object(n.a)(ce,2),se=ie[0],ue=ie[1],me=s.a.useState(""),de=Object(n.a)(me,2),pe=de[0],be=de[1],ge=function(){be("")},Ee=s.a.useCallback((function(e){ue("warning"),be(e),setTimeout(ge,3e3)}),[ue,be]),he=function(e){ue("info"),be(e),Wa(e),setTimeout(ge,3e3)},ve=s.a.useCallback((function(e){Ee(e)}),[Ee]),xe=s.a.useCallback((function(){xa(a,(function(e){r(e)}),ve)}),[a,ve]),ke=function(){v(!1)},Se=function(){C(!1)},je=function(){w(!1)},we=function(){T(!1)},_e=function(e){P(!0),re(e)},Ne=function(){P(!1)},Re=function(e){z(!0),re(e)},Te=function(){z(!1)},Ie=function(){L(!1)},De=function(){$(!1)},Ae=function(){Q(!1)},Pe=function(){te(!0)},Fe=function(){te(!1)},Be=function(){xe()},Me=function(e){Ee("set auto start fail: "+e)};s.a.useEffect((function(){xe()}),[xe]);var ze,We,qe=e.lang,He=Zi[qe],Le=[];if(null===l)ze=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}}),We="";else{var Ue,Ve={label:He.disabledWhenRunning,icon:vi.a},Ge={label:He.disabledWhenStopped,icon:vi.a};Ue=l.display_protocol?l.display_protocol+"://"+l.internal.display_address:"vnc://"+l.internal.display_address;var $e=[l.qos&&l.qos.send_speed&&0!==l.qos.receive_speed?ma(l.qos.receive_speed):He.noLimit,l.qos&&l.qos.receive_speed&&0!==l.qos.send_speed?ma(l.qos.send_speed):He.noLimit].join(" / "),Ze=[];u?Ze.push({label:He.hide,icon:xi.a,onClick:function(){return m(null)}}):Ze.push({label:He.display,icon:ul.a,onClick:function(){wa(a,(function(e,a){m(a||'no password configured for user "'+e+'"')}),ve)}}),l.running?Ze.push({label:He.modify,icon:Yo.a,onClick:function(){T(!0)}}):Ze.push(Ge);var Ye,Je=[{title:He.name,value:l.name,operators:l.running?[Ve]:[{label:He.modify,icon:Yo.a,onClick:function(){v(!0)}}]},{title:He.id,value:a},{title:He.cores,value:l.cores,operators:l.running?[Ve]:[{label:He.modify,icon:Yo.a,onClick:function(){C(!0)}}]},{title:He.memory,value:ua(l.memory),operators:l.running?[Ve]:[{label:He.modify,icon:Yo.a,onClick:function(){w(!0)}}]},{title:He.status,value:l.running?He.running:He.stopped},{title:He.ethernetAddress,value:l.ethernet_address},{title:He.createdTime,value:l.create_time},{title:He.adminPassword,value:u||"****************",operators:Ze},{title:He.monitorAddress,value:l.internal?Ue:""},{title:He.monitorSecret,value:f?l.monitor_secret:new Array(l.monitor_secret.length).fill("*"),operators:f?[{label:He.hide,icon:xi.a,onClick:function(){return b(!1)}},{label:He.resetSecret,icon:wi.a,onClick:Pe}]:[{label:He.display,icon:ul.a,onClick:function(){return b(!0)}},{label:He.resetSecret,icon:wi.a,onClick:Pe}]},{title:He.systemDisk,value:ua(l.disks[0]),operators:l.running?[Ve]:[{label:He.extendDisk,icon:Ci.a,onClick:function(){return _e(0)}},{label:He.shrinkDisk,icon:Oi.a,onClick:function(){return Re(0)}}]}];if(l.disks.length>1)for(var Ke=function(){var e=Xe;Je.push({title:He.dataDisk+Xe.toString(),value:ua(l.disks[Xe]),operators:l.running?[Ve]:[{label:He.extendDisk,icon:Ci.a,onClick:function(){return _e(e)}},{label:He.shrinkDisk,icon:Oi.a,onClick:function(){return Re(e)}}]})},Xe=1;Xe<l.disks.length;Xe++)Ke();if(l.qos&&l.qos.cpu_priority)switch(l.qos.cpu_priority){case"high":Ye=He.cpuPriorityHigh;break;case"medium":Ye=He.cpuPriorityMedium;break;case"low":Ye=He.cpuPriorityLow;break;default:Ye="invalid priority "+l.qos.cpu_priority}else Ye=He.noLimit;Je=Je.concat([{title:He.internalAddress,value:l.internal&&l.internal.network_address?l.internal.network_address:""},{title:He.allocatedAddress,value:l.internal&&l.internal.allocated_address?l.internal.allocated_address:""},{title:He.externalAddress,value:l.external&&l.external.network_address?l.external.network_address:""},{title:He.operatingSystem,value:l.system},{title:He.autoStartup,value:s.a.createElement("div",null,He.disable,s.a.createElement(Oe.a,{checked:l.auto_start,onChange:function(e){var t=e.target.checked;e.preventDefault(),function(e,a,t,n){$a("/guests/"+e+"/auto_start",{enable:a},(function(){t(e)}),n)}(a,t,Be,Me)},color:"primary"}),He.enable)},{title:He.pool,value:l.pool},{title:He.cell,value:l.cell},{title:He.cpuPriority,value:Ye,operators:[{label:He.modify,icon:Yo.a,onClick:function(){L(!0)}}]},{title:He.iops,value:l.qos&&l.qos.write_iops?l.qos.write_iops:He.noLimit,operators:l.running?[Ve]:[{label:He.modify,icon:Yo.a,onClick:function(){$(!0)}}]},{title:He.bandwidth,value:$e,operators:[{label:He.modify,icon:Yo.a,onClick:function(){Q(!0)}}]}]),ze=s.a.createElement(Ei.a,{maxWidth:"md"},s.a.createElement(Dc.a,{component:ye.a},s.a.createElement(hn.a,null,s.a.createElement(xn.a,null,Je.map((function(e,a){return s.a.createElement(yn.a,{key:a},s.a.createElement(kn.a,{component:"th",scope:"row"},e.title),s.a.createElement(kn.a,null,e.value),s.a.createElement(kn.a,null,e.operators?e.operators.map((function(e,a){var t=e.label,n=e.icon,o=e.onClick;return s.a.createElement(jn,{key:a,label:t,icon:n,onClick:o})})):""))})))))),We=He.title+l.name,Le=[s.a.createElement(fe,{size:"sm",color:"info",round:!0,href:"/admin/instances/"},s.a.createElement(il.a,null),He.back)]}return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{display:"flex"},Le.map((function(e,a){return s.a.createElement(K.a,{key:a,pl:2,pr:2},e)})))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12,sm:12,md:12},s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},We),s.a.createElement(dn,null,ze))),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:se,message:pe,open:""!==pe,closeNotification:ge,close:!0})),s.a.createElement(Qe,null,s.a.createElement(Ni,{lang:qe,open:h,instanceID:a,current:l,onSuccess:function(e){ke(),he("name of "+a+" changed to "+e),xe()},onCancel:ke})),s.a.createElement(Qe,null,s.a.createElement(Ti,{lang:qe,open:k,instanceID:a,current:l,onSuccess:function(e){Se(),he("cores of "+a+" changed to "+e),xe()},onCancel:Se})),s.a.createElement(Qe,null,s.a.createElement(Di,{lang:qe,open:j,instanceID:a,current:l,onSuccess:function(e){je(),he("memory of "+a+" changed to "+ua(e)),xe()},onCancel:je})),s.a.createElement(Qe,null,s.a.createElement(Pi,{lang:qe,open:R,instanceID:a,onSuccess:function(e){we(),he("password of "+e+" modified"),xe()},onCancel:we})),s.a.createElement(Qe,null,s.a.createElement(Bi,{lang:qe,open:A,instanceID:a,current:l,index:le,onSuccess:function(e,a){Ne(),he("size of disk "+e+" changed to "+ua(a)),xe()},onCancel:Ne})),s.a.createElement(Qe,null,s.a.createElement(zi,{lang:qe,open:M,instanceID:a,current:l,index:le,onSuccess:function(e){Te(),he("size of disk "+e+" shrunk"),xe()},onCancel:Te})),s.a.createElement(Qe,null,s.a.createElement(qi,{lang:qe,open:H,instanceID:a,current:l,onSuccess:function(e){Ie(),he("CPU priority changed to "+e),xe()},onCancel:Ie})),s.a.createElement(Qe,null,s.a.createElement(Li,{lang:qe,open:G,instanceID:a,current:l,onSuccess:function(e){De(),he("Disk IOPS changed to "+e),xe()},onCancel:De})),s.a.createElement(Qe,null,s.a.createElement(Vi,{lang:qe,open:J,instanceID:a,current:l,onSuccess:function(e,a){Ae();var t=[ma(e),ma(a)].join("/");he("network bandwidth changed to "+t),xe()},onCancel:Ae})),s.a.createElement($i,{lang:qe,open:ae,guestID:a,guestName:l?l.name:"",onSuccess:function(){Fe(),he("monitor secret reset"),xe()},onCancel:Fe}))}var Ji=t(608),Qi=Object(E.a)(Object(E.a)({},Yn),{},{cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),Ki=Object(g.a)(Qi),Xi={en:{createButton:"Create New Instance",batchCreate:"Batch Create",batchDelete:"Batch Delete",batchStop:"Batch Stop",enterBatch:"Enter Batch Mode",exitBatch:"Exit Batch Mode",tableTitle:"Cloud Instances",name:"Name",cell:"Host Cell",address:"Address",core:"Core",memory:"Memory",disk:"Disk",status:"Status",operates:"Operates",noResource:"No instances available",pool:"Compute Pool",disabled:"Disabled",offline:"Offline",allCells:"All Cells",allPools:"All Pools",keyword:"Key Word",search:"Search",notMatch:"No instance match keyword: "},cn:{createButton:"\u521b\u5efa\u4e91\u4e3b\u673a",batchCreate:"\u6279\u91cf\u521b\u5efa",batchDelete:"\u6279\u91cf\u5220\u9664",batchStop:"\u6279\u91cf\u505c\u6b62",enterBatch:"\u8fdb\u5165\u6279\u91cf\u6a21\u5f0f",exitBatch:"\u9000\u51fa\u6279\u91cf\u6a21\u5f0f",tableTitle:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",name:"\u540d\u79f0",cell:"\u627f\u8f7d\u8282\u70b9",address:"\u5730\u5740",core:"\u6838\u5fc3",memory:"\u5185\u5b58",disk:"\u78c1\u76d8",status:"\u72b6\u6001",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u4e91\u4e3b\u673a\u5b9e\u4f8b",pool:"\u8ba1\u7b97\u8d44\u6e90\u6c60",disabled:"\u5df2\u7981\u7528",offline:"\u79bb\u7ebf",allCells:"\u6240\u6709\u8282\u70b9",allPools:"\u6240\u6709\u8d44\u6e90\u6c60",keyword:"\u5173\u952e\u8bcd",search:"\u641c\u7d22",notMatch:"\u6ca1\u6709\u4e91\u4e3b\u673a\u5339\u914d\u5173\u952e\u8bcd: "}};function es(e){var a=Ki(),t=Object(b.g)(),o=new URLSearchParams(t.search),l=o.get("pool"),r=o.get("cell"),c=s.a.useState(!1),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(null),p=Object(n.a)(d,2),f=p[0],g=p[1],h=s.a.useState(new Map),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(!1),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState({pool:"",cell:""}),w=Object(n.a)(j,2),_=w[0],N=w[1],R=s.a.useState([]),T=Object(n.a)(R,2),I=T[0],D=T[1],A=s.a.useState([]),P=Object(n.a)(A,2),F=P[0],B=P[1],M=s.a.useState(0),z=Object(n.a)(M,2),W=z[0],q=z[1],H=s.a.useState(""),L=Object(n.a)(H,2),U=L[0],V=L[1],G=s.a.useState(""),$=Object(n.a)(G,2),Z=$[0],Y=$[1],J=s.a.useState(0),Q=Object(n.a)(J,2),X=Q[0],ee=Q[1],ae=s.a.useState(!1),te=Object(n.a)(ae,2),ne=te[0],oe=te[1],le=s.a.useState(!1),re=Object(n.a)(le,2),ce=re[0],ie=re[1],se=s.a.useState(!1),ue=Object(n.a)(se,2),me=ue[0],de=ue[1],pe=s.a.useState(!1),be=Object(n.a)(pe,2),ge=be[0],he=be[1],ve=s.a.useState(!1),ye=Object(n.a)(ve,2),xe=ye[0],ke=ye[1],Oe=s.a.useState(!1),_e=Object(n.a)(Oe,2),Ne=_e[0],Re=_e[1],Te=s.a.useState(!1),Ie=Object(n.a)(Te,2),De=Ie[0],Ae=Ie[1],Fe=s.a.useState(!1),Be=Object(n.a)(Fe,2),Me=Be[0],ze=Be[1],We=s.a.useState(!1),qe=Object(n.a)(We,2),He=qe[0],Le=qe[1],Ue=s.a.useState(!1),Ve=Object(n.a)(Ue,2),Ge=Ve[0],$e=Ve[1],Ze=s.a.useState(""),Ye=Object(n.a)(Ze,2),Je=Ye[0],Ke=Ye[1],Xe=s.a.useState(""),ea=Object(n.a)(Xe,2),aa=ea[0],ta=ea[1],na=s.a.useState(""),oa=Object(n.a)(na,2),la=oa[0],ra=oa[1],ua=s.a.useState("warning"),ma=Object(n.a)(ua,2),da=ma[0],pa=ma[1],ga=s.a.useState(""),Ea=Object(n.a)(ga,2),ha=Ea[0],va=Ea[1],ya=function(){va("")},xa=s.a.useCallback((function(e){pa("warning"),va(e),setTimeout(ya,3e3)}),[pa,va]),ka=function(e){pa("info"),va(e),Wa(e),setTimeout(ya,3e3)},Ca=function(){V(""),Y(""),q(0)},Sa=s.a.useCallback((function(){!function(e,a,t,n,o,l,r){var c={};c.limit=e||20,a&&(c.offset=a),t&&(c.pool=t),n&&(c.cell=n),o&&(c.keyword=o),Ua("/search/guests/",c,l,r)}(10,W,_.pool,_.cell,U,(function(e){var a=e.result,t=y,n=!1;if(ee(e.total),a){g(a);var o=[];t.forEach((function(e,t){a.some((function(e){return e.id===t}))||o.push(t)})),a.forEach((function(e){var a=e.id;t.has(a)||(t.set(a,!1),n||(n=!0))})),0!==o.length&&o.forEach((function(e){t.delete(e),n||(n=!0)}))}else g([]),0!==t.size&&(t.clear(),n=!0);n&&x(new Map(t))}),(function(e){xa(e),ia()}))}),[_,y,xa,W,U]),Oa=function(e){ie(!0),Ke(e)},ja=function(){ie(!1)},wa=function(e){de(!0),Ke(e)},_a=function(){de(!1)},Na=function(e){he(!0),Ke(e)},Ra=function(){he(!1)},Ta=function(e){ke(!0),Ke(e)},Ia=function(){ke(!1)},Da=function(e){Re(!0),Ke(e)},Aa=function(){Re(!1)},Pa=function(e,a,t){Ae(!0),Ke(e),ta(a),ra(t)},Fa=function(){Ae(!1)},Ba=function(){oe(!1)},Ma=function(){ze(!1)},za=function(){Le(!1)},qa=function(){$e(!1)},Ha=function(){Sa()},La=function(e,a){var t=new Map(y);t.set(a,e),x(t)};if(s.a.useEffect((function(){if(u){var e=!0;Sa();var a=setInterval((function(){e&&Sa()}),5e3);return function(){e=!1,clearInterval(a)}}fa((function(e){var a=[];e.forEach((function(e){a.push(e.name)})),0!==a.length?(D(a),B([]),N({pool:l||"",cell:r||""}),m(!0)):xa("no compute pools available")}),xa)}),[Sa,u,xa,l,r]),null===ca())return sa();var Va,Ga=e.lang,$a=Xi[Ga];if(u&&f)if(0===f.length)Va=""!==U?s.a.createElement(K.a,{textAlign:"center"},s.a.createElement(fn,null,$a.notMatch+U)):s.a.createElement(K.a,{textAlign:"center"},s.a.createElement(fn,null,$a.noResource));else{var Za,Ya,Ja,Qa;if(Za=S?s.a.createElement(K.a,{display:"flex",alignItems:"center"},s.a.createElement(K.a,null,s.a.createElement(Pe.a,{onChange:function(e){var a,t=e.target.checked,n=new Map,o=Object(Or.a)(y.keys());try{for(o.s();!(a=o.n()).done;){var l=a.value;n.set(l,t)}}catch(r){o.e(r)}finally{o.f()}x(n)}})),s.a.createElement(K.a,null,$a.name)):$a.name,X>10)Ja=0===X%10?X/10:(X-X%10)/10+1,Qa=0===W%10?W/10+1:(W-W%10)/10+1,Ya=s.a.createElement(K.a,{justifyContent:"center",display:"flex"},s.a.createElement(K.a,{m:1},s.a.createElement(Ji.a,{count:Ja,page:Qa,onChange:function(e,a){q(10*(a-1))},color:"primary",boundaryCount:3,showFirstButton:!0,showLastButton:!0})));else Ya=s.a.createElement("div",null);Va=s.a.createElement("div",null,s.a.createElement(Dr,{color:"primary",headers:[Za,$a.cell,$a.address,$a.core,$a.memory,$a.disk,$a.status,$a.operates],rows:f.map((function(e){var a=e.id;return s.a.createElement(mc,{key:e.id,instance:e,lang:Ga,checked:!(!y||!y.has(a))&&y.get(a),checkable:S,onCheckStatusChanged:La,onNotify:ka,onError:xa,onDelete:Oa,onMediaStart:wa,onInsertMedia:Na,onResetSystem:Ta,onBuildImage:Da,onStatusChange:Ha,onMigrateInstance:Pa})}))}),Ya)}else Va=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});var Ka=[{label:$a.createButton,icon:Tt.a,color:"info",onClick:function(){oe(!0)}},{label:$a.batchCreate,icon:Nr.a,color:"info",onClick:function(){$e(!0)}}];S?Ka.push({label:$a.batchDelete,icon:Wt.a,color:"danger",onClick:function(){Le(!0)}},{label:$a.batchStop,icon:wr.a,color:"info",onClick:function(){ze(!0)}},{label:$a.exitBatch,icon:Tr.a,color:"success",onClick:function(){O(!1)}}):Ka.push({label:$a.enterBatch,icon:Qo.a,color:"info",onClick:function(){var e,a=new Map,t=Object(Or.a)(y.keys());try{for(t.s();!(e=t.n()).done;){var n=e.value;a.set(n,!1)}}catch(o){t.e(o)}finally{t.f()}x(a),O(!0)}});var Xa=[{label:$a.allPools,value:""}];I.forEach((function(e){Xa.push({label:e,value:e})}));var et=[{label:$a.allCells,value:""}];F.forEach((function(e){var a=e.name;e.address&&(a+="("+e.address+")"),e.alive||(a+="-"+$a.offline),e.enabled||(a+="-"+$a.disabled),et.push({label:a,value:e.name})}));var at=s.a.createElement(K.a,{m:0,pb:2},s.a.createElement(je.a,{htmlFor:"pool",className:a.cardCategory},$a.pool),s.a.createElement(Se.a,{value:_.pool,onChange:function(e){var a=e.target.value,t=[];ba(a,(function(e){e.forEach((function(e){t.push(e)})),B(t),N({pool:a,cell:""}),Ca()}),xa)},inputProps:{name:"pool",id:"pool"},fullWidth:!0},Xa.map((function(e,a){return s.a.createElement(Ee.a,{value:e.value,key:a},e.label)})))),tt=s.a.createElement(K.a,{m:0,pb:2},s.a.createElement(je.a,{htmlFor:"cell",className:a.cardCategory},$a.cell),s.a.createElement(Se.a,{value:_.cell,onChange:function(e){var a=e.target.value;N((function(e){return Object(E.a)(Object(E.a)({},e),{},{cell:a})})),Ca()},inputProps:{name:"cell",id:"cell"},fullWidth:!0},et.map((function(e,a){return s.a.createElement(Ee.a,{value:e.value,key:a},e.label)})))),nt=[];return y&&(y.forEach((function(e,a){e&&nt.push(a)})),nt.sort()),s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{display:"flex"},Ka.map((function(e,a){var t=e.label,n=e.color,o=e.icon,l=e.onClick;return s.a.createElement(K.a,{key:a,m:1},s.a.createElement(fe,{size:"sm",color:n,round:!0,onClick:l},s.a.createElement(o),t))})))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{ml:1},s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:5,sm:3,md:2},s.a.createElement(K.a,{pt:1},at)),s.a.createElement(Qe,{xs:6,sm:4,md:2},s.a.createElement(K.a,{pt:1},tt)),s.a.createElement(Qe,{xs:10,sm:6,md:4},s.a.createElement(we.a,{value:Z,label:$a.keyword,onChange:function(e){var a=e.target.value;Y(a)},margin:"normal",fullWidth:!0})),s.a.createElement(Qe,{xs:2,sm:1},s.a.createElement(K.a,{pt:4},s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){Y(""),V(Z),q(0),Sa()}},$a.search)))))),s.a.createElement(Qe,{xs:12},s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",{className:a.cardTitleWhite},$a.tableTitle)),s.a.createElement(dn,null,Va))),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:da,message:ha,open:""!==ha,closeNotification:ya,close:!0})),s.a.createElement(Qe,null,s.a.createElement(kc,{lang:Ga,open:ne,onSuccess:function(e){Ba(),ka("new instance "+e+" created"),Sa()},onCancel:Ba})),s.a.createElement(Qe,null,s.a.createElement(pc,{lang:Ga,instanceID:Je,open:ce,onSuccess:function(e){ja(),ka("instance "+e+" deleted"),Sa()},onCancel:ja})),s.a.createElement(Qe,null,s.a.createElement(Sc,{lang:Ga,instanceID:Je,open:me,onSuccess:function(e){_a(),ka("instance "+e+" started with media"),Sa()},onCancel:_a})),s.a.createElement(Qe,null,s.a.createElement(jc,{lang:Ga,instanceID:Je,open:ge,onSuccess:function(e){Ra(),ka("instance "+e+" started with media"),Sa()},onCancel:Ra})),s.a.createElement(Qe,null,s.a.createElement(Rc,{lang:Ga,instanceID:Je,open:xe,onSuccess:function(e){Ia(),ka("guest system of "+e+" reset")},onCancel:Ia})),s.a.createElement(Qe,null,s.a.createElement(_c,{lang:Ga,instanceID:Je,open:Ne,onSuccess:function(e){Aa(),ka("new image "+e+" created from "+Je)},onCancel:Aa})),s.a.createElement(Qe,null,s.a.createElement(Ic,{lang:Ga,instanceID:Je,sourcePool:aa,sourceCell:la,open:De,onSuccess:function(e){Fa(),ka("instance "+e+" migrated"),Sa()},onCancel:Fa})),s.a.createElement(Qe,null,s.a.createElement(Pc,{lang:Ga,open:Me,targets:Me?nt:[],onSuccess:function(){Ma(),Sa()},onCancel:Ma})),s.a.createElement(Qe,null,s.a.createElement(Bc,{lang:Ga,open:He,targets:He?nt:[],onSuccess:function(){za(),Sa()},onCancel:za})),s.a.createElement(Qe,null,s.a.createElement(zc,{lang:Ga,open:Ge,onSuccess:function(){qa(),Sa()},onCancel:qa})))}var as=t(200),ts=t.n(as),ns=t(201),os=t.n(ns),ls={en:{title:"Remove Security Policy Rule",content:"Are you sure to remove ",content2:"th rule",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u5b89\u5168\u89c4\u5219",content:"\u662f\u5426\u5220\u9664\u7b2c ",content2:"\u4e2a\u5b89\u5168\u89c4\u5219",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function rs(e){var a=e.lang,t=e.open,o=e.guestID,l=e.index,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!0),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(""),f=Object(n.a)(p,2),b=f[0],g=f[1],E=ls[a],h=E.title,v=function(e){d(!0),g(e)},y=function(){d(!0),g(""),r(o,l)},x=E.content+(l+1)+E.content2,k=[{color:"transparent",label:E.cancel,onClick:function(){g(""),c()}},{color:"info",label:E.confirm,onClick:function(){d(!1),function(e,a,t,n){Za("/guests/"+e+"/security_policy/rules/"+a,(function(){t(e,a)}),n)}(o,l,y,v)}}];return s.a.createElement(oa,{size:"xs",open:t,prompt:b,title:h,buttons:k,content:x,operatable:m})}var cs={en:{title:"Add Security Policy Rule",action:"Action",accept:"Accept",reject:"Reject",protocol:"Protocol",sourceAddress:"Source Address",targetPort:"Target Port",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6dfb\u52a0\u5b89\u5168\u89c4\u5219",action:"\u5904\u7406",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",protocol:"\u534f\u8bae",sourceAddress:"\u6765\u6e90\u5730\u5740",targetPort:"\u76ee\u6807\u7aef\u53e3",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function is(e){var a={action:"accept",protocol:"",port:""},t=e.lang,o=e.guestID,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!0),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(""),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(a),v=Object(n.a)(h,2),y=v[0],x=v[1],k=cs[t],C=k.title,S=[{label:k.accept,value:"accept"},{label:k.reject,value:"reject"}],O=function(e){d(!0),g(e)},j=function(){g(""),x(a)},w=function(e){d(!0),j(),r(e)},_=function(e){return function(a){var t=a.target.value;x((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}},N=[{type:"radio",label:k.action,onChange:_("action"),value:y.action,options:S,required:!0,oneRow:!0,xs:10,sm:8,md:6},{type:"select",label:k.protocol,onChange:_("protocol"),value:y.protocol,options:[{label:"TCP",value:"tcp"},{label:"UDP",value:"udp"}],required:!0,oneRow:!0,xs:8,sm:6,md:4},{type:"text",label:k.targetPort,onChange:_("port"),value:y.port,required:!0,oneRow:!0,xs:8,sm:6,md:4}],R=s.a.createElement(ze,{inputs:N}),T=[{color:"transparent",label:k.cancel,onClick:function(){j(),c()}},{color:"info",label:k.confirm,onClick:function(){if(d(!1),y.action)if(y.protocol)if(y.port){var e=Number.parseInt(y.port);Number.isNaN(e)?O("invalid target port: "+y.port):function(e,a,t,n,o,l){Ua("/guests/"+e+"/security_policy/rules/",{action:a,protocol:t,to_port:n},(function(){o(e)}),l)}(o,y.action,y.protocol,e,w,O)}else O("must specify target port");else O("must specify protocol");else O("must specify action")}}];return s.a.createElement(oa,{size:"sm",open:l,prompt:b,title:C,buttons:T,content:R,operatable:m})}var ss={en:{title:"Modify Security Policy Rule",action:"Action",accept:"Accept",reject:"Reject",protocol:"Protocol",sourceModifyress:"Source Address",targetPort:"Target Port",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u5b89\u5168\u89c4\u5219",action:"\u5904\u7406",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",protocol:"\u534f\u8bae",sourceModifyress:"\u6765\u6e90\u5730\u5740",targetPort:"\u76ee\u6807\u7aef\u53e3",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function us(e){var a=e.lang,t=e.guestID,o=e.rule,l=e.open,r=e.onSuccess,c=e.onCancel,i={action:o.action,protocol:o.protocol,port:o.to_port},u=s.a.useState(!0),m=Object(n.a)(u,2),d=m[0],p=m[1],f=s.a.useState(""),b=Object(n.a)(f,2),g=b[0],h=b[1],v=s.a.useState(!1),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState(!1),S=Object(n.a)(C,2),O=S[0],j=S[1],w=s.a.useState(i),_=Object(n.a)(w,2),N=_[0],R=_[1],T=ss[a],I=T.title,D=[{label:T.accept,value:"accept"},{label:T.reject,value:"reject"}],A=s.a.useCallback((function(e){x&&(p(!0),h(e))}),[x]),P=function(){h(""),R(i),j(!1)},F=function(e){p(!0),P(),r(e)},B=function(e){return function(a){var t=a.target.value;R((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}};s.a.useEffect((function(){if(t&&o)return k(!0),R({action:o.action,protocol:o.protocol,port:o.to_port}),j(!0),function(){k(!1)}}),[x,l,t,o,A]);var M,z=[{color:"transparent",label:T.cancel,onClick:function(){P(),c()}}];if(O){var W=[{type:"radio",label:T.action,onChange:B("action"),value:N.action,options:D,required:!0,oneRow:!0,xs:10,sm:8,md:6},{type:"select",label:T.protocol,onChange:B("protocol"),value:N.protocol,options:[{label:"TCP",value:"tcp"},{label:"UDP",value:"udp"}],required:!0,oneRow:!0,xs:8,sm:6,md:4},{type:"text",label:T.targetPort,onChange:B("port"),value:N.port,required:!0,oneRow:!0,xs:8,sm:6,md:4}];M=s.a.createElement(ze,{inputs:W}),z.push({color:"info",label:T.confirm,onClick:function(){if(p(!1),N.action)if(N.protocol)if(N.port){var e=Number.parseInt(N.port);Number.isNaN(e)?A("invalid target port: "+N.port):function(e,a,t,n,o,l,r){$a("/guests/"+e+"/security_policy/rules/"+a,{action:t,protocol:n,to_port:o},(function(){l(e,a)}),r)}(t,o.index,N.action,N.protocol,e,F,A)}else A("must specify target port");else A("must specify protocol");else A("must specify action")}})}else M=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:l,prompt:g,title:I,buttons:z,content:M,operatable:d})}var ms=Object(E.a)(Object(E.a)({},U),{},{cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),ds=Object(g.a)(ms),ps={en:{createButton:"Add New Rule",tableTitle:"Security Policy Rules",rule:"Rule",action:"Action",protocol:"Protocol",sourceAddress:"Source Address",targetPort:"Target Port",default:"Default Action",accept:"Accept",reject:"Reject",operates:"Operates",noResource:"No security policy available",modify:"Modify",remove:"Remove",moveUp:"Move Up",moveDown:"Move Down",back:"Back"},cn:{createButton:"\u6dfb\u52a0\u65b0\u89c4\u5219",tableTitle:"\u5b89\u5168\u89c4\u5219",rule:"\u89c4\u5219",action:"\u5904\u7406",protocol:"\u534f\u8bae",sourceAddress:"\u6765\u6e90\u5730\u5740",targetPort:"\u76ee\u6807\u7aef\u53e3",default:"\u9ed8\u8ba4\u5904\u7406",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u5b89\u5168\u7b56\u7565\u7ec4",modify:"\u4fee\u6539",remove:"\u79fb\u9664",moveUp:"\u4e0a\u79fb",moveDown:"\u4e0b\u79fb",back:"\u8fd4\u56de"}};function fs(e){var a,t=e.match.params.id,o=e.lang,l=ps[o],r=ds(),c=s.a.useState(!1),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(null),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(!1),E=Object(n.a)(g,2),h=E[0],v=E[1],y=s.a.useState(!1),x=Object(n.a)(y,2),k=x[0],C=x[1],S=s.a.useState(!1),O=Object(n.a)(S,2),j=O[0],w=O[1],_=s.a.useState(""),N=Object(n.a)(_,2),R=N[0],T=N[1],I=s.a.useState("warning"),D=Object(n.a)(I,2),A=D[0],P=D[1],F=s.a.useState(""),B=Object(n.a)(F,2),M=B[0],z=B[1],W=function(){z("")},q=s.a.useCallback((function(e){if(u){P("warning"),z(e),setTimeout(W,3e3)}}),[P,z,u]),H=s.a.useCallback((function(){u&&function(e,a,t){Ha("/guests/"+e+"/security_policy/",a,t)}(t,b,q)}),[t,q,u]),L=function(e){if(u){P("info"),z(e),Wa(e),setTimeout(W,3e3)}},U=function(){C(!1)},V=function(){w(!1)},G=function(){v(!1)},$=function(e){!function(e,a,t,n){$a("/guests/"+e+"/security_policy/rules/"+a+"/order",{direction:"up"},(function(){t(e,a)}),n)}(t,e.index,(function(e,a){L(a+"th rule moved up"),H()}),q)},Z=function(e){!function(e,a,t,n){$a("/guests/"+e+"/security_policy/rules/"+a+"/order",{direction:"down"},(function(){t(e,a)}),n)}(t,e.index,(function(e,a){L(a+"th rule moved down"),H()}),q)};if(s.a.useEffect((function(){return m(!0),H(),function(){m(!1)}}),[H]),null===f)a=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});else{var Y=s.a.createElement(Te.a,{component:"fieldset",fullWidth:!0,disabled:!0},s.a.createElement(Ne.a,{name:"type",value:f.default_action,onChange:function(e){var a=e.target.value;!function(e,a,t,n){$a("/guests/"+e+"/security_policy/default_action",{action:a},(function(){t(e)}),n)}(t,a,(function(){L("default action changed to "+a),H()}),q)},row:!0},s.a.createElement(K.a,{display:"flex",alignItems:"center"},s.a.createElement(K.a,null,s.a.createElement(Re.a,{value:"accept",control:s.a.createElement(_e.a,null),label:l.accept})),s.a.createElement(K.a,null,s.a.createElement(Re.a,{value:"reject",control:s.a.createElement(_e.a,null),label:l.reject}))))),J=[[l.default,Y]];f.rules&&0!==f.rules.length?f.rules.forEach((function(e,a){var t={index:a,action:e.action,protocol:e.protocol,to_port:e.to_port},n=[{onClick:function(e){return function(e){C(!0),T(e)}(t)},icon:Yo.a,label:l.modify},{onClick:function(e){return function(e){w(!0),T(e)}(t)},icon:Wt.a,label:l.remove}];f.rules.length-1!==a&&n.push({onClick:function(e){return Z(t)},icon:ts.a,label:l.moveDown}),0!==a&&n.push({onClick:function(e){return $(t)},icon:os.a,label:l.moveUp}),J.push(function(e,a,t){var n=t.map((function(e,a){return s.a.createElement(jn,{label:e.label,icon:e.icon,onClick:e.onClick,key:a})})),o=a.action,c=a.protocol,i=a.from_address,u=a.to_port;return[e,"accept"===o?s.a.createElement(te.a,{title:l.accept,placement:"top"},s.a.createElement(At.a,{className:r.successText})):s.a.createElement(te.a,{title:l.reject,placement:"top"},s.a.createElement(Ft.a,{className:r.dangerText})),c,i,u,n]}(a,e,n))})):J.push([s.a.createElement(K.a,{display:"flex",justifyContent:"center"},s.a.createElement(fn,null,l.noResource))]),a=s.a.createElement(On,{color:"primary",headers:[l.rule,l.action,l.protocol,l.sourceAddress,l.targetPort,l.operates],rows:J})}var Q=[{href:"/admin/instances/",icon:il.a,label:l.back},{onClick:function(){v(!0)},icon:Tt.a,label:l.createButton}];return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12,sm:12,md:12},s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12,sm:6,md:4},s.a.createElement(K.a,{display:"flex"},Q.map((function(e,a){return e.href?s.a.createElement(K.a,{p:1,key:a},s.a.createElement(fe,{size:"sm",color:"info",round:!0,href:e.href},s.a.createElement(e.icon),e.label)):s.a.createElement(K.a,{p:1,key:a},s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:e.onClick},s.a.createElement(e.icon),e.label))})))))),s.a.createElement(Qe,{xs:12,sm:12,md:12},s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",{className:r.cardTitleWhite},l.tableTitle)),s.a.createElement(dn,null,a))),s.a.createElement(En,{place:"tr",color:A,message:M,open:""!==M,closeNotification:W,close:!0}),s.a.createElement(is,{lang:o,open:h,guestID:t,onSuccess:function(){G(),L("new security policy rule added"),H()},onCancel:G}),s.a.createElement(us,{lang:o,open:k,guestID:t,rule:R,onSuccess:function(e){U(),L(e+"th rule modified"),H()},onCancel:U}),s.a.createElement(rs,{lang:o,open:j,guestID:t,index:R.index,onSuccess:function(e,a){V(),L(a+"the rule removed"),H()},onCancel:V}))}var bs=t(202),gs=t.n(bs),Es=t(305),hs=t.n(Es),vs=t(607),ys=t(605),xs={cardTitle:{float:"left",padding:"10px 10px 10px 0px",lineHeight:"24px"},cardTitleRTL:{float:"right",padding:"10px 0px 10px 10px !important"},displayNone:{display:"none !important"},tabsRoot:{minHeight:"unset !important",overflowX:"visible","& $tabRootButton":{fontSize:"0.875rem"}},tabRootButton:{minHeight:"unset !important",minWidth:"unset !important",width:"unset !important",height:"unset !important",maxWidth:"unset !important",maxHeight:"unset !important",padding:"10px 15px",borderRadius:"3px",lineHeight:"24px",border:"0 !important",color:"#FFF !important",marginLeft:"4px","&:last-child":{marginLeft:"0px"}},tabSelected:{backgroundColor:"rgba("+h("#FFF")+", 0.2)",transition:"0.2s background-color 0.1s"},tabWrapper:{display:"inline-block",minHeight:"unset !important",minWidth:"unset !important",width:"unset !important",height:"unset !important",maxWidth:"unset !important",maxHeight:"unset !important",fontWeight:"500",fontSize:"12px",marginTop:"1px","& > svg,& > .material-icons":{verticalAlign:"middle",margin:"-1px 5px 0 0 !important"}}},ks=Object(g.a)(xs);function Cs(e){var a,t=s.a.useState(0),o=Object(n.a)(t,2),l=o[0],r=o[1],c=ks(),i=e.headerColor,u=e.plainTabs,m=e.tabs,d=e.title,p=e.rtlActive,f=Q()((a={},Object(Y.a)(a,c.cardTitle,!0),Object(Y.a)(a,c.cardTitleRTL,p),a));return s.a.createElement(nn,{plain:u},s.a.createElement(cn,{color:i,plain:u},void 0!==d?s.a.createElement("div",{className:f},d):null,s.a.createElement(vs.a,{value:l,onChange:function(e,a){r(a)},classes:{root:c.tabsRoot,indicator:c.displayNone,scrollButtons:c.displayNone},variant:"scrollable",scrollButtons:"auto"},m.map((function(e,a){var t={};return e.tabIcon&&(t={icon:s.a.createElement(e.tabIcon,null)}),s.a.createElement(ys.a,Object.assign({classes:{root:c.tabRootButton,selected:c.tabSelected,wrapper:c.tabWrapper},key:a,label:e.tabName},t))})))),s.a.createElement(dn,null,m.map((function(e,a){return a===l?s.a.createElement("div",{key:a},e.tabContent):null}))))}var Ss=t(304),Os=t.n(Ss),js=t(121),ws=t.n(js),_s={en:{title:"Create User",user:"Username",password:"Password",password2:"Confirm Password",nick:"Nickname",mail:"Mail",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u521b\u5efa\u7528\u6237",user:"\u7528\u6237\u540d",password:"\u5bc6\u7801",password2:"\u786e\u8ba4\u5bc6\u7801",nick:"\u6635\u79f0",mail:"\u90ae\u7bb1",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Ns(e){var a,t={user:"",password:"",password2:"",nick:"",mail:""},o=e.lang,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(""),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(t),f=Object(n.a)(p,2),b=f[0],g=f[1],h=_s[o],v=function(e){d(e)},y=function(){d(""),g(t)},x=function(e){y(),r(e)},k=function(e){return function(a){var t=a.target.value;g((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}},C=s.a.createElement(De.a,{container:!0},s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:6,md:4},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:h.user,onChange:k("user"),value:b.user,margin:"normal",required:!0,fullWidth:!0})))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:10,md:8},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:h.password,onChange:k("password"),value:b.password,margin:"normal",type:"password",required:!0,fullWidth:!0})))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:10,md:8},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:h.password2,onChange:k("password2"),value:b.password2,margin:"normal",type:"password",required:!0,fullWidth:!0})))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:10,md:8},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:h.nick,onChange:k("nick"),value:b.nick,margin:"normal",fullWidth:!0})))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:10,md:8},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:h.mail,onChange:k("mail"),value:b.mail,margin:"normal",fullWidth:!0})))));return a=m&&""!==m?s.a.createElement(Qe,{xs:12},s.a.createElement(na,{message:m,color:"danger"})):s.a.createElement(Qe,{xs:12}),s.a.createElement(We.a,{open:l,"aria-labelledby":h.title,maxWidth:"sm",fullWidth:!0},s.a.createElement(Le.a,null,h.title),s.a.createElement(He.a,null,s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},C),a)),s.a.createElement(qe.a,null,s.a.createElement(fe,{onClick:function(){y(),c()},color:"transparent",autoFocus:!0},h.cancel),s.a.createElement(fe,{onClick:function(){var e=new RegExp("[^\\w-.]");b.user?e.test(b.user)?v("only letter/digit/'-'/'_'/'.' allowed in username"):b.password?b.password2===b.password?function(e,a,t,n,o,l){var r="/users/"+e,c={password:a};t&&(c.nick=t),n&&(c.mail=n),Ua(r,c,(function(){o(e)}),l)}(b.user,b.password,b.nick,b.mail,x,v):v("password mismatch"):v("please input password"):v("must specify user name")},color:"info"},h.confirm)))}var Rs={en:{title:"Modify User",user:"Username",nick:"Nickname",mail:"Mail",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u7528\u6237",user:"\u7528\u6237\u540d",nick:"\u6635\u79f0",mail:"\u90ae\u7bb1",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Ts(e){var a={user:"",nick:"",mail:""},t=e.lang,o=e.name,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!1),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(""),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(a),v=Object(n.a)(h,2),y=v[0],x=v[1],k=Rs[t],C=function(e){g(e)},S=function(){g(""),x(a),d(!1)},O=function(e){S(),r(e)};s.a.useEffect((function(){if(o&&l&&!m){!function(e,a,t){Ha("/users/"+e,a,t)}(o,(function(e){x({user:o,nick:e.nick?e.nick:"",mail:e.mail?e.mail:""}),d(!0)}),C)}}),[m,l,o]);var j,w,_=function(e){return function(a){var t=a.target.value;x((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}};return j=m?s.a.createElement(De.a,{container:!0},s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:6,md:4},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:k.user,value:y.user,margin:"normal",disabled:!0,required:!0,fullWidth:!0})))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:10,md:8},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:k.nick,onChange:_("nick"),value:y.nick,margin:"normal",fullWidth:!0})))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:10,md:8},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:k.mail,onChange:_("mail"),value:y.mail,margin:"normal",fullWidth:!0}))))):s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}}),w=b&&""!==b?s.a.createElement(Qe,{xs:12},s.a.createElement(na,{message:b,color:"danger"})):s.a.createElement(Qe,{xs:12}),s.a.createElement(We.a,{open:l,"aria-labelledby":k.title,maxWidth:"sm",fullWidth:!0},s.a.createElement(Le.a,null,k.title),s.a.createElement(He.a,null,s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},j),w)),s.a.createElement(qe.a,null,s.a.createElement(fe,{onClick:function(){S(),c()},color:"transparent",autoFocus:!0},k.cancel),s.a.createElement(fe,{onClick:function(){!function(e,a,t,n,o){var l="/users/"+e,r={};a&&(r.nick=a),t&&(r.mail=t),$a(l,r,(function(){n(e)}),o)}(y.user,y.nick,y.mail,O,C)},color:"info"},k.confirm)))}var Is={en:{title:"Delete User",content:"Are you sure to delete user ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u7528\u6237",content:"\u662f\u5426\u5220\u9664\u7528\u6237 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Ds(e){var a,t=e.lang,o=e.name,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(""),u=Object(n.a)(i,2),m=u[0],d=u[1],p=Is[t],f=function(e){d(e)},b=function(e){d(""),r(e)};return a=m&&""!==m?s.a.createElement(Qe,{xs:12},s.a.createElement(na,{message:m,color:"danger"})):s.a.createElement(Qe,{xs:12}),s.a.createElement(We.a,{open:l,"aria-labelledby":p.title,maxWidth:"xs",fullWidth:!0},s.a.createElement(Le.a,null,p.title),s.a.createElement(He.a,null,s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},p.content+o),a)),s.a.createElement(qe.a,null,s.a.createElement(fe,{onClick:function(){d(""),c()},color:"transparent",autoFocus:!0},p.cancel),s.a.createElement(fe,{onClick:function(){!function(e,a,t){Za("/users/"+e,(function(){a(e)}),t)}(o,b,f)},color:"info"},p.confirm)))}var As=Object(E.a)({},Cn),Ps=Object(g.a)(As),Fs={en:{createButton:"Create New User",name:"User Name",modify:"Modify",delete:"Delete",operates:"Operates",noResource:"No User Available"},cn:{createButton:"\u521b\u5efa\u65b0\u7528\u6237",name:"\u7528\u6237\u540d",modify:"\u4fee\u6539",delete:"\u5220\u9664",operates:"\u64cd\u4f5c",noResource:"\u5c1a\u672a\u521b\u5efa\u7528\u6237"}};function Bs(e){var a=Ps(),t=s.a.useState(null),o=Object(n.a)(t,2),l=o[0],r=o[1],c=s.a.useState(!1),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(!1),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(!1),E=Object(n.a)(g,2),h=E[0],v=E[1],y=s.a.useState(""),x=Object(n.a)(y,2),k=x[0],C=x[1],S=s.a.useState("warning"),O=Object(n.a)(S,2),j=O[0],w=O[1],_=s.a.useState(""),N=Object(n.a)(_,2),R=N[0],T=N[1],I=function(){T("")},D=s.a.useCallback((function(e){w("warning"),T(e),setTimeout(I,3e3)}),[w,T]),A=s.a.useCallback((function(){Ha("/users/",r,(function(e){D(e),ia()}))}),[D]),P=function(e){w("info"),T(e),Wa(e),setTimeout(I,3e3)},F=function(){v(!1)},B=function(){b(!1)},M=function(){m(!1)};s.a.useEffect((function(){A()}),[A]);var z=ca();if(null===z)return sa();var W,q=e.lang,H=Fs[q];W=null===l?s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}}):0===l.length?s.a.createElement(fn,null,H.noResource):s.a.createElement(Dr,{color:"primary",headers:[H.name,H.operates],rows:l.map((function(e,t){var n=e,o=[s.a.createElement(jn,{key:"modify",label:H.modify,icon:ws.a,onClick:function(){return function(e){v(!0),C(e)}(n)}})];return n!==z.user&&o.push(s.a.createElement(jn,{key:"delete",label:H.delete,icon:Wt.a,onClick:function(){return function(e){b(!0),C(e)}(n)}})),s.a.createElement(yn.a,{className:a.tableBodyRow,key:t},s.a.createElement(kn.a,{className:a.tableCell},e),s.a.createElement(kn.a,{className:a.tableCell},o))}))});var L=[s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){m(!0)}},s.a.createElement(Os.a,null),H.createButton)];return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{display:"flex"},L.map((function(e,a){return s.a.createElement(K.a,{key:a,m:1},e)})))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12},s.a.createElement(Ei.a,{maxWidth:"sm"},W)),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:j,message:R,open:""!==R,closeNotification:I,close:!0})),s.a.createElement(Qe,null,s.a.createElement(Ns,{lang:q,open:u,onSuccess:function(e){M(),P("user "+e+" created"),A()},onCancel:M})),s.a.createElement(Qe,null,s.a.createElement(Ts,{lang:q,name:k,open:h,onSuccess:function(e){F(),P("user "+e+" modified"),A()},onCancel:F})),s.a.createElement(Qe,null,s.a.createElement(Ds,{lang:q,name:k,open:f,onSuccess:function(e){B(),P("user "+e+" deleted"),A()},onCancel:B})))}var Ms={en:{title:"Add Group",name:"Group Name",display:"Display Name",role:"Roles",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6dfb\u52a0\u7528\u6237\u7ec4",name:"\u7ec4\u540d\u79f0",display:"\u663e\u793a\u540d\u79f0",role:"\u89d2\u8272\u6e05\u5355",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function zs(e){var a={name:"",display:"",checked:new Set},t=e.lang,o=e.open,l=e.onSuccess,r=e.onCancel,c=Ms[t],i=s.a.useState([]),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!1),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(""),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(a),C=Object(n.a)(k,2),S=C[0],O=C[1],j=function(e){x(e)},w=function(){x(""),O(a),g(!1)},_=function(e){w(),l(e)};s.a.useEffect((function(){if(o&&!b){za((function(e){d(e),g(!0)}),j)}}),[b,o]);var N,R,T=function(e){return function(a){var t=a.target.value;O((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}},I=function(e){var a=e.target.value,t=e.target.checked,n=Object(E.a)({},S);t?n.checked.add(a):n.checked.delete(a),O(n)};return N=b?s.a.createElement(De.a,{container:!0},s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:6,md:4},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:c.name,onChange:T("name"),value:S.name,margin:"normal",required:!0,fullWidth:!0})))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:10,md:8},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:c.display,onChange:T("display"),value:S.display,margin:"normal",required:!0,fullWidth:!0})))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(Te.a,{component:"fieldset",fullWidth:!0},s.a.createElement(Ie.a,{component:"legend"},c.role),s.a.createElement(Ae.a,null,s.a.createElement(De.a,{container:!0},m.map((function(e,a){var t;return t=!!S.checked.has(e),s.a.createElement(Qe,{xs:6,sm:3,key:a},s.a.createElement(Re.a,{control:s.a.createElement(Pe.a,{checked:t,onChange:I,value:e}),label:e}))}))))))))):s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}}),R=y&&""!==y?s.a.createElement(Qe,{xs:12},s.a.createElement(na,{message:y,color:"danger"})):s.a.createElement(Qe,{xs:12}),s.a.createElement(We.a,{open:o,"aria-labelledby":c.title,maxWidth:"md",fullWidth:!0},s.a.createElement(Le.a,null,c.title),s.a.createElement(He.a,null,s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},N),R)),s.a.createElement(qe.a,null,s.a.createElement(fe,{onClick:function(){w(),r()},color:"transparent",autoFocus:!0},c.cancel),s.a.createElement(fe,{onClick:function(){if(S.name)if(S.display)if(S.checked&&0!==S.checked.size){var e=[];m.forEach((function(a){S.checked.has(a)&&e.push(a)})),function(e,a,t,n,o){var l="/user_groups/"+e,r={display:a};t&&(r.role=t),Ua(l,r,(function(){n(e)}),o)}(S.name,S.display,e,_,j)}else j("Select at least one role");else j("Display name required");else j("Group name required")},color:"info"},c.confirm)))}var Ws={en:{title:"Modify Group",name:"Group Name",display:"Display Name",role:"Roles",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u7528\u6237\u7ec4",name:"\u7ec4\u540d\u79f0",display:"\u663e\u793a\u540d\u79f0",role:"\u89d2\u8272\u6e05\u5355",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function qs(e){var a={display:"",checked:new Set},t=e.lang,o=e.name,l=e.open,r=e.onSuccess,c=e.onCancel,i=Ws[t],u=s.a.useState([]),m=Object(n.a)(u,2),d=m[0],p=m[1],f=s.a.useState(!1),b=Object(n.a)(f,2),g=b[0],h=b[1],v=s.a.useState(""),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState(a),S=Object(n.a)(C,2),O=S[0],j=S[1],w=function(e){k(e)},_=function(){k(""),j(a),h(!1)},N=function(){_(),r(o)};s.a.useEffect((function(){if(o&&l&&!g){var e=[],a=function(a){var t=a.display,n=a.role,o=new Set;n.forEach((function(e){o.add(e)})),j({display:t,checked:o}),p(e),h(!0)};za((function(t){e=t,function(e,a,t){Ha("/user_groups/"+e,a,t)}(o,a,w)}),w)}}),[g,l,o]);var R,T,I=function(e){var a=e.target.value,t=e.target.checked,n=Object(E.a)({},O);t?n.checked.add(a):n.checked.delete(a),j(n)};return R=g?s.a.createElement(De.a,{container:!0},s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:6,md:4},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:i.name,value:o,margin:"normal",disabled:!0,required:!0,fullWidth:!0})))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:10,md:8},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:i.display,onChange:function(e){return function(a){var t=a.target.value;j((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}}("display"),value:O.display,margin:"normal",required:!0,fullWidth:!0})))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(Te.a,{component:"fieldset",fullWidth:!0},s.a.createElement(Ie.a,{component:"legend"},i.role),s.a.createElement(Ae.a,null,s.a.createElement(De.a,{container:!0},d.map((function(e,a){var t;return t=!!O.checked.has(e),s.a.createElement(Qe,{xs:6,sm:3,key:a},s.a.createElement(Re.a,{control:s.a.createElement(Pe.a,{checked:t,onChange:I,value:e}),label:e}))}))))))))):s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}}),T=x&&""!==x?s.a.createElement(Qe,{xs:12},s.a.createElement(na,{message:x,color:"danger"})):s.a.createElement(Qe,{xs:12}),s.a.createElement(We.a,{open:l,"aria-labelledby":i.title,maxWidth:"md",fullWidth:!0},s.a.createElement(Le.a,null,i.title),s.a.createElement(He.a,null,s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},R),T)),s.a.createElement(qe.a,null,s.a.createElement(fe,{onClick:function(){_(),c()},color:"transparent",autoFocus:!0},i.cancel),s.a.createElement(fe,{onClick:function(){if(O.checked&&0!==O.checked.size)if(O.display){var e=[];d.forEach((function(a){O.checked.has(a)&&e.push(a)})),function(e,a,t,n,o){var l="/user_groups/"+e,r={};a&&(r.display=a),t&&(r.role=t),$a(l,r,(function(){n(e)}),o)}(o,O.display,e,N,w)}else w("Display name required");else w("Select at least one role")},color:"info"},i.confirm)))}var Hs={en:{title:"Remove Group",content:"Are you sure to remove group ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u7528\u6237\u7ec4",content:"\u662f\u5426\u5220\u9664\u7528\u6237\u7ec4 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Ls(e){var a,t=e.lang,o=e.name,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(""),u=Object(n.a)(i,2),m=u[0],d=u[1],p=Hs[t],f=function(e){d(e)},b=function(e){d(""),r(e)};return a=m&&""!==m?s.a.createElement(Qe,{xs:12},s.a.createElement(na,{message:m,color:"danger"})):s.a.createElement(Qe,{xs:12}),s.a.createElement(We.a,{open:l,"aria-labelledby":p.title,maxWidth:"xs",fullWidth:!0},s.a.createElement(Le.a,null,p.title),s.a.createElement(He.a,null,s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},p.content+o),a)),s.a.createElement(qe.a,null,s.a.createElement(fe,{onClick:function(){d(""),c()},color:"transparent",autoFocus:!0},p.cancel),s.a.createElement(fe,{onClick:function(){!function(e,a,t){Za("/user_groups/"+e,(function(){a(e)}),t)}(o,b,f)},color:"info"},p.confirm)))}var Us=Object(E.a)({},Cn),Vs=Object(g.a)(Us),Gs={en:{createButton:"Add New Group",name:"Group Name",display:"Display Name",modify:"Modify",remove:"Remove",member:"Members",operates:"Operates",noResource:"No Group Available"},cn:{createButton:"\u521b\u5efa\u65b0\u7528\u6237\u7ec4",name:"\u7528\u6237\u7ec4\u540d",display:"\u663e\u793a\u540d\u79f0",modify:"\u4fee\u6539",remove:"\u5220\u9664",member:"\u6210\u5458",operates:"\u64cd\u4f5c",noResource:"\u5c1a\u672a\u521b\u5efa\u7528\u6237\u7ec4"}};function $s(e){var a=e.lang,t=e.setGroup,o=Vs(),l=s.a.useState(null),r=Object(n.a)(l,2),c=r[0],i=r[1],u=s.a.useState(!1),m=Object(n.a)(u,2),d=m[0],p=m[1],f=s.a.useState(!1),b=Object(n.a)(f,2),g=b[0],E=b[1],h=s.a.useState(!1),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(""),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState("warning"),w=Object(n.a)(j,2),_=w[0],N=w[1],R=s.a.useState(""),T=Object(n.a)(R,2),I=T[0],D=T[1],A=function(){D("")},P=s.a.useCallback((function(e){N("warning"),D(e),setTimeout(A,3e3)}),[N,D]),F=s.a.useCallback((function(){Ha("/user_groups/",i,(function(e){P(e),ia()}))}),[P]),B=function(e){N("info"),D(e),Wa(e),setTimeout(A,3e3)},M=function(){x(!1)},z=function(){E(!1)},W=function(){p(!1)};s.a.useEffect((function(){F()}),[F]);var q=ca();if(null===q)return sa();var H,L=Gs[a];H=null===c?s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}}):0===c.length?s.a.createElement(fn,null,L.noResource):s.a.createElement(Dr,{color:"primary",headers:[L.name,L.display,L.member,L.operates],rows:c.map((function(e,a){var n=e.name,l=[s.a.createElement(jn,{key:"modify",label:L.modify,icon:ws.a,onClick:function(){return e=n,x(!0),void O(e);var e}}),s.a.createElement(jn,{key:"member",label:L.member,icon:gs.a,onClick:function(){return t(n)}})];return n!==q.group&&l.push(s.a.createElement(jn,{key:"remove",label:L.remove,icon:Wt.a,onClick:function(){return e=n,E(!0),void O(e);var e}})),s.a.createElement(yn.a,{className:o.tableBodyRow,key:a},s.a.createElement(kn.a,{className:o.tableCell},n),s.a.createElement(kn.a,{className:o.tableCell},e.display),s.a.createElement(kn.a,{className:o.tableCell},e.member.toString()),s.a.createElement(kn.a,{className:o.tableCell},l))}))});var U=[s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){p(!0)}},s.a.createElement(Tt.a,null),L.createButton)];return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{display:"flex"},U.map((function(e,a){return s.a.createElement(K.a,{key:a,m:1},e)})))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12},s.a.createElement(Ei.a,{maxWidth:"sm"},H)),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:_,message:I,open:""!==I,closeNotification:A,close:!0})),s.a.createElement(Qe,null,s.a.createElement(zs,{lang:a,open:d,onSuccess:function(e){W(),B("new group "+e+" added"),F()},onCancel:W})),s.a.createElement(Qe,null,s.a.createElement(qs,{lang:a,name:S,open:y,onSuccess:function(e){M(),B("group "+e+" modified"),F()},onCancel:M})),s.a.createElement(Qe,null,s.a.createElement(Ls,{lang:a,name:S,open:g,onSuccess:function(e){z(),B("group "+e+" removed"),F()},onCancel:z})))}var Zs={en:{title:"Add Group Member ",name:"User",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6dfb\u52a0\u65b0\u6210\u5458 ",name:"\u7528\u6237\u540d",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Ys(e){var a,t,o,l={member:""},r=e.lang,c=e.group,i=e.open,u=e.onSuccess,m=e.onCancel,d=s.a.useState(!1),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(""),h=Object(n.a)(g,2),v=h[0],y=h[1],x=s.a.useState(l),k=Object(n.a)(x,2),C=k[0],S=k[1],O=s.a.useState([]),j=Object(n.a)(O,2),w=j[0],_=j[1],N=Zs[r],R=function(e){y(e)},T=function(){y(""),S(l),b(!1)},I=function(e){T(),u(e,c)};return s.a.useEffect((function(){if(i&&!f){!function(e,a,t){var n="/user_search/";e&&(n+="?group="+e),Ha(n,a,t)}(null,(function(e){0!==e.length?(_(e),b(!0)):R("no unallocated users available")}),R)}}),[f,i]),a=f?s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:8},s.a.createElement(K.a,{m:1,p:2},s.a.createElement(je.a,{htmlFor:"member"},N.name),s.a.createElement(Se.a,{value:C.member,onChange:(t="member",function(e){var a=e.target.value;S((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},t,a))}))}),inputProps:{name:"member",id:"member"},fullWidth:!0},w.map((function(e){return s.a.createElement(Ee.a,{value:e,key:e},e)})))))):s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}}),o=v&&""!==v?s.a.createElement(Qe,{xs:12},s.a.createElement(na,{message:v,color:"danger"})):s.a.createElement(Qe,{xs:12}),s.a.createElement(We.a,{open:i,"aria-labelledby":N.title,maxWidth:"sm",fullWidth:!0},s.a.createElement(Le.a,null,N.title),s.a.createElement(He.a,null,s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},a),o)),s.a.createElement(qe.a,null,s.a.createElement(fe,{onClick:function(){T(),m()},color:"transparent",autoFocus:!0},N.cancel),s.a.createElement(fe,{onClick:function(){C.member&&""!==C.member||R("must select an user"),function(e,a,t,n){Ua("/user_groups/"+e+"/members/"+a,{},(function(){t(a,e)}),n)}(c,C.member,I,R)},color:"info"},N.confirm)))}var Js={en:{title:"Remove Group Member",content:"Are you sure to remove member ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u6210\u5458",content:"\u662f\u5426\u5220\u9664\u7528\u6237\u7ec4\u6210\u5458 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Qs(e){var a,t=e.lang,o=e.group,l=e.member,r=e.open,c=e.onSuccess,i=e.onCancel,u=s.a.useState(""),m=Object(n.a)(u,2),d=m[0],p=m[1],f=Js[t],b=function(e){p(e)},g=function(){p(""),c(l,o)};return a=d&&""!==d?s.a.createElement(Qe,{xs:12},s.a.createElement(na,{message:d,color:"danger"})):s.a.createElement(Qe,{xs:12}),s.a.createElement(We.a,{open:r,"aria-labelledby":f.title,maxWidth:"xs",fullWidth:!0},s.a.createElement(Le.a,null,f.title),s.a.createElement(He.a,null,s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},f.content+l),a)),s.a.createElement(qe.a,null,s.a.createElement(fe,{onClick:function(){p(""),i()},color:"transparent",autoFocus:!0},f.cancel),s.a.createElement(fe,{onClick:function(){!function(e,a,t,n){Za("/user_groups/"+e+"/members/"+a,(function(){t(a,e)}),n)}(o,l,g,b)},color:"info"},f.confirm)))}var Ks=Object(E.a)({},Cn),Xs=Object(g.a)(Ks),eu={en:{addButton:"Add Group Member",backButton:"Back",remove:"Remove",member:"Member",operates:"Operates",noResource:"No Member Available"},cn:{addButton:"\u589e\u52a0\u65b0\u6210\u5458",backButton:"\u8fd4\u56de",remove:"\u5220\u9664",member:"\u6210\u5458",operates:"\u64cd\u4f5c",noResource:"\u5c1a\u672a\u6dfb\u52a0\u6210\u5458"}};function au(e){var a=e.lang,t=e.group,o=e.onBack,l=Xs(),r=s.a.useState(null),c=Object(n.a)(r,2),i=c[0],u=c[1],m=s.a.useState(!1),d=Object(n.a)(m,2),p=d[0],f=d[1],b=s.a.useState(!1),g=Object(n.a)(b,2),E=g[0],h=g[1],v=s.a.useState(""),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState("warning"),S=Object(n.a)(C,2),O=S[0],j=S[1],w=s.a.useState(""),_=Object(n.a)(w,2),N=_[0],R=_[1],T=function(){R("")},I=s.a.useCallback((function(e){j("warning"),R(e),setTimeout(T,3e3)}),[j,R]),D=s.a.useCallback((function(){!function(e,a,t){Ha("/user_groups/"+e+"/members/",a,t)}(t,u,(function(e){I(e),ia()}))}),[I,t]),A=function(e){j("info"),R(e),Wa(e),setTimeout(T,3e3)},P=function(){h(!1)},F=function(){f(!1)};if(s.a.useEffect((function(){D()}),[D]),null===ca())return sa();var B,M=eu[a];B=null===i?s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}}):0===i.length?s.a.createElement(fn,null,M.noResource):s.a.createElement(Dr,{color:"primary",headers:[M.member,M.operates],rows:i.map((function(e,a){var t=[s.a.createElement(jn,{key:"remove",label:M.remove,icon:Wt.a,onClick:function(){return a=e,h(!0),void k(a);var a}})];return s.a.createElement(yn.a,{className:l.tableBodyRow,key:a},s.a.createElement(kn.a,{className:l.tableCell},e),s.a.createElement(kn.a,{className:l.tableCell},t))}))});var z=[s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:o},s.a.createElement(il.a,null),M.backButton),s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){f(!0)}},s.a.createElement(Tt.a,null),M.addButton)];return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{display:"flex"},z.map((function(e,a){return s.a.createElement(K.a,{key:a,m:1},e)})))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12},s.a.createElement(Ei.a,{maxWidth:"sm"},B)),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:O,message:N,open:""!==N,closeNotification:T,close:!0})),s.a.createElement(Qe,null,s.a.createElement(Ys,{lang:a,group:t,open:p,onSuccess:function(e){F(),A("member "+e+" added"),D()},onCancel:F})),s.a.createElement(Qe,null,s.a.createElement(Qs,{lang:a,group:t,member:x,open:E,onSuccess:function(e){P(),A("member "+e+" removed"),D()},onCancel:P})))}var tu={en:{title:"Add Role",name:"Name",menu:"Menus",dashboard:"Dashboard",computePool:"Compute Pools",addressPool:"Address Pools",storagePool:"Storage Pools",instance:"Instances",diskImage:"Disk Image",mediaImage:"Media Image",user:"Users",log:"Logs",visibility:"Resource Visibility",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u65b0\u589e\u89d2\u8272",name:"\u540d\u79f0",menu:"\u53ef\u7528\u83dc\u5355",dashboard:"\u4eea\u8868\u76d8",computePool:"\u8ba1\u7b97\u8d44\u6e90\u6c60",addressPool:"\u5730\u5740\u8d44\u6e90\u6c60",storagePool:"\u5b58\u50a8\u8d44\u6e90\u6c60",instance:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",diskImage:"\u78c1\u76d8\u955c\u50cf",mediaImage:"\u5149\u76d8\u955c\u50cf",user:"\u7528\u6237",log:"\u65e5\u5fd7",visibility:"\u8d44\u6e90\u53ef\u89c1\u6027",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function nu(e){var a,t,o={name:"",checked:new Set},l=e.lang,r=e.open,c=e.onSuccess,i=e.onCancel,u=tu[l],m=pa(l),d=s.a.useState(""),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(o),h=Object(n.a)(g,2),v=h[0],y=h[1],x=function(e){b(e)},k=function(){b(""),y(o)},C=function(e){k(),c(e)},S=function(e){var a=e.target.value,t=e.target.checked,n=Object(E.a)({},v);t?n.checked.add(a):n.checked.delete(a),y(n)},O=s.a.createElement(De.a,{container:!0},s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:6,md:4},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:u.name,onChange:(a="name",function(e){var t=e.target.value;y((function(e){return Object(E.a)(Object(E.a)({},e),{},Object(Y.a)({},a,t))}))}),value:v.name,margin:"normal",required:!0,fullWidth:!0})))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(Te.a,{component:"fieldset",fullWidth:!0},s.a.createElement(Ie.a,{component:"legend"},u.menu),s.a.createElement(Ae.a,null,s.a.createElement(De.a,{container:!0},m.map((function(e,a){var t,n=e.value,o=e.label;return t=!!v.checked.has(n),s.a.createElement(Qe,{xs:6,sm:3,key:a},s.a.createElement(Re.a,{control:s.a.createElement(Pe.a,{checked:t,onChange:S,value:n}),label:o}))})))))))));return t=f&&""!==f?s.a.createElement(Qe,{xs:12},s.a.createElement(na,{message:f,color:"danger"})):s.a.createElement(Qe,{xs:12}),s.a.createElement(We.a,{open:r,"aria-labelledby":u.title,maxWidth:"md",fullWidth:!0},s.a.createElement(Le.a,null,u.title),s.a.createElement(He.a,null,s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},O),t)),s.a.createElement(qe.a,null,s.a.createElement(fe,{onClick:function(){k(),i()},color:"transparent",autoFocus:!0},u.cancel),s.a.createElement(fe,{onClick:function(){if(v.name)if(v.checked&&0!==v.checked.size){var e=[];m.forEach((function(a){v.checked.has(a.value)&&e.push(a.value)})),function(e,a,t,n){Ua("/roles/"+e,{menu:a},(function(){t(e)}),n)}(v.name,e,C,x)}else x("Select at least one menu item");else x("must specify role name")},color:"info"},u.confirm)))}var ou={en:{title:"Modify Role",name:"Name",menu:"Menus",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u89d2\u8272\u6743\u9650",name:"\u540d\u79f0",menu:"\u53ef\u7528\u83dc\u5355",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function lu(e){var a={checked:new Set},t=e.lang,o=e.name,l=e.open,r=e.onSuccess,c=e.onCancel,i=ou[t],u=pa(t),m=s.a.useState(!1),d=Object(n.a)(m,2),p=d[0],f=d[1],b=s.a.useState(""),g=Object(n.a)(b,2),h=g[0],v=g[1],y=s.a.useState(a),x=Object(n.a)(y,2),k=x[0],C=x[1],S=function(e){v(e)},O=function(){v(""),C(a),f(!1)},j=function(){O(),r(o)};s.a.useEffect((function(){if(o&&l&&!p){!function(e,a,t){Ha("/roles/"+e,a,t)}(o,(function(e){var a=e.menu,t=new Set;a.forEach((function(e){t.add(e)})),C({checked:t}),f(!0)}),S)}}),[p,l,o]);var w,_,N=function(e){var a=e.target.value,t=e.target.checked,n=Object(E.a)({},k);t?n.checked.add(a):n.checked.delete(a),C(n)};return w=p?s.a.createElement(De.a,{container:!0},s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12,sm:6,md:4},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:i.name,value:o,margin:"normal",disabled:!0,required:!0,fullWidth:!0})))),s.a.createElement(yc,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(Te.a,{component:"fieldset",fullWidth:!0},s.a.createElement(Ie.a,{component:"legend"},i.menu),s.a.createElement(Ae.a,null,s.a.createElement(De.a,{container:!0},u.map((function(e,a){var t,n=e.value,o=e.label;return t=!!k.checked.has(n),s.a.createElement(Qe,{xs:6,sm:3,key:a},s.a.createElement(Re.a,{control:s.a.createElement(Pe.a,{checked:t,onChange:N,value:n}),label:o}))}))))))))):s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}}),_=h&&""!==h?s.a.createElement(Qe,{xs:12},s.a.createElement(na,{message:h,color:"danger"})):s.a.createElement(Qe,{xs:12}),s.a.createElement(We.a,{open:l,"aria-labelledby":i.title,maxWidth:"md",fullWidth:!0},s.a.createElement(Le.a,null,i.title),s.a.createElement(He.a,null,s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},w),_)),s.a.createElement(qe.a,null,s.a.createElement(fe,{onClick:function(){O(),c()},color:"transparent",autoFocus:!0},i.cancel),s.a.createElement(fe,{onClick:function(){if(k.checked&&0!==k.checked.size){var e=[];u.forEach((function(a){k.checked.has(a.value)&&e.push(a.value)})),function(e,a,t,n){$a("/roles/"+e,{menu:a},(function(){t(e)}),n)}(o,e,j,S)}else S("Select at least one menu item")},color:"info"},i.confirm)))}var ru={en:{title:"Remove Role",content:"Are you sure to remove role ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u89d2\u8272",content:"\u662f\u5426\u5220\u9664\u89d2\u8272 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function cu(e){var a,t=e.lang,o=e.name,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(""),u=Object(n.a)(i,2),m=u[0],d=u[1],p=ru[t],f=function(e){d(e)},b=function(e){d(""),r(e)};return a=m&&""!==m?s.a.createElement(Qe,{xs:12},s.a.createElement(na,{message:m,color:"danger"})):s.a.createElement(Qe,{xs:12}),s.a.createElement(We.a,{open:l,"aria-labelledby":p.title,maxWidth:"xs",fullWidth:!0},s.a.createElement(Le.a,null,p.title),s.a.createElement(He.a,null,s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},p.content+o),a)),s.a.createElement(qe.a,null,s.a.createElement(fe,{onClick:function(){d(""),c()},color:"transparent",autoFocus:!0},p.cancel),s.a.createElement(fe,{onClick:function(){!function(e,a,t){Za("/roles/"+e,(function(){a(e)}),t)}(o,b,f)},color:"info"},p.confirm)))}var iu=Object(E.a)({},Cn),su=Object(g.a)(iu),uu={en:{createButton:"Add New Role",name:"Role Name",modify:"Modify",delete:"Remove",operates:"Operates",noResource:"No Role Available"},cn:{createButton:"\u589e\u52a0\u65b0\u89d2\u8272",name:"\u89d2\u8272\u540d",modify:"\u4fee\u6539",delete:"\u5220\u9664",operates:"\u64cd\u4f5c",noResource:"\u5c1a\u672a\u521b\u5efa\u89d2\u8272"}};function mu(e){var a=su(),t=s.a.useState(null),o=Object(n.a)(t,2),l=o[0],r=o[1],c=s.a.useState(!1),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(!1),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(!1),E=Object(n.a)(g,2),h=E[0],v=E[1],y=s.a.useState(""),x=Object(n.a)(y,2),k=x[0],C=x[1],S=s.a.useState("warning"),O=Object(n.a)(S,2),j=O[0],w=O[1],_=s.a.useState(""),N=Object(n.a)(_,2),R=N[0],T=N[1],I=function(){T("")},D=s.a.useCallback((function(e){w("warning"),T(e),setTimeout(I,3e3)}),[w,T]),A=s.a.useCallback((function(){za(r,(function(e){D(e),ia()}))}),[D]),P=function(e){w("info"),T(e),Wa(e),setTimeout(I,3e3)},F=function(){v(!1)},B=function(){b(!1)},M=function(){m(!1)};if(s.a.useEffect((function(){A()}),[A]),null===ca())return sa();var z,W=e.lang,q=uu[W];z=null===l?s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}}):0===l.length?s.a.createElement(fn,null,q.noResource):s.a.createElement(Dr,{color:"primary",headers:[q.name,q.operates],rows:l.map((function(e,t){var n=e,o=[s.a.createElement(jn,{key:"modify",label:q.modify,icon:ws.a,onClick:function(){return function(e){v(!0),C(e)}(n)}}),s.a.createElement(jn,{key:"remove",label:q.delete,icon:Wt.a,onClick:function(){return function(e){b(!0),C(e)}(n)}})];return s.a.createElement(yn.a,{className:a.tableBodyRow,key:t},s.a.createElement(kn.a,{className:a.tableCell},e),s.a.createElement(kn.a,{className:a.tableCell},o))}))});var H=[s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){m(!0)}},s.a.createElement(Tt.a,null),q.createButton)];return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{display:"flex"},H.map((function(e,a){return s.a.createElement(K.a,{key:a,m:1},e)})))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12},s.a.createElement(Ei.a,{maxWidth:"sm"},z)),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:j,message:R,open:""!==R,closeNotification:I,close:!0})),s.a.createElement(Qe,null,s.a.createElement(nu,{lang:W,open:u,onSuccess:function(e){M(),P("role "+e+" added"),A()},onCancel:M})),s.a.createElement(Qe,null,s.a.createElement(lu,{lang:W,name:k,open:h,onSuccess:function(e){F(),P("role "+e+" modified"),A()},onCancel:F})),s.a.createElement(Qe,null,s.a.createElement(cu,{lang:W,name:k,open:f,onSuccess:function(e){B(),P("role "+e+" removed"),A()},onCancel:B})))}var du={en:{title:"Permissions",user:"Users",group:"User Groups",role:"Roles"},cn:{title:"\u6743\u9650\u7ba1\u7406",user:"\u7528\u6237",group:"\u7528\u6237\u7ec4",role:"\u89d2\u8272"}},pu=function(e){var a=e.lang,t=s.a.useState(""),o=Object(n.a)(t,2),l=o[0],r=o[1];return l?s.a.createElement(au,{lang:a,group:l,onBack:function(){return r("")}}):s.a.createElement($s,{lang:a,setGroup:r})};var fu=Object(E.a)({},Cn),bu=Object(g.a)(fu),gu={en:{modify:"Modify",visibility:"Group Resource Visibility",description:"Description",instance:"Instances Visible",instanceDescription:"User can browse instances created by other users in the same group when enabled. Otherwise, an instance is only visible to its creator by default.",disk:"Disk Images Visible",diskDescription:"User can browse disk images created by other users in the same group when enabled. Otherwise, an image is only visible to its creator by default.",media:"Media Images Visible",mediaDescription:"User can browse media images created by other users in the same group when enabled. Otherwise, an image is only visible to its creator by default."},cn:{modify:"\u4fee\u6539",visibility:"\u7ec4\u8d44\u6e90\u53ef\u89c1\u6027",description:"\u63cf\u8ff0",instance:"\u4e91\u4e3b\u673a\u5b9e\u4f8b\u7ec4\u5185\u53ef\u89c1",instanceDescription:"\u52fe\u9009\u540e\uff0c\u7528\u6237\u53ef\u4ee5\u67e5\u770b\u540c\u4e00\u7ec4\u5185\u5176\u4ed6\u7528\u6237\u521b\u5efa\u7684\u4e91\u4e3b\u673a\u5b9e\u4f8b(\u9ed8\u8ba4\u4ec5\u5bf9\u521b\u5efa\u8005\u53ef\u89c1)",disk:"\u78c1\u76d8\u955c\u50cf\u7ec4\u5185\u53ef\u89c1",diskDescription:"\u52fe\u9009\u540e\uff0c\u7528\u6237\u53ef\u4ee5\u67e5\u770b\u540c\u4e00\u7ec4\u5185\u5176\u4ed6\u7528\u6237\u521b\u5efa\u7684\u78c1\u76d8\u955c\u50cf(\u9ed8\u8ba4\u4ec5\u5bf9\u521b\u5efa\u8005\u53ef\u89c1)",media:"\u5149\u76d8\u955c\u50cf\u7ec4\u5185\u53ef\u89c1",mediaDescription:"\u52fe\u9009\u540e\uff0c\u7528\u6237\u53ef\u4ee5\u67e5\u770b\u540c\u4e00\u7ec4\u5185\u5176\u4ed6\u7528\u6237\u521b\u5efa\u7684\u5149\u76d8\u955c\u50cf(\u9ed8\u8ba4\u4ec5\u5bf9\u521b\u5efa\u8005\u53ef\u89c1)"}},Eu=function(e){var a=e.checked,t=e.onChange,n=e.label,o=e.description,l=e.classes;return s.a.createElement(yn.a,{className:l.tableBodyRow},s.a.createElement(kn.a,{className:l.tableCell},s.a.createElement(K.a,{display:"flex",alignItems:"center"},s.a.createElement(K.a,null,s.a.createElement(Pe.a,{checked:a,onChange:t})),s.a.createElement(K.a,null,n))),s.a.createElement(kn.a,{className:l.tableCell},o))};var hu=t(306),vu=t.n(hu),yu={en:{title:"Delete Security Policy",content:"Are you sure to delete security policy ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u5b89\u5168\u7b56\u7565",content:"\u662f\u5426\u5220\u9664\u5b89\u5168\u7b56\u7565 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function xu(e){var a=e.lang,t=e.policyID,o=e.open,l=e.onSuccess,r=e.onCancel,c=s.a.useState(!0),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(""),p=Object(n.a)(d,2),f=p[0],b=p[1],g=yu[a],E=g.title,h=function(e){m(!0),b(e)},v=function(){m(!0),b(""),l(t)},y=g.content+t,x=[{color:"transparent",label:g.cancel,onClick:function(){b(""),r()}},{color:"info",label:g.confirm,onClick:function(){m(!1),function(e,a,t){Za("/security_policy_groups/"+e,(function(){a(e)}),t)}(t,v,h)}}];return s.a.createElement(oa,{size:"xs",open:o,prompt:f,title:E,buttons:x,content:y,operatable:u})}var ku={en:{title:"Create New Security Policy",name:"Name",description:"Description",enable:"Enable",enabled:"Enabled",disabled:"Disabled",global:"Global",yes:"Yes",no:"No",defaultAction:"Default Action",accept:"Accept",reject:"Reject",format:"only letter/digit/'_' allowed",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u521b\u5efa\u65b0\u5b89\u5168\u7b56\u7565",name:"\u540d\u79f0",description:"\u63cf\u8ff0",enable:"\u662f\u5426\u542f\u7528",enabled:"\u5df2\u542f\u7528",disabled:"\u5df2\u7981\u7528",global:"\u5168\u5c40\u53ef\u89c1",yes:"\u662f",no:"\u5426",defaultAction:"\u9ed8\u8ba4\u5904\u7406",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",format:"\u4ec5\u5141\u8bb8\u5b57\u6bcd\u6570\u5b57\u4e0e\u4e0b\u5212\u7ebf_",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Cu(e){var a={name:"",description:"",action:"accept",enabled:!0,global:!1},t=e.lang,o=e.open,l=e.onSuccess,r=e.onCancel,c=s.a.useState(!0),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(""),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(a),h=Object(n.a)(g,2),v=h[0],y=h[1],x=ku[t],k=x.title,C=[{label:x.accept,value:"accept"},{label:x.reject,value:"reject"}],S=function(e){m(!0),b(e)},O=function(){b(""),y(a)},j=function(e){m(!0),O(),l(e)},w=function(e){return function(a){var t=a.target.value;y((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}},_=function(e){return function(a){var t=a.target.checked;y((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}},N=[{type:"text",label:x.name,onChange:w("name"),value:v.name,required:!0,oneRow:!0,xs:8,sm:6,md:4},{type:"radio",label:x.defaultAction,onChange:w("action"),value:v.action,options:C,disabled:!0,oneRow:!0,xs:10,sm:8,md:6},{type:"switch",label:x.enable,onChange:_("enabled"),value:v.enabled,on:x.enabled,off:x.disabled,oneRow:!0,xs:6},{type:"switch",label:x.global,onChange:_("global"),value:v.global,on:x.yes,off:x.no,oneRow:!0,xs:6},{type:"textarea",label:x.description,onChange:w("description"),value:v.description,oneRow:!0,rows:3,xs:10}],R=s.a.createElement(ze,{inputs:N}),T=[{color:"transparent",label:x.cancel,onClick:function(){O(),r()}},{color:"info",label:x.confirm,onClick:function(){if(m(!1),v.action){var e=new RegExp("[^\\w]");v.name?e.test(v.name)?S(x.format):function(e,a,t,n,o,l,r){var c=ca();if(null!==c){Ua("/security_policy_groups/",{name:e,description:a,user:c.user,group:c.group,enabled:t,global:n,default_action:o},(function(e){var a=e.id;l(a)}),r)}else r("session expired")}(v.name,v.description,v.enabled,v.global,v.action,j,S):S("must specify policy name")}else S("must specify action")}}];return s.a.createElement(oa,{size:"sm",open:o,prompt:f,title:k,buttons:T,content:R,operatable:u})}var Su={en:{title:"Modify Security Policy",name:"Name",description:"Description",enable:"Enable",enabled:"Enabled",disabled:"Disabled",global:"Global",yes:"Yes",no:"No",defaultAction:"Default Action",accept:"Accept",reject:"Reject",format:"only letter/digit/'_' allowed",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u5b89\u5168\u7b56\u7565",name:"\u540d\u79f0",description:"\u63cf\u8ff0",enable:"\u662f\u5426\u542f\u7528",enabled:"\u5df2\u542f\u7528",disabled:"\u5df2\u7981\u7528",global:"\u5168\u5c40\u53ef\u89c1",yes:"\u662f",no:"\u5426",defaultAction:"\u9ed8\u8ba4\u5904\u7406",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",format:"\u4ec5\u5141\u8bb8\u5b57\u6bcd\u6570\u5b57\u4e0e\u4e0b\u5212\u7ebf_",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Ou(e){var a={name:"",description:"",action:"accept",enabled:!0,global:!1},t=e.lang,o=e.policyID,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!1),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(!0),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(""),v=Object(n.a)(h,2),y=v[0],x=v[1],k=s.a.useState(!1),C=Object(n.a)(k,2),S=C[0],O=C[1],j=s.a.useState(a),w=Object(n.a)(j,2),_=w[0],N=w[1],R=Su[t],T=R.title,I=[{label:R.accept,value:"accept"},{label:R.reject,value:"reject"}],D=s.a.useCallback((function(e){S&&(g(!0),x(e))}),[S]),A=function(){x(""),N(a),d(!1)},P=function(){g(!0),A(),r(o)},F=function(e){return function(a){var t=a.target.value;N((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}},B=function(e){return function(a){var t=a.target.checked;N((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}};s.a.useEffect((function(){if(o&&l){O(!0);return function(e,a,t){Ha("/security_policy_groups/"+e,a,t)}(o,(function(e){S&&(N({name:e.name,description:e.description,action:e.default_action,enabled:e.enabled,global:e.global}),d(!0))}),D),function(){O(!1)}}}),[S,l,o,D]);var M,z=[{color:"transparent",label:R.cancel,onClick:function(){A(),c()}}];if(m){var W=[{type:"text",label:R.name,onChange:F("name"),value:_.name,required:!0,oneRow:!0,xs:8,sm:6,md:4},{type:"radio",label:R.defaultAction,onChange:F("action"),value:_.action,options:I,disabled:!0,oneRow:!0,xs:10,sm:8,md:6},{type:"switch",label:R.enable,onChange:B("enabled"),value:_.enabled,on:R.enabled,off:R.disabled,oneRow:!0,xs:6},{type:"switch",label:R.global,onChange:B("global"),value:_.global,on:R.yes,off:R.no,oneRow:!0,xs:6},{type:"textarea",label:R.description,onChange:F("description"),value:_.description,oneRow:!0,rows:3,xs:10}];M=s.a.createElement(ze,{inputs:W}),z.push({color:"info",label:R.confirm,onClick:function(){if(g(!1),_.action){var e=new RegExp("[^\\w]");_.name?e.test(_.name)?D(R.format):function(e,a,t,n,o,l,r,c){var i="/security_policy_groups/"+e,s=ca();if(null!==s){$a(i,{name:a,description:t,user:s.user,group:s.group,enabled:n,global:o,default_action:l},(function(){r(e)}),c)}else c("session expired")}(o,_.name,_.description,_.enabled,_.global,_.action,P,D):D("must specify policy name")}else D("must specify action")}})}else M=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:l,prompt:y,title:T,buttons:z,content:M,operatable:b})}var ju=Object(g.a)({cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),wu={en:{createButton:"Create New Group",tableTitle:"Security Policy Groups",name:"Name",enable:"Enable",enabled:"Enabled",disabled:"Disabled",global:"Global",yes:"Yes",no:"No",defaultAction:"Default Action",accept:"Accept",reject:"Reject",operates:"Operates",noResource:"No security policy available",modify:"Modify",delete:"Delete",rules:"Rules"},cn:{createButton:"\u521b\u5efa\u65b0\u7b56\u7565\u7ec4",tableTitle:"\u5b89\u5168\u7b56\u7565\u7ec4",name:"\u540d\u79f0",enable:"\u662f\u5426\u542f\u7528",enabled:"\u5df2\u542f\u7528",disabled:"\u5df2\u7981\u7528",global:"\u5168\u5c40\u53ef\u89c1",yes:"\u662f",no:"\u5426",defaultAction:"\u9ed8\u8ba4\u5904\u7406",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u5b89\u5168\u7b56\u7565\u7ec4",modify:"\u4fee\u6539",delete:"\u5220\u9664",rules:"\u89c4\u5219"}};function _u(e){var a,t=e.lang,o=wu[t],l=ju(),r=s.a.useState(!1),c=Object(n.a)(r,2),i=c[0],u=c[1],m=s.a.useState(null),d=Object(n.a)(m,2),p=d[0],f=d[1],b=s.a.useState(!1),g=Object(n.a)(b,2),E=g[0],h=g[1],v=s.a.useState(!1),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState(!1),S=Object(n.a)(C,2),O=S[0],j=S[1],w=s.a.useState(""),_=Object(n.a)(w,2),N=_[0],R=_[1],T=s.a.useState("warning"),I=Object(n.a)(T,2),D=I[0],A=I[1],P=s.a.useState(""),F=Object(n.a)(P,2),B=F[0],M=F[1],z=function(){M("")},W=s.a.useCallback((function(e){if(i){A("warning"),M(e),setTimeout(z,3e3)}}),[A,M,i]),q=s.a.useCallback((function(){if(i){var e=ca();null!==e?Ma(e.user,e.group,!1,!1,f,W):W("session expired")}}),[W,i]),H=function(e){if(i){A("info"),M(e),Wa(e),setTimeout(z,3e3)}},L=function(){k(!1)},U=function(){j(!1)},V=function(){h(!1)};if(s.a.useEffect((function(){return u(!0),q(),function(){u(!1)}}),[q]),null===p)a=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});else if(0===p.length)a=s.a.createElement(K.a,{display:"flex",justifyContent:"center"},s.a.createElement(fn,null,o.noResource));else{var G=[];p.forEach((function(e){var a=[{onClick:function(a){return t=e.id,k(!0),void R(t);var t},icon:Yo.a,label:o.modify},{onClick:function(a){return t=e.id,j(!0),void R(t);var t},icon:Wt.a,label:o.delete},{href:"/admin/security_policies/"+e.id+"/rules/",icon:vu.a,label:o.rules}];G.push(function(e,a){var t=a.map((function(e,a){return s.a.createElement(jn,{label:e.label,icon:e.icon,onClick:e.onClick,href:e.href,key:a})})),n=e.name,l=e.default_action,r=e.enabled,c=e.global;return[n,"accept"===l?o.accept:o.reject,r?o.enabled:o.disabled,c?o.yes:o.no,t]}(e,a))})),a=s.a.createElement(On,{color:"primary",headers:[o.name,o.defaultAction,o.enable,o.global,o.operates],rows:G})}var $=[{onClick:function(){h(!0)},icon:Tt.a,label:o.createButton}];return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12},s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12,sm:6,md:4},s.a.createElement(K.a,{display:"flex"},$.map((function(e,a){return e.href?s.a.createElement(K.a,{p:1,key:a},s.a.createElement(fe,{size:"sm",color:"info",round:!0,href:e.href},s.a.createElement(e.icon),e.label)):s.a.createElement(K.a,{p:1,key:a},s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:e.onClick},s.a.createElement(e.icon),e.label))})))))),s.a.createElement(Qe,{xs:12},s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",{className:l.cardTitleWhite},o.tableTitle)),s.a.createElement(dn,null,a))),s.a.createElement(En,{place:"tr",color:D,message:B,open:""!==B,closeNotification:z,close:!0}),s.a.createElement(Cu,{lang:t,open:E,onSuccess:function(e){V(),H("new policy "+e+" created"),q()},onCancel:V}),s.a.createElement(Ou,{lang:t,open:x,policyID:N,onSuccess:function(e){L(),H("policy "+e+" modified"),q()},onCancel:L}),s.a.createElement(xu,{lang:t,open:O,policyID:N,onSuccess:function(e){U(),H("policy "+e+" deleted"),q()},onCancel:U}))}var Nu={en:{title:"Remove Security Policy Rule",content:"Are you sure to remove ",content2:"th rule",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u5b89\u5168\u89c4\u5219",content:"\u662f\u5426\u5220\u9664\u7b2c ",content2:"\u4e2a\u5b89\u5168\u89c4\u5219",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Ru(e){var a=e.lang,t=e.open,o=e.policyID,l=e.index,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!0),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(""),f=Object(n.a)(p,2),b=f[0],g=f[1],E=Nu[a],h=E.title,v=function(e){d(!0),g(e)},y=function(){d(!0),g(""),r(o,l)},x=E.content+(l+1)+E.content2,k=[{color:"transparent",label:E.cancel,onClick:function(){g(""),c()}},{color:"info",label:E.confirm,onClick:function(){d(!1),function(e,a,t,n){Za("/security_policy_groups/"+e+"/rules/"+a,(function(){t(e,a)}),n)}(o,l,y,v)}}];return s.a.createElement(oa,{size:"xs",open:t,prompt:b,title:h,buttons:k,content:x,operatable:m})}var Tu={en:{title:"Add Security Policy Rule",action:"Action",accept:"Accept",reject:"Reject",protocol:"Protocol",sourceAddress:"Source Address",targetPort:"Target Port",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6dfb\u52a0\u5b89\u5168\u89c4\u5219",action:"\u5904\u7406",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",protocol:"\u534f\u8bae",sourceAddress:"\u6765\u6e90\u5730\u5740",targetPort:"\u76ee\u6807\u7aef\u53e3",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Iu(e){var a={action:"accept",protocol:"",port:""},t=e.lang,o=e.policyID,l=e.open,r=e.onSuccess,c=e.onCancel,i=s.a.useState(!0),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(""),f=Object(n.a)(p,2),b=f[0],g=f[1],h=s.a.useState(a),v=Object(n.a)(h,2),y=v[0],x=v[1],k=Tu[t],C=k.title,S=[{label:k.accept,value:"accept"},{label:k.reject,value:"reject"}],O=function(e){d(!0),g(e)},j=function(){g(""),x(a)},w=function(e){d(!0),j(),r(e)},_=function(e){return function(a){var t=a.target.value;x((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}},N=[{type:"radio",label:k.action,onChange:_("action"),value:y.action,options:S,required:!0,oneRow:!0,xs:10,sm:8,md:6},{type:"select",label:k.protocol,onChange:_("protocol"),value:y.protocol,options:[{label:"TCP",value:"tcp"},{label:"UDP",value:"udp"}],required:!0,oneRow:!0,xs:8,sm:6,md:4},{type:"text",label:k.targetPort,onChange:_("port"),value:y.port,required:!0,oneRow:!0,xs:8,sm:6,md:4}],R=s.a.createElement(ze,{inputs:N}),T=[{color:"transparent",label:k.cancel,onClick:function(){j(),c()}},{color:"info",label:k.confirm,onClick:function(){if(d(!1),y.action)if(y.protocol)if(y.port){var e=Number.parseInt(y.port);Number.isNaN(e)?O("invalid target port: "+y.port):function(e,a,t,n,o,l){Ua("/security_policy_groups/"+e+"/rules/",{action:a,protocol:t,to_port:n},o,l)}(o,y.action,y.protocol,e,w,O)}else O("must specify target port");else O("must specify protocol");else O("must specify action")}}];return s.a.createElement(oa,{size:"sm",open:l,prompt:b,title:C,buttons:T,content:R,operatable:m})}var Du={en:{title:"Modify Security Policy Rule",action:"Action",accept:"Accept",reject:"Reject",protocol:"Protocol",sourceModifyress:"Source Address",targetPort:"Target Port",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u5b89\u5168\u89c4\u5219",action:"\u5904\u7406",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",protocol:"\u534f\u8bae",sourceModifyress:"\u6765\u6e90\u5730\u5740",targetPort:"\u76ee\u6807\u7aef\u53e3",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Au(e){var a=e.lang,t=e.policyID,o=e.rule,l=e.open,r=e.onSuccess,c=e.onCancel,i={action:o.action,protocol:o.protocol,port:o.to_port},u=s.a.useState(!0),m=Object(n.a)(u,2),d=m[0],p=m[1],f=s.a.useState(""),b=Object(n.a)(f,2),g=b[0],h=b[1],v=s.a.useState(!1),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState(!1),S=Object(n.a)(C,2),O=S[0],j=S[1],w=s.a.useState(i),_=Object(n.a)(w,2),N=_[0],R=_[1],T=Du[a],I=T.title,D=[{label:T.accept,value:"accept"},{label:T.reject,value:"reject"}],A=s.a.useCallback((function(e){x&&(p(!0),h(e))}),[x]),P=function(){h(""),R(i),j(!1)},F=function(e){p(!0),P(),r(e)},B=function(e){return function(a){var t=a.target.value;R((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}};s.a.useEffect((function(){if(t&&o)return k(!0),R({action:o.action,protocol:o.protocol,port:o.to_port}),j(!0),function(){k(!1)}}),[x,l,t,o,A]);var M,z=[{color:"transparent",label:T.cancel,onClick:function(){P(),c()}}];if(O){var W=[{type:"radio",label:T.action,onChange:B("action"),value:N.action,options:D,required:!0,oneRow:!0,xs:10,sm:8,md:6},{type:"select",label:T.protocol,onChange:B("protocol"),value:N.protocol,options:[{label:"TCP",value:"tcp"},{label:"UDP",value:"udp"}],required:!0,oneRow:!0,xs:8,sm:6,md:4},{type:"text",label:T.targetPort,onChange:B("port"),value:N.port,required:!0,oneRow:!0,xs:8,sm:6,md:4}];M=s.a.createElement(ze,{inputs:W}),z.push({color:"info",label:T.confirm,onClick:function(){if(p(!1),N.action)if(N.protocol)if(N.port){var e=Number.parseInt(N.port);Number.isNaN(e)?A("invalid target port: "+N.port):function(e,a,t,n,o,l,r){$a("/security_policy_groups/"+e+"/rules/"+a,{action:t,protocol:n,to_port:o},(function(){l(e,a)}),r)}(t,o.index,N.action,N.protocol,e,F,A)}else A("must specify target port");else A("must specify protocol");else A("must specify action")}})}else M=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(oa,{size:"sm",open:l,prompt:g,title:I,buttons:z,content:M,operatable:d})}var Pu=Object(E.a)(Object(E.a)({},U),{},{cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),Fu=Object(g.a)(Pu),Bu={en:{createButton:"Add New Rule",tableTitle:"Security Policy Rules",rule:"Rule",action:"Action",protocol:"Protocol",sourceAddress:"Source Address",targetPort:"Target Port",accept:"Accept",reject:"Reject",operates:"Operates",noResource:"No security policy available",modify:"Modify",remove:"Remove",moveUp:"Move Up",moveDown:"Move Down",back:"Back"},cn:{createButton:"\u6dfb\u52a0\u65b0\u89c4\u5219",tableTitle:"\u5b89\u5168\u89c4\u5219",rule:"\u89c4\u5219",action:"\u5904\u7406",protocol:"\u534f\u8bae",sourceAddress:"\u6765\u6e90\u5730\u5740",targetPort:"\u76ee\u6807\u7aef\u53e3",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u5b89\u5168\u7b56\u7565\u7ec4",modify:"\u4fee\u6539",remove:"\u79fb\u9664",moveUp:"\u4e0a\u79fb",moveDown:"\u4e0b\u79fb",back:"\u8fd4\u56de"}};function Mu(e){var a,t=e.match.params.id,o=e.lang,l=Bu[o],r=Fu(),c=s.a.useState(!1),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(null),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(!1),E=Object(n.a)(g,2),h=E[0],v=E[1],y=s.a.useState(!1),x=Object(n.a)(y,2),k=x[0],C=x[1],S=s.a.useState(!1),O=Object(n.a)(S,2),j=O[0],w=O[1],_=s.a.useState(""),N=Object(n.a)(_,2),R=N[0],T=N[1],I=s.a.useState("warning"),D=Object(n.a)(I,2),A=D[0],P=D[1],F=s.a.useState(""),B=Object(n.a)(F,2),M=B[0],z=B[1],W=function(){z("")},q=s.a.useCallback((function(e){if(u){P("warning"),z(e),setTimeout(W,3e3)}}),[P,z,u]),H=s.a.useCallback((function(){u&&function(e,a,t){Ha("/security_policy_groups/"+e+"/rules/",a,t)}(t,b,q)}),[t,q,u]),L=function(e){if(u){P("info"),z(e),Wa(e),setTimeout(W,3e3)}},U=function(){C(!1)},V=function(){w(!1)},G=function(){v(!1)},$=function(e){!function(e,a,t,n){$a("/security_policy_groups/"+e+"/rules/"+a+"/order",{direction:"up"},(function(){t(e,a)}),n)}(t,e.index,(function(e,a){L(a+"th rule moved up"),H()}),q)},Z=function(e){!function(e,a,t,n){$a("/security_policy_groups/"+e+"/rules/"+a+"/order",{direction:"down"},(function(){t(e,a)}),n)}(t,e.index,(function(e,a){L(a+"th rule moved down"),H()}),q)};if(s.a.useEffect((function(){return m(!0),H(),function(){m(!1)}}),[H]),null===f)a=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});else if(0===f.length)a=s.a.createElement(K.a,{display:"flex",justifyContent:"center"},s.a.createElement(fn,null,l.noResource));else{var Y=[];f.forEach((function(e,a){var t={index:a,action:e.action,protocol:e.protocol,to_port:e.to_port},n=[{onClick:function(e){return function(e){C(!0),T(e)}(t)},icon:Yo.a,label:l.modify},{onClick:function(e){return function(e){w(!0),T(e)}(t)},icon:Wt.a,label:l.remove}];f.length-1!==a&&n.push({onClick:function(e){return Z(t)},icon:ts.a,label:l.moveDown}),0!==a&&n.push({onClick:function(e){return $(t)},icon:os.a,label:l.moveUp}),Y.push(function(e,a,t){var n=t.map((function(e,a){return s.a.createElement(jn,{label:e.label,icon:e.icon,onClick:e.onClick,key:a})})),o=a.action,c=a.protocol,i=a.from_address,u=a.to_port;return[e,"accept"===o?s.a.createElement(te.a,{title:l.accept,placement:"top"},s.a.createElement(At.a,{className:r.successText})):s.a.createElement(te.a,{title:l.reject,placement:"top"},s.a.createElement(Ft.a,{className:r.dangerText})),c,i,u,n]}(a,e,n))})),a=s.a.createElement(On,{color:"primary",headers:[l.rule,l.action,l.protocol,l.sourceAddress,l.targetPort,l.operates],rows:Y})}var J=[{href:"/admin/security_policies/",icon:il.a,label:l.back},{onClick:function(){v(!0)},icon:Tt.a,label:l.createButton}];return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12},s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12,sm:6,md:4},s.a.createElement(K.a,{display:"flex"},J.map((function(e,a){return e.href?s.a.createElement(K.a,{p:1,key:a},s.a.createElement(fe,{size:"sm",color:"info",round:!0,href:e.href},s.a.createElement(e.icon),e.label)):s.a.createElement(K.a,{p:1,key:a},s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:e.onClick},s.a.createElement(e.icon),e.label))})))))),s.a.createElement(Qe,{xs:12},s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",{className:r.cardTitleWhite},l.tableTitle)),s.a.createElement(dn,null,a))),s.a.createElement(En,{place:"tr",color:A,message:M,open:""!==M,closeNotification:W,close:!0}),s.a.createElement(Iu,{lang:o,open:h,policyID:t,onSuccess:function(){G(),L("new security policy rule added"),H()},onCancel:G}),s.a.createElement(Au,{lang:o,open:k,policyID:t,rule:R,onSuccess:function(e){U(),L(e+"th rule modified"),H()},onCancel:U}),s.a.createElement(Ru,{lang:o,open:j,policyID:t,index:R.index,onSuccess:function(e,a){V(),L(a+"the rule removed"),H()},onCancel:V}))}var zu={en:{title:"Batch Deleting Log",content1:"Are you sure to delete ",content2:" log(s)",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6279\u91cf\u5220\u9664\u65e5\u5fd7",content1:"\u662f\u5426\u5220\u9664 ",content2:" \u6761\u65e5\u5fd7",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Wu(e){var a,t=e.lang,o=e.targets,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.length,u=s.a.useState(""),m=Object(n.a)(u,2),d=m[0],p=m[1],f=zu[t],b=function(e){p(e)},g=function(){p(""),r(i)};return a=d&&""!==d?s.a.createElement(Qe,{xs:12},s.a.createElement(na,{message:d,color:"danger"})):s.a.createElement(Qe,{xs:12}),s.a.createElement(We.a,{open:l,"aria-labelledby":f.title,maxWidth:"sm",fullWidth:!0},s.a.createElement(Le.a,null,f.title),s.a.createElement(He.a,null,s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},f.content1+i.toString()+f.content2),a)),s.a.createElement(qe.a,null,s.a.createElement(fe,{onClick:function(){p(""),c()},color:"transparent",autoFocus:!0},f.cancel),s.a.createElement(fe,{onClick:function(){!function(e,a,t){Ja("/logs/",{entries:e},a,t)}(o,g,b)},color:"info"},f.confirm)))}var qu=Object(E.a)(Object(E.a)({},Yn),{},{cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),Hu=Object(g.a)(qu),Lu={en:{batchDelete:"Batch Delete",enterBatch:"Enter Batch Mode",exitBatch:"Exit Batch Mode",tableTitle:"Operate Logs",duration:"Log Duration",time:"Timestamp",content:"Content",noResource:"No log available",day:"The Last Day",month:"The Last Month",year:"The Last Year"},cn:{batchDelete:"\u6279\u91cf\u5220\u9664",enterBatch:"\u8fdb\u5165\u6279\u91cf\u6a21\u5f0f",exitBatch:"\u9000\u51fa\u6279\u91cf\u6a21\u5f0f",tableTitle:"\u64cd\u4f5c\u65e5\u5fd7",duration:"\u65e5\u5fd7\u8303\u56f4",time:"\u65e5\u5fd7\u65f6\u95f4",content:"\u5185\u5bb9",noResource:"\u6ca1\u6709\u65e5\u5fd7\u4fe1\u606f",day:"\u6700\u8fd1\u4e00\u5929",month:"\u6700\u8fd1\u4e00\u4e2a\u6708",year:"\u6700\u8fd1\u4e00\u5e74"}},Uu=function(e){var a,t=e.log,n=e.checked,o=e.checkable,l=e.onCheckStatusChanged;return a=o?s.a.createElement(K.a,{display:"flex",alignItems:"center"},s.a.createElement(K.a,null,s.a.createElement(Pe.a,{checked:n,onChange:function(e){var a=e.target.checked;l(a,t.id)},value:t.id})),s.a.createElement(K.a,null,t.time)):t.time,s.a.createElement(yn.a,null,s.a.createElement(kn.a,null,a),s.a.createElement(kn.a,null,t.content))};var Vu,Gu=t(307),$u=t.n(Gu),Zu=t(308),Yu=t.n(Zu),Ju=t(309),Qu=t.n(Ju),Ku=t(310),Xu=t.n(Ku),em=t(311),am=t.n(em),tm=t(313),nm=t.n(tm),om=t(314),lm=t.n(om),rm=t(312),cm=t.n(rm),im=[{path:"/dashboard",name:"dashboard",display:{cn:"\u7cfb\u7edf\u4eea\u8868\u76d8",en:"Dashboard"},icon:$u.a,component:function(e){return s.a.createElement("div",null,s.a.createElement(b.b,{path:"/admin/dashboard",exact:!0,render:function(a){return s.a.createElement(so,Object(E.a)(Object(E.a)({},a),e))}}),s.a.createElement(b.b,{path:"/admin/dashboard/pools/",exact:!0,render:function(a){return s.a.createElement(ko,Object(E.a)(Object(E.a)({},a),e))}}),s.a.createElement(b.b,{path:"/admin/dashboard/pools/:pool",exact:!0,render:function(a){return s.a.createElement(Fo,Object(E.a)(Object(E.a)({},a),e))}}))},layout:"/admin"},{path:"/compute_pools",name:"compute_pool",display:{cn:"\u8ba1\u7b97\u8d44\u6e90\u6c60",en:"Compute Pools"},icon:Yu.a,component:function(e){var a,t=Go(),o=e.lang,l=$o[o],r=s.a.useState(!1),c=Object(n.a)(r,2),i=c[0],u=c[1],m=s.a.useState(null),d=Object(n.a)(m,2),p=d[0],f=d[1],b=s.a.useState(!1),g=Object(n.a)(b,2),h=g[0],v=g[1],y=s.a.useState(!1),x=Object(n.a)(y,2),k=x[0],C=x[1],S=s.a.useState(!1),O=Object(n.a)(S,2),j=O[0],w=O[1],_=s.a.useState(""),N=Object(n.a)(_,2),R=N[0],T=N[1],I=s.a.useState("warning"),D=Object(n.a)(I,2),A=D[0],P=D[1],F=s.a.useState(""),B=Object(n.a)(F,2),M=B[0],z=B[1],W=function(){z("")},q=s.a.useCallback((function(e){if(i){P("warning"),z(e),setTimeout(W,3e3)}}),[P,z,i]),H=s.a.useCallback((function(){if(i){fa(f,(function(e){i&&q(e)}))}}),[q,i]),L=function(e){if(i){P("info"),z(e),Wa(e),setTimeout(W,3e3)}},U=function(){C(!1)},V=function(){w(!1)},G=function(){v(!1)};if(s.a.useEffect((function(){return u(!0),H(),function(){u(!1)}}),[H]),null===p)a=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});else if(0===p.length)a=s.a.createElement(K.a,{display:"flex",justifyContent:"center"},s.a.createElement(fn,null,l.noPools));else{var $=[];p.forEach((function(e){var a,n,o,r=[{label:l.cells,icon:Mo.a,href:"/admin/compute_cells/?pool="+e.name},{label:l.instances,icon:Mt.a,href:"/admin/instances/range/?pool="+e.name},{onClick:function(a){return t=e.name,C(!0),void T(t);var t},icon:Ht.a,label:l.modify},{onClick:function(a){return t=e.name,w(!0),void T(t);var t},icon:Wt.a,label:l.delete}],c=e.name,i=e.cells,u=e.network,m=e.storage,d=e.enabled,p=e.failover;d?(a=l.enabled,n=s.a.createElement(te.a,{title:a,placement:"top"},s.a.createElement(At.a,{className:t.successText})),r=[{label:l.disable,icon:Ft.a}].concat(r)):(a=l.disabled,n=s.a.createElement(te.a,{title:a,placement:"top"},s.a.createElement(Ft.a,{className:t.warningText})),r=[{label:l.enable,icon:At.a}].concat(r));o=p?l.on:l.off;var f=r.map((function(e,a){return s.a.createElement(jn,Object(E.a)(Object(E.a)({},e),{},{key:a}))})),b=[c,i,u||l.noAddressPool,m||l.localStorage,o,n,f];$.push(b)})),a=s.a.createElement(On,{color:"primary",headers:[l.name,l.cells,l.network,l.storage,l.failover,l.status,l.operates],rows:$})}return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(It.a,{separator:"\u203a","aria-label":"breadcrumb"},s.a.createElement(oe.a,{color:"textPrimary"},l.computePools))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12,sm:12,md:12},s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:3,sm:3,md:3},s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){v(!0)}},s.a.createElement(Tt.a,null),l.createButton)))),s.a.createElement(Qe,{xs:12,sm:12,md:12},s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",{className:t.cardTitleWhite},l.tableTitle)),s.a.createElement(dn,null,a))),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:A,message:M,open:""!==M,closeNotification:W,close:!0})),s.a.createElement(Qe,null,s.a.createElement(Ho,{lang:o,open:h,onSuccess:function(e){G(),L("pool "+e+" created"),H()},onCancel:G})),s.a.createElement(Qe,null,s.a.createElement(Uo,{lang:o,open:k,pool:R,onSuccess:function(e){U(),L("pool "+e+" modified"),H()},onCancel:U})),s.a.createElement(Qe,null,s.a.createElement(Wo,{lang:o,open:j,pool:R,onSuccess:function(e){V(),L("pool "+e+" deleted"),H()},onCancel:V})))},layout:"/admin"},{path:"/address_pools",name:"address_pool",display:{cn:"\u5730\u5740\u6c60",en:"Address Pools"},icon:Qu.a,component:function(e){return s.a.createElement("div",null,s.a.createElement(b.b,{path:"/admin/address_pools",exact:!0,render:function(){return s.a.createElement(rl,e)}}),s.a.createElement(b.b,{path:"/admin/address_pools/:pool",exact:!0,render:function(a){return s.a.createElement(El,Object(E.a)(Object(E.a)({},e),a))}}),s.a.createElement(b.b,{path:"/admin/address_pools/:pool/:type/ranges/:start",render:function(a){return s.a.createElement(yl,Object(E.a)(Object(E.a)({},e),a))}}))},layout:"/admin"},{path:"/storage_pools",name:"storage_pool",display:{cn:"\u5b58\u50a8\u6c60",en:"Storage Pools"},icon:Jt.a,component:function(e){var a,t=e.lang,o=_l[t],l=wl(),r=s.a.useState(!1),c=Object(n.a)(r,2),i=c[0],u=c[1],m=s.a.useState(null),d=Object(n.a)(m,2),p=d[0],f=d[1],b=s.a.useState(!1),g=Object(n.a)(b,2),E=g[0],h=g[1],v=s.a.useState(!1),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState(!1),S=Object(n.a)(C,2),O=S[0],j=S[1],w=s.a.useState(""),_=Object(n.a)(w,2),N=_[0],R=_[1],T=s.a.useState("warning"),I=Object(n.a)(T,2),D=I[0],A=I[1],P=s.a.useState(""),F=Object(n.a)(P,2),B=F[0],M=F[1],z=function(){M("")},W=s.a.useCallback((function(e){if(i){A("warning"),M(e),setTimeout(z,3e3)}}),[A,M,i]),q=s.a.useCallback((function(){if(i){Ea(f,(function(e){i&&W(e)}))}}),[W,i]),H=function(e){if(i){A("info"),M(e),Wa(e),setTimeout(z,3e3)}},L=function(){k(!1)},U=function(){j(!1)},V=function(){h(!1)};if(s.a.useEffect((function(){return u(!0),q(),function(){u(!1)}}),[q]),null===p)a=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});else if(0===p.length)a=s.a.createElement(K.a,{display:"flex",justifyContent:"center"},s.a.createElement(fn,null,o.noResource));else{var G=[];p.forEach((function(e){var a=[{onClick:function(a){return t=e.name,k(!0),void R(t);var t},icon:Ht.a,label:o.modify},{onClick:function(a){return t=e.name,j(!0),void R(t);var t},icon:Wt.a,label:o.delete}];G.push(function(e,a){var t=a.map((function(e,a){return s.a.createElement(jn,{label:e.label,icon:e.icon,onClick:e.onClick,key:a})}));return[e.name,e.type,e.host,e.target,t]}(e,a))})),a=s.a.createElement(On,{color:"primary",headers:[o.name,o.type,o.host,o.target,o.operates],rows:G})}return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12,sm:12,md:12},s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:3,sm:3,md:3},s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){h(!0)}},s.a.createElement(Tt.a,null),o.createButton)))),s.a.createElement(Qe,{xs:12,sm:12,md:12},s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",{className:l.cardTitleWhite},o.tableTitle)),s.a.createElement(dn,null,a))),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:D,message:B,open:""!==B,closeNotification:z,close:!0})),s.a.createElement(Qe,null,s.a.createElement(Sl,{lang:t,open:E,onSuccess:function(e){V(),H("pool "+e+" created"),q()},onCancel:V})),s.a.createElement(Qe,null,s.a.createElement(jl,{lang:t,open:x,pool:N,onSuccess:function(e){L(),H("pool "+e+" modified"),q()},onCancel:L})),s.a.createElement(Qe,null,s.a.createElement(kl,{lang:t,open:O,pool:N,onSuccess:function(e){U(),H("pool "+e+" deleted"),q()},onCancel:U})))},layout:"/admin"},{path:"/instances",name:"instance",display:{cn:"\u4e91\u4e3b\u673a",en:"Instances"},icon:Xu.a,component:function(e){return s.a.createElement("div",null,s.a.createElement(b.b,{path:"/admin/instances",exact:!0,render:function(){return s.a.createElement(es,e)}}),s.a.createElement(b.b,{path:"/admin/instances/range/",render:function(){return s.a.createElement(Hc,e)}}),s.a.createElement(b.b,{path:"/admin/instances/status/:id",render:function(a){return s.a.createElement(Zc,Object(E.a)(Object(E.a)({},a),e))}}),s.a.createElement(b.b,{path:"/admin/instances/snapshots/:id",render:function(a){return s.a.createElement(gi,Object(E.a)(Object(E.a)({},a),e))}}),s.a.createElement(b.b,{path:"/admin/instances/details/:id",render:function(a){return s.a.createElement(Yi,Object(E.a)(Object(E.a)({},a),e))}}),s.a.createElement(b.b,{path:"/admin/instances/policies/:id",render:function(a){return s.a.createElement(fs,Object(E.a)(Object(E.a)({},a),e))}}))},layout:"/admin"},{path:"/disk_images",name:"image",display:{cn:"\u78c1\u76d8\u955c\u50cf",en:"Disk Images"},icon:am.a,component:function(e){var a,t=e.lang,o=tr[t],l=s.a.useState(!1),r=Object(n.a)(l,2),c=r[0],i=r[1],u=s.a.useState(null),m=Object(n.a)(u,2),d=m[0],p=m[1],f=s.a.useState(!1),b=Object(n.a)(f,2),g=b[0],h=b[1],v=s.a.useState(!1),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState(!1),S=Object(n.a)(C,2),O=S[0],j=S[1],w=s.a.useState(!1),_=Object(n.a)(w,2),N=_[0],R=_[1],T=s.a.useState(!1),I=Object(n.a)(T,2),D=I[0],A=I[1],P=s.a.useState(""),F=Object(n.a)(P,2),B=F[0],M=F[1],z=s.a.useState("warning"),W=Object(n.a)(z,2),q=W[0],H=W[1],L=s.a.useState(""),U=Object(n.a)(L,2),V=U[0],G=U[1],$=function(){G("")},Z=s.a.useCallback((function(e){if(c){H("warning"),G(e),setTimeout($,3e3)}}),[H,G,c]),Y=function(e){H("info"),G(e),Wa(e),setTimeout($,3e3)},J=s.a.useCallback((function(){if(c){Ra((function(e){c&&p(e||[])}),(function(e){c&&Z(e)}))}}),[Z,c]),Q=function(){j(!1)},X=function(){R(!1)},ee=function(){h(!1)},ae=function(){k(!1)},te=function(){A(!1)};return s.a.useEffect((function(){i(!0),J();var e=setInterval((function(){J()}),5e3);return function(){i(!1),clearInterval(e)}}),[J]),a=null===d?s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}}):0===d.length?s.a.createElement(K.a,{display:"flex",justifyContent:"center"},s.a.createElement(fn,null,o.noResource)):s.a.createElement(Xt,null,d.map((function(e,a){var t=[{label:o.modify,icon:Ht.a,onClick:function(){return a=e.id,j(!0),void M(a);var a}},{label:o.download,icon:Vl.a,onClick:function(){var a="/api/v1/disk_images/"+e.id+"/file/";window.location.href=a}},{label:o.delete,icon:Wt.a,onClick:function(){return a=e.id,R(!0),void M(a);var a}}],n=function(e,a,t,n){var o=e.name,l=e.size,r=e.tags,c=e.description,i=e.create_time,u=e.modify_time,m=e.id,d=ua(l),p=a.map((function(e,a){return s.a.createElement(jn,Object(E.a)(Object(E.a)({},e),{},{key:a}))}));return s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",null,o),s.a.createElement("span",null,m),s.a.createElement(K.a,{display:"flex",alignItems:"center"},s.a.createElement(K.a,{m:1},d),r?r.map((function(e){return s.a.createElement(K.a,{m:0,p:1,key:e},s.a.createElement(Dl.a,{label:e}))})):s.a.createElement(K.a,null))),s.a.createElement(dn,null,s.a.createElement(oe.a,{variant:"body1",component:"p",noWrap:!0},c),s.a.createElement("p",null,t+": "+i),s.a.createElement("p",null,n+": "+u),p))}(e,t,o.createTime,o.modifyTime);return s.a.createElement(Qe,{xs:12,sm:6,md:4,key:a},n)}))),s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12,sm:6,md:4},s.a.createElement(K.a,{display:"flex"},s.a.createElement(K.a,{p:1},s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){h(!0)}},s.a.createElement(Rl.a,null),o.uploadButton)),s.a.createElement(K.a,{p:1},s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){k(!0)}},s.a.createElement(Yo.a,null),o.buildButton)),s.a.createElement(K.a,{p:1},s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){A(!0)}},s.a.createElement(Il.a,null),o.syncButton)))))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12,sm:12,md:12},a),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:q,message:V,open:""!==V,closeNotification:$,close:!0})),s.a.createElement(Qe,null,s.a.createElement(Yl,{lang:t,open:g,onSuccess:function(e){ee(),Y("new image "+e+" uploaded"),J()},onCancel:ee})),s.a.createElement(Qe,null,s.a.createElement(Xl,{lang:t,open:x,onSuccess:function(e){ae(),Y("new image "+e+" built"),J()},onCancel:ae})),s.a.createElement(Qe,null,s.a.createElement(Ql,{lang:t,imageID:B,open:O,onSuccess:function(e){Q(),Y("image "+e+" modified"),J()},onCancel:Q})),s.a.createElement(Qe,null,s.a.createElement($l,{lang:t,imageID:B,open:N,onSuccess:function(e){X(),Y("image "+e+" deleted"),J()},onCancel:X})),s.a.createElement(Qe,null,s.a.createElement(ar,{lang:t,open:D,onSuccess:function(){te(),Y("all disk images synchronized"),J()},onCancel:te})))},layout:"/admin"},{path:"/media_images",name:"media",display:{cn:"\u5149\u76d8\u955c\u50cf",en:"Media Images"},icon:sc.a,component:function(e){var a,t=e.lang,o=Ll[t],l=s.a.useState(!1),r=Object(n.a)(l,2),c=r[0],i=r[1],u=s.a.useState(null),m=Object(n.a)(u,2),d=m[0],p=m[1],f=s.a.useState(!1),b=Object(n.a)(f,2),g=b[0],h=b[1],v=s.a.useState(!1),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState(!1),S=Object(n.a)(C,2),O=S[0],j=S[1],w=s.a.useState(!1),_=Object(n.a)(w,2),N=_[0],R=_[1],T=s.a.useState(""),I=Object(n.a)(T,2),D=I[0],A=I[1],P=s.a.useState("warning"),F=Object(n.a)(P,2),B=F[0],M=F[1],z=s.a.useState(""),W=Object(n.a)(z,2),q=W[0],H=W[1],L=function(){H("")},U=s.a.useCallback((function(e){if(c){M("warning"),H(e),setTimeout(L,3e3)}}),[M,H,c]),V=function(e){M("info"),H(e),Wa(e),setTimeout(L,3e3)},G=s.a.useCallback((function(){if(c){_a((function(e){c&&p(e||[])}),(function(e){c&&U(e)}))}}),[U,c]),$=function(){k(!1)},Z=function(){j(!1)},Y=function(){h(!1)},J=function(){R(!1)};return s.a.useEffect((function(){i(!0),G();var e=setInterval((function(){G()}),5e3);return function(){i(!1),clearInterval(e)}}),[G]),a=null===d?s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}}):0===d.length?s.a.createElement(K.a,{display:"flex",justifyContent:"center"},s.a.createElement(fn,null,o.noResource)):s.a.createElement(Xt,null,d.map((function(e,a){var t=[{label:o.modify,icon:Ht.a,onClick:function(){return a=e.id,k(!0),void A(a);var a}},{label:o.delete,icon:Wt.a,onClick:function(){return a=e.id,j(!0),void A(a);var a}}],n=function(e,a,t,n){var o=e.name,l=e.size,r=e.tags,c=e.description,i=e.create_time,u=e.modify_time,m=e.id,d=ua(l),p=a.map((function(e,a){return s.a.createElement(jn,Object(E.a)(Object(E.a)({},e),{},{key:a}))}));return s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",null,o),s.a.createElement("span",null,m),s.a.createElement(K.a,{display:"flex",alignItems:"center"},s.a.createElement(K.a,{m:1},d),r?r.map((function(e){return s.a.createElement(K.a,{m:0,p:1,key:e},s.a.createElement(Dl.a,{label:e}))})):s.a.createElement(K.a,null))),s.a.createElement(dn,null,s.a.createElement(oe.a,{variant:"body1",component:"p",noWrap:!0},c),s.a.createElement("p",null,t+": "+i),s.a.createElement("p",null,n+": "+u),p))}(e,t,o.createTime,o.modifyTime);return s.a.createElement(Qe,{xs:12,sm:6,md:4,key:a},n)}))),s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12,sm:12,md:12},s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12,sm:6,md:4},s.a.createElement(K.a,{display:"flex"},s.a.createElement(K.a,{p:1},s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){h(!0)}},s.a.createElement(Rl.a,null),o.uploadButton)),s.a.createElement(K.a,{p:1},s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){R(!0)}},s.a.createElement(Il.a,null),o.syncButton)))))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12,sm:12,md:12},a),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:B,message:q,open:""!==q,closeNotification:L,close:!0})),s.a.createElement(Qe,null,s.a.createElement(Ml,{lang:t,open:g,onSuccess:function(e){Y(),V("new image "+e+" uploaded"),G()},onCancel:Y})),s.a.createElement(Qe,null,s.a.createElement(Wl,{lang:t,imageID:D,open:x,onSuccess:function(e){$(),V("image "+e+" modified"),G()},onCancel:$})),s.a.createElement(Qe,null,s.a.createElement(Pl,{lang:t,imageID:D,open:O,onSuccess:function(e){Z(),V("image "+e+" deleted"),G()},onCancel:Z})),s.a.createElement(Qe,null,s.a.createElement(Hl,{lang:t,open:N,onSuccess:function(){J(),V("all media images synchronized"),G()},onCancel:J})))},layout:"/admin"},{path:"/system_templates",name:"templates",display:{cn:"\u7cfb\u7edf\u6a21\u677f",en:"System Templates"},icon:cm.a,component:function(e){var a,t=e.lang,o=Sr[t],l=Cr(),r=s.a.useState(!1),c=Object(n.a)(r,2),i=c[0],u=c[1],m=s.a.useState(null),d=Object(n.a)(m,2),p=d[0],f=d[1],b=s.a.useState(!1),g=Object(n.a)(b,2),E=g[0],h=g[1],v=s.a.useState(!1),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState(!1),S=Object(n.a)(C,2),O=S[0],j=S[1],w=s.a.useState(""),_=Object(n.a)(w,2),N=_[0],R=_[1],T=s.a.useState("warning"),I=Object(n.a)(T,2),D=I[0],A=I[1],P=s.a.useState(""),F=Object(n.a)(P,2),B=F[0],M=F[1],z=function(){M("")},W=s.a.useCallback((function(e){if(i){A("warning"),M(e),setTimeout(z,3e3)}}),[A,M,i]),q=s.a.useCallback((function(){i&&Ba(f,W)}),[W,i]),H=function(e){if(i){A("info"),M(e),Wa(e),setTimeout(z,3e3)}},L=function(){k(!1)},U=function(){j(!1)},V=function(){h(!1)};if(s.a.useEffect((function(){return u(!0),q(),function(){u(!1)}}),[q]),null===p)a=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});else if(0===p.length)a=s.a.createElement(K.a,{display:"flex",justifyContent:"center"},s.a.createElement(fn,null,o.noResource));else{var G=[];p.forEach((function(e){var a=[{onClick:function(a){return t=e.id,k(!0),void R(t);var t},icon:Ht.a,label:o.detail},{onClick:function(a){return t=e.id,j(!0),void R(t);var t},icon:Wt.a,label:o.delete}];G.push(function(e,a){var t=a.map((function(e,a){return s.a.createElement(jn,{label:e.label,icon:e.icon,onClick:e.onClick,key:a})}));return[e.id,e.name,e.operating_system,e.created_time,e.modified_time,t]}(e,a))})),a=s.a.createElement(On,{color:"primary",headers:["ID",o.name,o.os,o.createdTime,o.modifiedTime,o.operates],rows:G})}return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12,sm:12,md:12},s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:3,sm:3,md:3},s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){h(!0)}},s.a.createElement(Tt.a,null),o.createButton)))),s.a.createElement(Qe,{xs:12,sm:12,md:12},s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",{className:l.cardTitleWhite},o.tableTitle)),s.a.createElement(dn,null,a))),s.a.createElement(En,{place:"tr",color:D,message:B,open:""!==B,closeNotification:z,close:!0}),s.a.createElement(pr,{lang:t,open:E,onSuccess:function(e){V(),H("new template "+e+" created"),q()},onCancel:V}),s.a.createElement(kr,{lang:t,open:x,templateID:N,onSuccess:function(e){L(),H("template "+e+" modified"),q()},onCancel:L}),s.a.createElement(or,{lang:t,open:O,templateID:N,onSuccess:function(e){U(),H("template "+e+" deleted"),q()},onCancel:U}))},layout:"/admin"},{path:"/security_policies",name:"policies",display:{cn:"\u5b89\u5168\u7b56\u7565\u7ec4",en:"Security Policies"},icon:Yr.a,component:function(e){return s.a.createElement("div",null,s.a.createElement(b.b,{path:"/admin/security_policies/",exact:!0,render:function(){return s.a.createElement(_u,e)}}),s.a.createElement(b.b,{path:"/admin/security_policies/:id/rules/",render:function(a){return s.a.createElement(Mu,Object(E.a)(Object(E.a)({},a),e))}}))},layout:"/admin"},{path:"/users",name:"user",display:{cn:"\u7528\u6237\u7ba1\u7406",en:"User Management"},icon:nm.a,component:function(e){var a=e.lang,t=du[a];return s.a.createElement(Cs,{title:t.title,headerColor:"primary",tabs:[{tabName:t.user,tabIcon:ge.a,tabContent:s.a.createElement(Bs,{lang:a})},{tabName:t.group,tabIcon:gs.a,tabContent:s.a.createElement(pu,{lang:a})},{tabName:t.role,tabIcon:hs.a,tabContent:s.a.createElement(mu,{lang:a})}]})},layout:"/admin"},{path:"/logs",name:"log",display:{cn:"\u64cd\u4f5c\u65e5\u5fd7",en:"Operate Logs"},icon:lm.a,component:function(e){var a=Hu(),t=s.a.useState(null),o=Object(n.a)(t,2),l=o[0],r=o[1],c=s.a.useState(new Map),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(!1),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState({offset:0,duration:"last-day"}),h=Object(n.a)(g,2),v=h[0],y=h[1],x=s.a.useState({current:0,total:0}),k=Object(n.a)(x,2),C=k[0],S=k[1],O=s.a.useState(!1),j=Object(n.a)(O,2),w=j[0],_=j[1],N=s.a.useState("warning"),R=Object(n.a)(N,2),T=R[0],I=R[1],D=s.a.useState(""),A=Object(n.a)(D,2),P=A[0],F=A[1],B=function(){F("")},M=s.a.useCallback((function(e){I("warning"),F(e),setTimeout(B,3e3)}),[I,F]),z=function(e){var a=function(e){return e<10?"0"+e.toString():e.toString()};return e.getFullYear()+"-"+a(e.getMonth()+1)+"-"+a(e.getDate())+" "+a(e.getHours())+":"+a(e.getMinutes())+":"+a(e.getSeconds())},W=s.a.useCallback((function(){var e=new Date,a=new Date(e);switch(v.duration){case"last-day":a.setDate(e.getDate()-1);break;case"last-month":a.setMonth(e.getMonth()-1);break;case"last-year":a.setFullYear(e.getFullYear()-1);break;default:return void M("invalid duration: "+v.duration)}!function(e,a,t,n,o,l){var r="/logs/?limit="+e;a&&(r+="&start="+a),t&&(r+="&after="+t),n&&(r+="&before="+n),Ha(r,(function(e){var t=0;a&&(t=a),o(Object(E.a)(Object(E.a)({},e),{},{offset:t}))}),l)}(10,v.offset,z(a),z(e),(function(e){var a,t,n=e.logs,o=e.total,l=e.offset,c=new Map(u),i=!1;if(n){r(n);var s=[];c.forEach((function(e,a){n.some((function(e){return e.id===a}))||s.push(a)})),n.forEach((function(e){var a=e.id;c.has(a)||(c.set(a,!1),i||(i=!0))})),0!==s.length&&s.forEach((function(e){c.delete(e),i||(i=!0)}))}else r([]),0!==c.size&&(c.clear(),i=!0);(i&&m(new Map(c)),0!==o)&&(a=l<10?0:Math.floor(l/10),t=0===o%10?o/10:Math.ceil(o/10),S({current:a,total:t}))}),(function(e){M(e)}))}),[v,u,M]),q=function(){_(!1)},H=function(e,a){var t=new Map(u);t.set(a,e),m(t)};if(s.a.useEffect((function(){W()}),[W]),null===ca())return sa();var L,U=e.lang,V=Lu[U];if(l)if(0===l.length)L=s.a.createElement(fn,null,V.noResource);else{var G;G=f?s.a.createElement(K.a,{display:"flex",alignItems:"center"},s.a.createElement(K.a,null,s.a.createElement(Pe.a,{onChange:function(e){var a,t=e.target.checked,n=new Map,o=Object(Or.a)(u.keys());try{for(o.s();!(a=o.n()).done;){var l=a.value;n.set(l,t)}}catch(r){o.e(r)}finally{o.f()}m(n)}})),s.a.createElement(K.a,null,V.time)):V.time,L=s.a.createElement(Dr,{color:"primary",headers:[G,V.content],rows:l.map((function(e){var a=e.id;return s.a.createElement(Uu,{key:a,log:e,checked:!(!u||!u.has(a))&&u.get(a),checkable:f,onCheckStatusChanged:H})}))})}else L=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});var $=[];f?$.push(s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){_(!0)}},s.a.createElement(Wt.a,null),V.batchDelete),s.a.createElement(fe,{size:"sm",color:"rose",round:!0,onClick:function(){b(!1)}},s.a.createElement(Tr.a,null),V.exitBatch)):$.push(s.a.createElement(fe,{size:"sm",color:"info",round:!0,onClick:function(){var e,a=new Map,t=Object(Or.a)(u.keys());try{for(t.s();!(e=t.n()).done;){var n=e.value;a.set(n,!1)}}catch(o){t.e(o)}finally{t.f()}m(a),b(!0)}},s.a.createElement(Qo.a,null),V.enterBatch));var Z,Y=[{label:V.day,value:"last-day"},{label:V.month,value:"last-month"},{label:V.year,value:"last-year"}],J=s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(Te.a,{component:"fieldset",fullWidth:!0},s.a.createElement(Ie.a,{component:"legend"},V.duration),s.a.createElement(Ne.a,{name:"duration",value:v.duration,onChange:function(e){var a=e.target.value;y({offset:0,duration:a}),W()},row:!0},s.a.createElement(K.a,{display:"flex",alignItems:"center"},Y.map((function(e,a){return s.a.createElement(K.a,{key:a},s.a.createElement(Re.a,{value:e.value,control:s.a.createElement(_e.a,null),label:e.label}))})))))),Q=[];if(u&&(u.forEach((function(e,a){e&&Q.push(a)})),Q.sort()),C.total>1){for(var X=[],ee=function(){var e=te;te===C.current?X.push(s.a.createElement(oe.a,null,e+1)):X.push(s.a.createElement(ae.a,{href:"#",underline:"none",onClick:function(){return function(e){var a=10*e;y((function(e){return Object(E.a)(Object(E.a)({},e),{},{offset:a})}))}(e)}},e+1))},te=0;te<C.total;te++)ee();Z=s.a.createElement(K.a,{display:"flex",alignItems:"center",justifyContent:"center"},X.map((function(e,a){return s.a.createElement(K.a,{key:a,m:1,p:0},e)})))}else Z=s.a.createElement("div",null);return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},J))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{mt:3,mb:3},s.a.createElement(Ce.a,null))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{display:"flex"},$.map((function(e,a){return s.a.createElement(K.a,{key:a,m:1},e)})))),s.a.createElement(Qe,{xs:12,sm:12,md:12},s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",{className:a.cardTitleWhite},V.tableTitle)),s.a.createElement(dn,null,L,Z))),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:T,message:P,open:""!==P,closeNotification:B,close:!0})),s.a.createElement(Qe,null,s.a.createElement(Wu,{lang:U,open:w,targets:w?Q:[],onSuccess:function(e){var a;q(),a=e.toString()+" log(s) deleted",I("info"),F(a),setTimeout(B,3e3),Wa(e.toString()+" log(s) deleted"),W()},onCancel:q})))},layout:"/admin"},{path:"/visibilities",name:"visibility",display:{cn:"\u8d44\u6e90\u53ef\u89c1\u6027",en:"Resource Visibilities"},icon:ul.a,component:function(e){var a,t=e.lang,o=gu[t],l=bu(),r=s.a.useState(!1),c=Object(n.a)(r,2),i=c[0],u=c[1],m=s.a.useState({instanceVisible:!1,diskImageVisible:!1,mediaImageVisible:!1}),d=Object(n.a)(m,2),p=d[0],f=d[1],b=s.a.useState("warning"),g=Object(n.a)(b,2),h=g[0],v=g[1],y=s.a.useState(""),x=Object(n.a)(y,2),k=x[0],C=x[1],S=function(){C("")},O=s.a.useCallback((function(e){v("warning"),C(e),setTimeout(S,3e3)}),[v,C]),j=s.a.useCallback((function(){Ha("/resource_visibilities/",(function(e){var a={};e.instance_visible&&(a.instanceVisible=!0),e.disk_image_visible&&(a.diskImageVisible=!0),e.media_image_visible&&(a.mediaImageVisible=!0),f(a),u(!0)}),(function(e){O(e)}))}),[O]),w=function(e){return function(a){var t=a.target.checked;f((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}};if(s.a.useEffect((function(){j()}),[j]),null===ca())return sa();a=i?s.a.createElement(Dr,{color:"primary",headers:[o.visibility,o.visibility],rows:[s.a.createElement(Eu,{key:"instance",checked:p.instanceVisible,onChange:w("instanceVisible"),label:o.instance,description:o.instanceDescription,classes:l}),s.a.createElement(Eu,{key:"disk",checked:p.diskImageVisible,onChange:w("diskImageVisible"),label:o.disk,description:o.diskDescription,classes:l}),s.a.createElement(Eu,{key:"media",checked:p.mediaImageVisible,onChange:w("mediaImageVisible"),label:o.media,description:o.mediaDescription,classes:l})]}):s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});var _=[s.a.createElement(fe,{key:"modify",color:"info",onClick:function(){!function(e,a,t,n,o){var l={};e&&(l.instance_visible=e),a&&(l.disk_image_visible=a),t&&(l.media_image_visible=t),$a("/resource_visibilities/",l,n,o)}(p.instanceVisible,p.diskImageVisible,p.mediaImageVisible,(function(){var e;e="Visibilities updated",v("info"),C(e),setTimeout(S,3e3),Wa("Visibilities updated")}),O)}},s.a.createElement(Yo.a,null),o.modify)];return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},a),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{display:"flex"},_.map((function(e,a){return s.a.createElement(K.a,{key:a,m:1},e)})))),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:h,message:k,open:""!==k,closeNotification:S,close:!0})))},layout:"/admin"}],sm=function(e){var a;return{wrapper:{position:"relative",top:"0",height:"100vh"},mainPanel:Object(E.a)(Object(E.a)((a={},Object(Y.a)(a,e.breakpoints.up("md"),{width:"calc(100% - ".concat(260,"px)")}),Object(Y.a)(a,"overflow","auto"),Object(Y.a)(a,"position","relative"),Object(Y.a)(a,"float","right"),a),v),{},{maxHeight:"100%",width:"100%",overflowScrolling:"touch"}),content:{marginTop:"70px",padding:"30px 15px",minHeight:"calc(100vh - 123px)"},container:y,map:{marginTop:"70px"}}},um=t(315),mm=t.n(um),dm=t(316),pm=t.n(dm),fm=["routes"],bm=["lang","setLang"];function gm(e){var a=e.routes,t=Object($.a)(e,fm),n=a[0],o=n.layout+n.path;return s.a.createElement(b.d,null,a.map((function(e,a){return"/admin"===e.layout?s.a.createElement(b.b,{path:e.layout+e.path,render:function(){return s.a.createElement(e.component,t)},key:a}):null})),s.a.createElement(b.b,{path:"/admin/compute_cells/",render:function(){return s.a.createElement(Un,t)}}),s.a.createElement(b.a,{from:"/admin",to:o}))}var Em=Object(g.a)(sm);function hm(e){var a=e.lang,t=e.setLang,o=Object($.a)(e,bm),l=Em(),r=s.a.createRef(),c=s.a.useState(!1),i=Object(n.a)(c,2),u=i[0],m=i[1],d=function(){m(!u)},p=function(){window.innerWidth>=960&&m(!1)};s.a.useEffect((function(){return navigator.platform.indexOf("Win")>-1&&(Vu=new Z.a(r.current,{suppressScrollX:!0,suppressScrollY:!1}),document.body.style.overflow="hidden"),window.addEventListener("resize",p),function(){navigator.platform.indexOf("Win")>-1&&Vu.destroy(),window.removeEventListener("resize",p)}}),[r]);var f=ca();if(null===f)return sa();var b=f.menu,g=[];return im.forEach((function(e){b.some((function(a){return a===e.name}))&&g.push(e)})),s.a.createElement("div",{className:l.wrapper},s.a.createElement(_t,Object.assign({routes:g,logoText:"Nano Portal",logo:pm.a,image:mm.a,handleDrawerToggle:d,open:u,color:"blue",lang:a},o)),s.a.createElement("div",{className:l.mainPanel,ref:r},s.a.createElement(vt,Object.assign({routes:g,handleDrawerToggle:d,lang:a,setLang:t},o)),s.a.createElement("div",{className:l.content},s.a.createElement("div",{className:l.container},s.a.createElement(gm,{lang:a,routes:g})))))}var vm=t(204),ym=t.n(vm),xm=t(203),km=t.n(xm),Cm=ym()((function(){return{background:{backgroundImage:"url("+km.a+")",height:"100vh"}}})),Sm={cn:{title:"Nano\u7ba1\u7406\u95e8\u6237",user:"\u7528\u6237\u540d",password:"\u5bc6\u7801",login:"\u767b\u5f55"},en:{title:"Nano Web Portal",user:"User",password:"Password",login:"Login"}};function Om(e){var a,t,o=e.lang,l=e.setLang,r=Sm[o],c=Cm(),i=s.a.useState({user:"",password:"",nonce:"stub",type:"manager"}),u=Object(n.a)(i,2),m=u[0],d=u[1],p=s.a.useState(""),f=Object(n.a)(p,2),g=f[0],h=f[1],v=s.a.useState(!0),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState(!1),S=Object(n.a)(C,2),O=S[0],j=S[1],w=s.a.useState(!1),_=Object(n.a)(w,2),N=_[0],R=_[1],T=s.a.useCallback((function(e){h(e),setTimeout((function(){h("")}),5e3)}),[h]),I=function(e){return function(a){var t=a.target.value;d((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}},D=s.a.useCallback((function(e){T(e)}),[T]),A=function(e){!function(e){localStorage.setItem("nano-session-data",JSON.stringify(e))}({id:e.session,timeout:e.timeout,menu:e.menu,address:e.address,user:m.user,group:e.group,nonce:m.nonce,type:m.type}),N||(R(!0),Wa("login success"))};if(s.a.useEffect((function(){if(!O){qa((function(e){e.ready||k(!1),j(!0)}),D)}}),[O,D]),O){if(!x)return s.a.createElement(b.a,{to:"/initial"});if(N){var P=new URLSearchParams(window.location.search);return P.has("previous")?s.a.createElement(b.a,{to:decodeURIComponent(P.get("previous"))}):s.a.createElement(b.a,{to:"/admin"})}var F;F=g?s.a.createElement(Qe,{xs:12},s.a.createElement(na,{message:g,color:"danger"})):s.a.createElement(Qe,{xs:12}),t=s.a.createElement(fe,{color:"info",onClick:function(){var e,a,t,n;e=m.user,a=m.password,t=A,n=D,Ga("/sessions/",{user:e,password:a,nonce:function(){for(var e="",a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",t=0;t<16;t++)e+=a.charAt(Math.floor(Math.random()*a.length));return e}()},t,n)}},r.login),a=s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:r.user,onChange:I("user"),value:m.user,margin:"normal",required:!0,fullWidth:!0}))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:r.password,onChange:I("password"),value:m.password,margin:"normal",type:"password",required:!0,fullWidth:!0}))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{alignItems:"center",display:"flex",m:1},t,s.a.createElement(K.a,{flexGrow:1}),s.a.createElement(bt,{lang:o,setLang:l}))),s.a.createElement(Qe,{xs:12},F))}else a=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}});return s.a.createElement(K.a,{component:"div",className:c.background},s.a.createElement(Ei.a,{maxWidth:"lg"},s.a.createElement(De.a,{container:!0,justify:"center"},s.a.createElement(De.a,{item:!0,xs:10,sm:6,md:4},s.a.createElement(K.a,{mt:20,p:0},s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",{className:c.cardTitleWhite},r.title)),s.a.createElement(dn,null,a)))))))}var jm=ym()((function(){return Object(E.a)(Object(E.a)({},Yn),{},{background:{backgroundImage:"url("+km.a+")",height:"100vh"}})})),wm={cn:{welcome:"\u6b22\u8fce\u4f7f\u7528Nano\u4e91\u5e73\u53f0",description:"\u8bf7\u8bbe\u5b9a\u7ba1\u7406\u5458\u8d26\u53f7\u53ca\u5bc6\u7801\uff0c\u5f00\u59cb\u521d\u59cb\u5316\u7cfb\u7edf",user:"\u9ed8\u8ba4\u7ba1\u7406\u5458\u5e10\u53f7",password:"\u5bc6\u7801",password2:"\u786e\u8ba4\u5bc6\u7801",initial:"\u521d\u59cb\u5316",confirm:"\u786e\u8ba4",success:"\u7cfb\u7edf\u521d\u59cb\u5316\u6210\u529f\uff0c\u70b9\u51fb\u8fdb\u5165\u767b\u5f55\u9875\u9762"},en:{welcome:"Welcome to Nano",description:"Please set up a new admin account",user:"Super Admin Name",password:"Password",password2:"Confirm Password",initial:"Initial System",confirm:"Confirm",success:"System initialed, click to login"}};function _m(e){var a,t,o,l=0,r=1,c=2,i=3,u=jm(),m=e.lang,d=e.setLang,p=wm[m],f=s.a.useState({user:"",password:"",password2:""}),g=Object(n.a)(f,2),h=g[0],v=g[1],y=s.a.useState(""),x=Object(n.a)(y,2),k=x[0],C=x[1],S=s.a.useState(l),O=Object(n.a)(S,2),j=O[0],w=O[1],_=function(e){return function(a){var t=a.target.value;v((function(a){return Object(E.a)(Object(E.a)({},a),{},Object(Y.a)({},e,t))}))}},N=s.a.useCallback((function(e){C(e),setTimeout((function(){C("")}),5e3)}),[]),R=function(){C(""),w(c)},T=s.a.useCallback((function(){w(i)}),[i]);switch(s.a.useEffect((function(){qa((function(e){e.ready?T():w(r)}),N)}),[r,N,T]),a=k&&""!==k?s.a.createElement(Qe,{xs:12},s.a.createElement(na,{message:k,color:"danger"})):s.a.createElement(Qe,{xs:12}),j){case r:o=s.a.createElement(fe,{color:"info",onClick:function(){var e=new RegExp("[^\\w-.]");if(h.user)if(e.test(h.user))N("only letter/digit/'-'/'_'/'.' allowed in username");else if(h.password)if(h.password2===h.password){var a=[];pa(m).forEach((function(e){a.push(e.value)})),function(e,a,t,n,o,l,r,c){var i={user:e,password:o,menu:l};a&&(i.group=a),t&&(i.display=t),n&&(i.role=n),Ga("/system/",i,r,c)}(h.user,null,null,null,h.password,a,R,N)}else N("password mismatch");else N("please input password");else N("must specify user name")}},p.initial),t=s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{justifyContent:"center",display:"flex"},s.a.createElement(oe.a,{className:u.cardTitle},p.description))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:p.user,onChange:_("user"),value:h.user,margin:"normal",required:!0,fullWidth:!0}))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:p.password,onChange:_("password"),value:h.password,margin:"normal",type:"password",required:!0,fullWidth:!0}))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{m:0,pt:2},s.a.createElement(we.a,{label:p.password2,onChange:_("password2"),value:h.password2,margin:"normal",type:"password",required:!0,fullWidth:!0}))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{alignItems:"center",display:"flex",m:1},o,s.a.createElement(K.a,{flexGrow:1}),s.a.createElement(bt,{lang:m,setLang:d}))),s.a.createElement(Qe,{xs:12},a));break;case c:o=s.a.createElement(fe,{color:"info",onClick:function(){T()}},p.confirm),t=s.a.createElement(De.a,{container:!0},s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{justifyContent:"center",display:"flex"},s.a.createElement(oe.a,{variant:"body1",component:"span",className:u.cardTitle},p.success))),s.a.createElement(Qe,{xs:12},s.a.createElement(K.a,{justifyContent:"center",display:"flex"},o)));break;case i:return s.a.createElement(b.a,{to:"/login"});default:t=s.a.createElement("div",null,s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}}),a)}return s.a.createElement(K.a,{component:"div",className:u.background},s.a.createElement(Ei.a,{maxWidth:"lg"},s.a.createElement(De.a,{container:!0,justify:"center"},s.a.createElement(De.a,{item:!0,xs:12,sm:8,md:4},s.a.createElement(K.a,{mt:20,p:0},s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},s.a.createElement("h4",{className:u.cardTitleWhite},p.welcome)),s.a.createElement(dn,null,t)))))))}var Nm=t(318),Rm=t.n(Nm),Tm=t(317),Im=t.n(Tm);function Dm(e){var a=e.url,t=e.password,o=e.callback,l=e.onFocusChanged,r=s.a.createRef(),c=s.a.useState(null),i=Object(n.a)(c,2),u=i[0],m=i[1],d=s.a.useState(!1),p=Object(n.a)(d,2),f=p[0],b=p[1],g=s.a.useState(!1),E=Object(n.a)(g,2),h=E[0],v=E[1],y=function(){f&&u&&(u.focus(),l(!0))};return s.a.useEffect((function(){if(r&&r.current&&a&&t){if(b(!0),!h){var e={credentials:{password:t},clipViewport:!0,focusOnClick:!1,qualityLevel:8},n=new Im.a(r.current,a,e);m(n),v(!0)}return function(){b(!1)}}}),[r,t,a,h]),o.onEmergency=function(){f&&u&&u.sendCtrlAltDel()},s.a.createElement("div",{ref:r,onMouseOver:y,onMouseOut:function(){f&&u&&(u.blur(),l(!1))},onMouseDown:function(e){e.preventDefault(),y()}})}var Am={en:{instance:"Instance",sendKeys:"Send Ctrl+Alt+Delete",stop:"Stop Instance",reboot:"Reboot Instance",reset:"Reset Instance",insertMedia:"Insert Media",ejectMedia:"Eject Media",activated:"The input is already redirected, move out the mouse to release control",deactivated:"Move the mouse to the screen to control the instance"},cn:{instance:"\u4e91\u4e3b\u673a",sendKeys:"\u53d1\u9001 Ctrl+Alt+Delete",stop:"\u505c\u6b62\u4e91\u4e3b\u673a",reboot:"\u91cd\u542f\u4e91\u4e3b\u673a",reset:"\u5f3a\u5236\u91cd\u542f\u4e91\u4e3b\u673a",insertMedia:"\u63d2\u5165\u5149\u76d8\u955c\u50cf",ejectMedia:"\u5f39\u51fa\u5149\u76d8\u955c\u50cf",activated:"\u8f93\u5165\u5df2\u91cd\u5b9a\u5411\u5230\u4e91\u4e3b\u673a\uff0c\u9f20\u6807\u79bb\u5f00\u753b\u9762\u89e3\u9664\u63a7\u5236",deactivated:"\u9f20\u6807\u79fb\u52a8\u5230\u76d1\u63a7\u753b\u9762\u5f00\u59cb\u63a7\u5236\u4e91\u4e3b\u673a"}};function Pm(e){var a,t,o=e.match.params.id,l=e.lang,r=s.a.useState(null),c=Object(n.a)(r,2),i=c[0],u=c[1],m=s.a.useState(!1),d=Object(n.a)(m,2),p=d[0],f=d[1],b=s.a.useState(!1),g=Object(n.a)(b,2),E=g[0],h=g[1],v=s.a.useState(!1),y=Object(n.a)(v,2),x=y[0],k=y[1],C=s.a.useState(!1),S=Object(n.a)(C,2),O=S[0],j=S[1],w=s.a.useState(!1),_=Object(n.a)(w,2),N=_[0],R=_[1],T=s.a.useState("warning"),I=Object(n.a)(T,2),D=I[0],A=I[1],P=s.a.useState(""),F=Object(n.a)(P,2),B=F[0],M=F[1],z=Am[l],W=function(){M("")},q=s.a.useCallback((function(e){if(E){A("warning"),M(e),setTimeout(W,3e3)}}),[E,A,M]),H=function(e){if(E){A("info"),M(e),Wa(e),setTimeout(W,3e3)}},L=s.a.useCallback((function(e){q(e)}),[q]),U=function(){R(!1)};if(s.a.useEffect((function(){if(o){if(h(!0),!O&&!x){j(!0);xa(o,(function(e){!function(e,a,t){Ua("/monitor_channels/",{guest:e},(function(e){a(e.id,e.password)}),t)}(o,(function(a,t){var n={name:e.name,pool:e.pool,cell:e.cell,channel:a,password:t,delegate:{}};u(n),k(!0),j(!1)}),L)}),L)}return function(){h(!1)}}}),[o,L,x,O]),x){var V=function(e){var a="/api/v1/monitor_channels/"+e;if(a.startsWith("ws://"))return a;if(a.startsWith("http://"))return a.replace("http://","ws://");if(a.startsWith("https://"))return a.replace("https://","ws://");var t="ws://"+window.location.hostname;return window.location.port&&(t+=":"+window.location.port),t+a}(i.channel);a=s.a.createElement(Dm,{url:V,password:i.password,callback:i.delegate,onFocusChanged:function(e){f(e)}});var G=[{tips:z.sendKeys,icon:Rm.a,handler:function(){i&&i.delegate&&i.delegate.onEmergency&&i.delegate.onEmergency()}},{tips:z.insertMedia,icon:nc.a,handler:function(){R(!0)}},{tips:z.ejectMedia,icon:lc.a,handler:function(){Ca(o,(function(){H("media of instance "+i.name+" ejected")}),L)}},{tips:z.stop,icon:wr.a,handler:function(){Sa(o,(function(){H("instance "+i.name+" stopped")}),L)}},{tips:z.reboot,icon:Xr.a,handler:function(){Oa(o,(function(){H("instance "+i.name+" reboot")}),L)}},{tips:z.reset,icon:ac.a,handler:function(){ja(o,(function(){H("instance "+i.name+" reset")}),L)}}];t=s.a.createElement(K.a,{display:"flex",alignItems:"center"},s.a.createElement(K.a,{flexGrow:1,fontWeight:"fontWeightBold",letterSpacing:10},s.a.createElement(oe.a,{component:"span"},z.instance+": "+i.name+"  ("+(p?z.activated:z.deactivated)+")")),G.map((function(e,a){return s.a.createElement(K.a,{key:a},s.a.createElement(te.a,{title:e.tips,placement:"top"},s.a.createElement(ne.a,{color:"inherit",onClick:e.handler},s.a.createElement(e.icon))))})))}else a=s.a.createElement(Nt.a,{variant:"rect",style:{height:"10rem"}}),t=s.a.createElement(K.a,null);return s.a.createElement(Xt,null,s.a.createElement(Qe,{xs:12},s.a.createElement(nn,null,s.a.createElement(cn,{color:"primary"},t),s.a.createElement(dn,null,a))),s.a.createElement(Qe,null,s.a.createElement(En,{place:"tr",color:D,message:B,open:""!==B,closeNotification:W,close:!0})),s.a.createElement(Qe,null,s.a.createElement(jc,{lang:l,instanceID:o,open:N,onSuccess:function(){U(),H("media insert into instance "+i.name)},onCancel:U})))}t(522);var Fm=function(e){Object(r.a)(t,e);var a=Object(c.a)(t);function t(e){var n;return Object(o.a)(this,t),(n=a.call(this,e)).state={hasError:!1,error:null,errorStack:null},n}return Object(l.a)(t,[{key:"render",value:function(){return this.state.hasError?s.a.createElement(G,null,this.state.error):this.props.children}}],[{key:"getDerivedStateFromError",value:function(e){return{hasError:!0,error:e.message,errorStack:e.stack}}}]),t}(s.a.Component),Bm=Object(f.a)(),Mm=Object(d.a)({palette:{primary:{light:k[1],main:k[0]},secondary:{light:j[1],main:j[0]},error:{light:S[1],main:S[0]}}});function zm(e){var a=s.a.useState(function(){var e=localStorage.getItem("nano-language-data");if(!e||0===e.length)return"cn";var a=JSON.parse(e);return a.lang?a.lang:"cn"}()),t=Object(n.a)(a,2),o=t[0],l=t[1];return s.a.createElement(p.a,{theme:Mm},s.a.createElement(Fm,null,s.a.createElement(b.c,{history:Bm},s.a.createElement(b.d,null,s.a.createElement(b.b,{path:"/admin",render:function(e){return s.a.createElement(hm,{lang:o,setLang:l})}}),s.a.createElement(b.b,{path:"/login",render:function(e){return s.a.createElement(Om,{lang:o,setLang:l})}}),s.a.createElement(b.b,{path:"/initial",render:function(e){return s.a.createElement(_m,{lang:o,setLang:l})}}),s.a.createElement(b.b,{path:"/monitor/:id",render:function(e){return s.a.createElement(Pm,Object.assign({lang:o},e))}}),s.a.createElement(b.a,{from:"/",to:"/login"})))))}m.a.render(s.a.createElement(zm,null),document.getElementById("root"))}},[[346,1,2]]]);
//# sourceMappingURL=main.8156b384.chunk.js.map