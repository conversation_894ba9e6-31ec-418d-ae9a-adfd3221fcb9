FORMAT: 1A

# Nano API

REST API provided by core service

# Data Structures

## QoS (object)
+ cpu_priority (enum[string], optional) - CPU priority
  + Default: 'medium'
  + Members
    + 'high'
    + 'medium'
    + 'low'

+ write_speed: 10485760 (number, optional) - disk write bps limit, 0 = unlimited
+ write_iops: 100 (number, optional) - disk write iops limit, 0 = unlimited
+ read_speed: 10485760 (number, optional) - disk read bps limit, 0 = unlimited
+ read_iops: 100 (number, optional) - disk read iops limit, 0 = unlimited
+ receive_speed: 10485760 (number, optional) - network receive bps, 0 = unlimited
+ send_speed: 10485760 (number, optional) - network send bps, 0 = unlimited

## CloudInit (object)
+ root_enabled: true (boolean, optional) - allow root user login via SSH, enable in default
+ admin_name: 'nano' (string, optional) - specify admin name, using 'root' when omitted
+ admin_secret (string, optional) - password in plain text, nano generate a new one when omitted
+ data_path (string, optional) - data disk mount path, '/opt/data' in default

## Visibility (object)

+ instance_visible: true (boolean) - user can view instances in the same group
+ media_image_visible: true (boolean) - user can view media images in the same group
+ disk_image_visible: false (boolean) - user can view disk images in the same group

# Group Resource Pool

## Compute Pool [/compute_pools/{pool}]

manage compute pool

+ Parameters
  + pool (string, optional) - pool name/id

### Get all pools in zone [GET /compute_pools/]

query all compute pool info in zone

+ Response 200 (application/json)

        [
        {
              "error_code": 0,
              "message": "",
              "data":[
                {
                    "name": "default",
                    "enabled": true,
                    "cells": 10,
                    "storage": "some_ceph_cluster",
                    "network": "nat_address_pool_1",
                    "failover": true
                },
                {
                    "name": "fast_instance",
                    "enabled": true
                    "cells": 12,
                    "storage": "some_ceph_cluster",
                    "network": "nat_address_pool_1",
                    "failover": false
                }
            ]
        }
        ]

### Query Pool Info [GET]

query compute pool details

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "name": "default",
            "enabled": true,
            "cells": 10,
            "storage": "some_ceph_cluster",
            "network": "nat_address_pool_1",
            "failover": true
          }
        }
        ]

### Create Compute Pool [POST]

create new compute pool

+ Attributes
  + cells(array[string], optional) - list of initial cell names
  + network_mode (enum[string], optional) - plain/share/mono
  + storage (string, optional) - attached storage pool name
  + network (string, optional) - attached address pool name
  + failover (boolean, optional) - enable failover


+ Request create compute pool (application/json)

        [
        {
          "cells":["cell1", "cell2"],
          "network_mode": "share",
          "storage": "some_ceph_cluster",
          "network": "nat_address_pool_1",
          "failover": true
        }
        ]

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Delete Compute Pool [DELETE]

delete compute pool

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]


### Modify Compute Pool [PUT]

modify compute pool config

+ Attributes

  + enable(boolean, optional) - set pool enable/disable
  + network_mode (enum[string], optional) - plain/share/mono
  + storage (string, optional) - attached storage pool name
  + network (string, optional) - attached address pool name

+ Request change compute pool (application/json)

        [
        {
          "failover": true,
          "storage": "nfs-poo1"
        }
        ]

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Compute Pool Cell [/compute_pool_cells/{pool}/{cell}]


+ Parameters

  + pool (string, optional) - pool name
  + cell (string, optional) - cell name

### Query unallocated cells [GET /compute_pool_cells/]

query cells not attached to any pool

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data": [
            {
              "name": "cell3",
              "address": "***********",
              "enabled": true,
              "alive": true,
            },
            {
              "name": "cell4",
              "address": "***********",
              "enabled": true,
              "alive": true,
            }
          ]
        }
        ]

### Query cells in pool [GET /compute_pool_cells/{pool}]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data": [
          {
            "name": "cell1",
            "address": "**********",
            "enabled": true,
            "alive": true,
          },
          {
            "name": "cell2",
            "address": "**********",
            "enabled": true,
            "alive": true,
          }
          ]
        }
        ]

### Add Pool Cell [POST]

Add {cell} to {pool}

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]


### Remove Pool Cell [DELETE]

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]


### Modify Pool Cell [PUT]

modify compute pool config

+ Attributes

    + enable(boolean, optional) - enable/disable cell

+ Request change compute pool (application/json)

        [
        {
          "enable": false
        }
        ]

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Get Cell Config [GET]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data": {
            "name": "cell1",
            "address": "**********",
            "enabled": true,
            "alive": true,
            "storage": [
            {
              "name": "nfspool1",
              "attached": true
            },
            {
              "name": "cephpool1",
              "attached": false,
              "error": "not support yet"
            }
            ]
          }
        }
        ]

## Storage Pool [/storage_pools/{pool}]

### Query Storage Pool [GET /storage_pools/]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            {
              "name": "nfspool1",
              "type": "nfs",
              "host": "nfs.nano.com",
              "target": "/var/nano/pool1"
            },
            {
              "name": "cephpool1",
              "type": "ceph",
              "host": "ceph.nano.com",
              "target": "some_pool_in_ceph"
             }
          ]
        }
        ]


### Get Storage Pool Info [GET]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data": {
            "type": "nfs",
            "host": "nfs.nano.com",
            "target": "/var/nano/pool1"
          }
        }
        ]

### Create Storage Pool [POST]

Create a new stoarge pool

+ Attributes
  + type (string) - storage type
  + host (string, optional) - ip or hostname of pool target
  + target (string, optional) - pool targe path

+ Request create storage pool (application/json)

        [
        {
          "type": "nfs",
          "host": "nfs.nano.com",
          "target": "/var/nano/pool1"
        }
        ]

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Modify Storage Pool [PUT]

Modify stoarge pool, only empty pool allowed(no volumes available)

+ Attributes
  + type (string) - storage type
  + host (string, optional) - ip or hostname of pool target
  + target (string, optional) - pool targe path

+ Request create storage pool (application/json)

        [
        {
          "type": "nfs",
          "host": "another_nfs.nano.com",
          "target": "/var/nano/new_location"
        }
        ]

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Delete Storage Pool [DELETE]

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Compute Zone Status [/compute_zone_status/]

### query zone status [GET]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "name": "default",
            "pools": [1, 0]//[disabled, enabled]
            "cells": [0, 25], //[offline, online]
            "instances": [3, 100, 3, 1], //[stopped, running, lost, migrate]
            "cpu_usage": 156.45,
            "max_cpu": 320,
            "available_memory": 560,
            "max_memory": 960,
            "available_disk": 12457,
            "max_disk": 34000,
            "read_speed": 8634,
            "write_speed": 3673,
            "receive_speed": 7634,
            "send_speed": 2643,
            "start_time": "2018-01-02 15:04:05"
          }
        }
        ]

## Compute Pool Status [/compute_pool_status/{pool}]

+ Parameters
  + pool (string, optional) - target pool name

### query pools status in zone [GET]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[{
            "name": "default",
            "enabled": true,
            "cells": [0, 25], //[offline, online]
            "instances": [3, 100, 3, 1], //[stopped, running, lost, migrate]
            "cpu_usage": 156.45,
            "max_cpu": 320,
            "available_memory": 560,
            "max_memory": 960,
            "available_disk": 12457,
            "max_disk": 34000,
            "read_speed": 8634,
            "write_speed": 3673,
            "receive_speed": 7634,
            "send_speed": 2643
          },
          {
            "name": "pool2",
            "enabled": true,
            "cells": [0, 15], //[offline, online]
            "instances": [3, 50, 0, 0], //[stopped, running, lost, migrate]
            "cpu_usage": 156.45,
            "max_cpu": 320,
            "available_memory": 560,
            "max_memory": 960,
            "available_disk": 12457,
            "max_disk": 34000,
            "read_speed": 8634,
            "write_speed": 3673,
            "receive_speed": 7634,
            "send_speed": 2643
          }]
        }
        ]

### query compute pool status [GET /compute_pool_status/{pool}]


+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "name": "default",
            "enabled": true,
            "cells": [0, 25], //[offline, online]
            "instances": [3, 100, 3, 1], //[stopped, running, lost, migrate]
            "cpu_usage": 156.45,
            "max_cpu": 320,
            "available_memory": 560,
            "max_memory": 960,
            "available_disk": 12457,
            "max_disk": 34000,
            "read_speed": 8634,
            "write_speed": 3673,
            "receive_speed": 7634,
            "send_speed": 2643
          }
        }
        ]

## Compute Cell Status [/compute_cell_status/{pool}/{cell}]

+ Parameters
  + pool (string) - target pool name
  + cell (string, optional) - target cell name

### query compute cell status [GET /compute_cell_status/{pool}/]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            {
              "name": "cell_93e3de24f0a4",
              "address": "**********",
              "enabled": true,
              "alive": true,
              "instances": [3, 15, 0, 0], //[stopped, running, lost, migrate]
              "cpu_usage": 156.45,
              "max_cpu": 320,
              "available_memory": 560,
              "max_memory": 960,
              "available_disk": 12457,
              "max_disk": 34000,
              "read_speed": 8634,
              "write_speed": 3673,
              "receive_speed": 7634,
              "send_speed": 2643
            },
            {
              "name": "cell_93e3de24f0a3",
              "address": "**********",
              "enabled": true,
              "alive": true,
              "instances": [0, 12, 1, 0], //[stopped, running, lost, migrate]
              "cpu_usage": 156.45,
              "max_cpu": 320,
              "available_memory": 560,
              "max_memory": 960,
              "available_disk": 12457,
              "max_disk": 34000,
              "read_speed": 8634,
              "write_speed": 3673,
              "receive_speed": 7634,
              "send_speed": 2643
            }
          ]
        }
        ]

### get compute cell status [GET /compute_cell_status/{pool}/{cell}]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "name": "cell_93e3de24f0a4",
            "address": "**********",
            "enabled": true,
            "alive": true,
            "instances": [3, 15, 0, 0], //[stopped, running, lost, migrate]
            "cpu_usage": 156.45,
            "max_cpu": 320,
            "available_memory": 560,
            "max_memory": 960,
            "available_disk": 12457,
            "max_disk": 34000,
            "read_speed": 8634,
            "write_speed": 3673,
            "receive_speed": 7634,
            "send_speed": 2643
          }
        }
        ]

## Instance Status [/instance_status/{pool}/{cell}/]

+ Parameters
  + pool (string) - target pool name
  + cell (string, optional) - target cell name

### Query instance in pool [GET /instance_status/{pool}/]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data": [{
            "name": "test01",
            "id": "df6723jhew67f3fdsf-fefew",
            "created": true,
            "running": true,
            "cores": 4,
            "memory": 5120,
            "disks": [4864000, 854365],
            "auto_start": true,
            "system": "linux",
            "display_protocol": "vnc",
            "monitor_secret": "abd",
            "internal":{
              "network_address": "**********",
              "display_address": "**********:5901",
              "allocated_address": "**********"
            },
            "external":{
              "network_address": "**********",
              "display_address": "**********:5901"
            },
            "create_time": "2018-08-21 00:12:34",
            "media_attached": true,
            "media_source": "centos_7_x64_iso"
          },
          {
            "name": "test02",
            "id": "dr6ufh73dgjf3fdsf-fefew",
            "created": false,
            "progress": 46,
            "running": false,
            "cores": 4,
            "memory": 5120,
            "disks": [4864000, 854365],
            "auto_start": false,
            "system": "linux"
          }]
        }
        ]

### Query instance in cell [GET /instance_status/{pool}/{cell}]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data": [{
            "name": "test01",
            "id": "df6723jhew67f3fdsf-fefew",
            "created": true,
            "running": true,
            "cores": 4,
            "memory": 5120,
            "disks": [4864000, 854365],
            "auto_start": true,
            "system": "linux",
            "display_protocol": "vnc",
            "monitor_secret": "abd",
            "internal":{
              "network_address": "**********",
              "display_address": "**********:5901",
              "allocated_address": "**********"
            },
            "external":{
              "network_address": "**********",
              "display_address": "**********:5901"
            },
            "create_time": "2018-08-21 00:12:34",
            "media_attached": true,
            "media_source": "centos_7_x64_iso"
          },
          {
            "name": "test02",
            "id": "dr6ufh73dgjf3fdsf-fefew",
            "created": false,
            "progress": 46,
            "running": false,
            "cores": 4,
            "memory": 5120,
            "disks": [4864000, 854365],
            "auto_start": false,
            "system": "windows"
          }]
        }
        ]

## Address Pool [/address_pools/{name}]

+ Parameters
  + name (string, optional) - pool name

### Query all address pools [GET /address_pools/]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            {
              "name": "address1",
              "gateway": "************",
              "addresses": 120,
              "allocated": 17
            },
            {
              "name": "address2",
              "gateway": "************",
              "addresses": 230,
              "allocated": 10
            }
          ]
        }
        ]

### Get address pool status [GET]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "gateway": "************",
            "dns": [
              "*******",
              "***********"
            ],
            "ranges": [
              {
                "start": "**********",
                "end": "**********8",
                "netmask": "*************",
              },
              {
                "start": "***********",
                "end": "**********90",
                "netmask": "*************",
              }
            ],
            "allocated": [
              {
                  "address": "**********",
                  "instance": "abcdef-1234567890",
              },
              {
                  "address": "**********7",
                  "instance": "abcdef-1234567893",
              }
            ]
          }
        }
        ]

### Create new address pool [POST]

+ Attributes

    + gateway(string) - gateway bound with instance
    + dns (array[string]) - list of dns server bound with instance

+ Request Create New Address Pool (application/json)

        {
          "gateway": "************",
          "dns": [
            "*******",
            "***********"
          ]
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Modify address pool [PUT]

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Delete address pool [DELETE]

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]


## Address Range [/address_pools/{name}/{type}/ranges/{start}]

+ Parameters
  + name (string) - name of address pool
  + type (enum[string]) - range type, 'external'/'internal'
  + start (string, optional) - start address of range

### Query Address Ranges [GET /address_pools/{name}/{type}/ranges/]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            {
              "start": "**********",
              "end": "**********8",
              "netmask": "*************"
            },
            {
              "start": "***********",
              "end": "***********0",
              "netmask": "*************"
            }
          ]
        }
        ]

### Get Address Ranges Status [GET]

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":{
              "start": "**********",
              "end": "**********8",
              "netmask": "*************",
              "allocated": [
                {
                    "address": "**********",
                    "instance": "abcdef-1234567890",
                },
                {
                    "address": "**********7",
                    "instance": "abcdef-1234567893",
                }
              ]
            }
        }
        ]


### Add Address Range [POST]

+ Attributes

    + end (string) - end address of ip range
    + netmask (string) - netmask of ip range

+ Request Add Address Range (application/json)

        {
          "end": "**********8",
          "netmask": "*************"
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Remove Address Range [DELETE]

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

# Group Virtual machines

## Guest Search [/guest_search/{?pool,session,cell,owner,group,status,created}]

### Search Guests [GET]

+ Parameters

  + pool (string) - target pool name
  + session (string) - login session ID
  + cell (string, optional) - target cell
  + status (number, optional) - running status
  + created (boolean, optional) - guest created

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            {
                "name": "guest1",
                "id": "df6723jhew67f3fdsf-fefew",
                "running": false,
                "owner": "admin",
                "group": "manager",
                "pool": "pool1",
                "cell": "cell_93e3de24f0a4",
                "cores": 4,
                "memory": 5120,
                "total_disk": 4864000,
                "disks": [482345, 487534],
                "auto_start": true,
                "system": "windows",
                "ethernet_address": "ed:35:3d:5a:4e:3f",
                "display_protocol": "vnc",
                "internal":{
                  "network_address": "**********",
                  "display_address": "**********:5901",
                  "allocated_address": "**********"
                },
                "external":{
                  "network_address": "**********",
                  "display_address": "**********:5901"
                },
                "create_time": "2018-08-21 00:12:34",
                "qos": {
                    "cpu_priority": "high",
                    "write_speed": 1048576,
                    "write_iops": 100,
                    "read_speed": 1048576,
                    "read_iops": 100,
                    "receive_speed": 10485760,
                    "send_speed": 10485760
                }
              }
            ],
          [
            {
                "name": "guest2",
                "id": "df6723jhew67f3fdsf-fefet",
                "running": false,
                "owner": "admin",
                "group": "manager",
                "pool": "pool1",
                "cell": "cell_93e3de24f0a4",
                "cores": 4,
                "memory": 5120,
                "total_disk": 4864000,
                "disks": [482345, 487534]
                "auto_start": true,
                "system": "linux",
                "ethernet_address": "ed:35:3d:5a:4e:3f",
                "display_protocol": "vnc",
                "internal":{
                  "network_address": "**********",
                  "display_address": "**********:5901",
                  "allocated_address": "**********"
                },
                "external":{
                  "network_address": "**********",
                  "display_address": "**********:5901"
                },
                "create_time": "2018-08-21 00:12:34",
                "qos": {
                    "cpu_priority": "high",
                    "write_speed": 1048576,
                    "write_iops": 100,
                    "read_speed": 1048576,
                    "read_iops": 100,
                    "receive_speed": 10485760,
                    "send_speed": 10485760
                }
              }
          ]
        }
        ]

## Guests [/guests/]

VM config/storage/network

### Get Guest Details [GET /guests/{id}]

+ Parameters
  + id (string, optional) - guest id


+ Response 201 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "name": "test01",
            "created": false
            "progress": 57 //limit to 100
          }
        }
        ]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "name": "test01",
            "created": true,
            "running": false
            "owner": "admin",
            "group": "manager",
            "pool": "pool1",
            "cell": "cell_93e3de24f0a4"
            "cores": 4,
            "memory": 5120,
            "total_disk": 4864000,
            "disks": [482345, 487534],
            "auto_start": true,
            "system": "linux",
            "ethernet_address": "ed:35:3d:5a:4e:3f"
            "display_protocol": "vnc",
            "monitor_secret": "abd",
            "internal":{
              "network_address": "**********",
              "display_address": "**********:5901",
              "allocated_address": "**********"
            },
            "external":{
              "network_address": "**********",
              "display_address": "**********:5901"
            },
            "create_time": "2018-08-21 00:12:34",
            "qos": {
                "cpu_priority": "high",
                "write_speed": 1048576,
                "write_iops": 100,
                "read_speed": 1048576,
                "read_iops": 100,
                "receive_speed": 10485760,
                "send_speed": 10485760
            }
          }
        }
        ]

### Create Guest [POST]

request create guest, return a new guest id for query after creating request submit

+ Attributes

    + name (string) - display name
    + owner (string) - user id
    + group (string) - user group
    + pool (string) - compute pool name
    + cores (number) - vm cpu cores
    + memory (number) - vm memory
    + disks (array[number]) - [system_disk_size, data_disk_n_size...]
    + auto_start (boolean) - auto start instance
    + system (string, optional) - system type, "linux", "windows", etc
    + network_address (string, optional) - optional specified ip address
    + ethernet_address (string, optional) - optional specified mac address
    + from_image (string, optional) - initial disk image id
    + ports (array[number], optional) - nat ports
    + system_version (string, optional) - os version, 'centos7', 'win2012'
    + modules (array[string], optional) - installed guest module, "qemu"/"cloud-init"
    + cloud_init (CloudInit, optional) - cloud-init params
    + qos (QoS, optional) - qos config

+ Request Create New Guest (application/json)

        {
          "name": "some_instance",
          "owner": "admin",
          "group": "manager",
          "pool": "pool_1",
          "cores": 8,
          "memory": 4048,
          "disks": [4048, 38443],
          "auto_start": true,
          "system": "linux",
          "system_version": "centos7",
          "modules": ["qemu", "cloud-init"],
          "cloud_init":{
            "root_enabled": true,
            "admin_name": "nano",
            "admin_secret": "12345678",
            "data_path": "/opt/data"
          }
        }

+ Response 202 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "id": "dsfds8979847r3dsf-3r67",
          }
        }
        ]


### Delete Guest [DELETE /guests/{id}]

delete guest, release all resource back to pool

+ Attributes

    + force (boolean, optional) - stop running instance for deleting

+ Request delete guest (application/json)

        {
            "force": true
        }


+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Guest Cores [/guests/{id}/cores]

+ Parameters

    + id (string) - UUID of guest

### Modify Cores [PUT]

+ Attributes

    + cores(number) - new cores
    + immediate (boolean, optional) - take effect when running

+ Request modify cores (application/json)

        {
          "cores": 4,
          "immediate": false
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Guest Memory [/guests/{id}/memory]

+ Parameters

    + id (string) - UUID of guest

### Modify Memory [PUT]

+ Attributes

    + memory(number) - memory in bytes
    + immediate (boolean, optional) - take effect when running

+ Request modify memory (application/json)

        {
          "memory": 4294967296,
          "immediate": false
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Resize Guest Disk [/guests/{id}/disks/resize/{disk}]

+ Parameters
  + id (string) - UUID of guest
  + disk (number) - index of target disk, system:0, data0:1, data1:2...

### Modify Disk Size [PUT]

+ Attributes

    + size(number) - size in bytes
    + immediate (boolean, optional) - take effect when running

+ Request modify disk (application/json)

        {
          "size": 42949672960,
          "immediate": false
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Shrink Guest Disk [/guests/{id}/disks/shrink/{disk}]

+ Parameters
  + id (string) - UUID of guest
  + disk (number) - index of target disk, system:0, data0:1, data1:2...

### Shrink Disk [PUT]

+ Attributes

    + immediate (boolean, optional) - take effect when running

+ Request shrink disk (application/json)

        {
          "immediate": false
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Guest System [/guests/{id}/system/]

+ Parameters

    + id (string) - UUID of guest

### Reset Guest System [PUT]

+ Attributes

    + from_image (string) - id of new system image

+ Request Reset Guest System (application/json)

        {
          "from_image": "0123456789-abcdef"
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Guest User Auth [/guests/{id}/auth]

+ Parameters

    + id (string) - UUID of guest

### Modify User Password [PUT]

+ Attributes

    + password (string, optional) - new password, automated generate when omitted
    + user(number, optional) - username, automated choose username by system type when omitted

+ Request modify disk (application/json)

        {
          "password": "asdb12356",
          "user": "root"
        }

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data": {
            "password": "1235656756",
            "user": "root"
          }
        }
        ]

### Get User Password [GET]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data": {
            "password": "1235656756",
            "user": "root"
          }
        }
        ]

## Guest Name [/guests/{id}/name/]

+ Parameters

    + id (string) - UUID of guest

### Modify Guest Name [PUT]

modify guest name

+ Attributes

    + name (string) - display name

+ Request Modify Name (application/json)

        {
          "name": "some_instance",
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## CPU priority [/guests/{id}/qos/cpu/]

+ Parameters

    + id (string) - UUID of guest

### Modify CPU prioirty [PUT]

+ Attributes

    + priority (enum[string]) - CPU priority
      + Members
        + 'high'
        + 'medium'
        + 'low'

+ Request Modify CPU priority (application/json)

        {
          "priority": "high",
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Disk QoS [/guests/{id}/qos/disk/]

+ Parameters

    + id (string) - UUID of guest

### Modify Disk QoS [PUT]

+ Attributes

    + write_speed: 10485760 (number, optional) - disk write bps limit, 0 = unlimited
    + write_iops: 100 (number, optional) - disk write iops limit, 0 = unlimited
    + read_speed: 10485760 (number, optional) - disk read bps limit, 0 = unlimited
    + read_iops: 100 (number, optional) - disk read iops limit, 0 = unlimited

+ Request Modify Disk QoS (application/json)

        {
            "write_speed": 1048576,
            "write_iops": 100,
            "read_speed": 1048576,
            "read_iops": 100
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Network QoS [/guests/{id}/qos/network/]

+ Parameters

    + id (string) - UUID of guest

### Modify Network QoS [PUT]

+ Attributes

    + receive_speed: 10485760 (number, optional) - network receive bps, 0 = unlimited
    + send_speed: 10485760 (number, optional) - network send bps, 0 = unlimited

+ Request Modify Network QoS (application/json)

        {
            "receive_speed": 10485760,
            "send_speed": 10485760
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Instances [/instances/{id}]

VM running instance

+ Parameters
  + id (string) - UUID of instance/guest

### Get running status [GET]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data": {
            "name": "test01",
            "created": true,
            "running": true,
            "owner": "admin",
            "group": "manager",
            "pool": "pool1",
            "cell": "cell_93e3de24f0a4",
            "cores": 4,
            "memory": 5120,
            "total_disk": 4864000,
            "disks": [482345, 487534]
            "auto_start": true,
            "ethernet_address": "ed:35:3d:5a:4e:3f",
            "display_protocol": "vnc",
            "internal":{
              "network_address": "**********",
              "display_address": "**********:5901",
              "allocated_address": "**********"
            },
            "external":{
              "network_address": "**********",
              "display_address": "**********:5901"
            },
            "create_time": "2018-08-21 00:12:34",
            "media_attached": true,
            "media_source": "centos_7_x64_iso",
            "cpu_usage": 5.34,
            "memory_available": 1280,
            "disk_available": 4925700,
            "bytes_read": 3673,
            "bytes_written": 8634,
            "bytes_received": 2643,
            "bytes_sent": 7634
          }
        }
        ]

### Start Instance [POST]

start guest instance

+ Attributes

    + from_media (boolean, optional) - boot from media
    + from_network (boolean, optional) - boot from network source/PXE
    + source (string, optional) - boot source id/URI

+ Request start instance (application/json)

        {
          "from_media": true,
          "source": "bd0fe127-a8db-4c89-98ea-9a5070b08aae"
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Stop Instance [DELETE]

stop guest instance

+ Attributes

    + reboot (boolean) - shutdown or reboot
    + force(boolean) - force operate

+ Request stop instance (application/json)

        {
            "reboot": false,
            "force": true
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Instance Media [/instances/{id}/media]

### Insert Media [POST]

Insert media into a running instance

+ Attributes

    + source (string) - media id
    + type (number, optional) - media type, 0 = Media Image

+ Request insert Media (application/json)

        {
            "source": "bd0fe127-a8db-4c89-98ea-9a5070b08aae",
            "type": 0
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Eject Media [DELETE]

Eject media from a running instance

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]


## Guest Snapshots [/instances/{id}/snapshots/]

Manage snapshots of a guest

+ Parameters

    + id (string) - UUID of target guest

### Query snapshot tree [GET]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
          "origin":{
            "is_root": true
          },
          "snapshot1": {
            "backing": "origin"
          },
          "snapshot2": {
            "backing": "snapshot1"
          },
          "snapshot3": {
            "backing": "snapshot2",
            "is_current": true
          },
          "another_branch": {
            "backing": "origin"
          },
          "another_end": {
            "backing": "another_branch"
          }
          ]
        }
        ]

### Create new snapshot [POST]

creae a new snapshot

+ Attributes

    + name (string) - snapshot name
    + description (string, optional) - snapshot description

+ Request restore snapshot (application/json)

        {
            "name": "snapshot1",
            "description": "this is a snapshot example"
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Restore snapshot [PUT]

resume to a specified snapshot

+ Attributes

    + target (string) - snapshot name

+ Request restore snapshot (application/json)

        {
            "target": "snapshot1"
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Configure Snapshot [/instances/{id}/snapshots/{name}]

Operate on a specified snapshot

+ Parameters

    + id (string) - UUID of target guest
    + name (string) - snapshot

### Get snapshot info [GET]

Query info of a snapshot

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "running": false,
            "description": "this is snapshot before installing cloud-init",
            "create_time": "2018-08-21 12:34:56"
          }
        }
        ]

### Delete snapshot [DELETE]

Delete a snapshot

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Monitor Channel [/monitor_channels/{id}]

create channel for monitor and control instance. Create a new temporary channel id 'ABC' using POST Method, then connect and consumed channel with websocket address 'ws://host/monitor_channels/ABC' using noVNC or other clients. Unused channel will be released if no connection established in time.

### Create New Channel [POST]

Establish a new monitor channel

+ Attributes

    + guest (string) - target guest id

+ Request Create New Monitor Channel (application/json)

        {
          "guest": "some_guest_id",
        }

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
              "id": "channel-id-123",
              "protocol": "vnc",
              "username": "",
              "password": "some_secret",
            }
        }
        ]

# Group Image

## Media Image Search [/media_image_search/{?session}]

### Search Media Image [GET]

Search media images with login session

+ Parameters

    + session (string) - login session ID

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            {
              "name": "centos7_64_minimal",
              "id": "sdf83kjfe-23r4wedf",
              "description": "some desc",
              "create_time": "2018-08-21 00:12:34",
              "modify_time": "2018-08-21 00:13:34",
              "size": 4872334659735,
              "tags": ["linux", "64bit", "centos"]
            },
            {
              "name": "win7_home_64",
              "id": "sdf83kjfe-23r4wertytdf",
              "description": "win desktop",
              "create_time": "2018-08-21 00:12:34",
              "modify_time": "2018-08-21 00:13:34",
              "size": 4872334659774,
              "tags": ["windows", "64bit", "windows7"]
            }
          ]
        }
        ]


## Media Image [/media_images/{id}]

+ Parameters

    + id (string, optional) - image id

### Query Image list [GET /media_images/]

Query all media image

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            {
              "name": "centos7_64_minimal",
              "id": "sdf83kjfe-23r4wedf",
              "description": "some desc",
              "create_time": "2018-08-21 00:12:34",
              "modify_time": "2018-08-21 00:13:34",
              "size": 4872334659735,
              "tags": ["linux", "64bit", "centos"]
            },
            {
              "name": "win7_home_64",
              "id": "sdf83kjfe-23r4wertytdf",
              "description": "win desktop",
              "create_time": "2018-08-21 00:12:34",
              "modify_time": "2018-08-21 00:13:34",
              "size": 4872334659774,
              "tags": ["windows", "64bit", "windows7"]
            }
          ]
        }
        ]

### Get Media Image [GET]

Get info of media image

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "name": "centos7_64_minimal",
            "id": "sdf83kjfe-23r4wedf",
            "description": "some desc",
            "create_time": "2018-08-21 00:12:34",
            "modify_time": "2018-08-21 00:13:34",
            "size": 4872334659735,
            "tags": ["linux", "64bit", "centos"]
          }
        }
        ]

### Create Media Image [POST /media_images/]

Create a new media image

+ Attributes

    + name (string) - image name
    + owner (string) - image creater
    + group (string) - image group
    + description (string, optional) - image description
    + tags (array[string], optional) - image tags

+ Request Create Image (application/json)

        {
          "name": "centos7_64_minimal",
          "owner": "admin",
          "group": "manager",
          "description": "some desc",
          "tags": ["linux", "64bit", "centos"]
        }

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            {
              "id": "sdf83kjfe-23r4wedf",
            }
          ]
        }
        ]

### Modify Media Image [PUT]

Modify info of media image

+ Attributes

    + name (string, optional) - image name
    + owner (string, optional) - image creater
    + group (string, optional) - image group
    + description (string, optional) - image description
    + tags (array[string], optional) - image tags

+ Request Modify Media Image (application/json)

        {
          "name": "centos7_64_minimal",
          "owner": "admin",
          "group": "manager",
          "description": "some desc",
          "tags": ["linux", "64bit", "centos"]
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Detele Media Image [DELETE]

Delete a media image

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Media Image File [/media_images/{id}/file/]

+ Parameters

    + id (string) - image id

### Download image file [GET]

Download binary media image data

+ Attributes

    + id (string) - image id

+ Response 200 (application/octet-stream)

### Upload image file [POST]

Upload iso data as media image

+ Attributes

    + id (string) - image id

+ Request upload (multipart/form-data)

        -----BOUNDARY
        Content-Disposition: form-data; name="image"; filename="image.iso"

        -----BOUNDARY

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Disk Image Search [/disk_image_search/{?session,tags}]

### Search Disk Image [GET]

Search disk images with login session

+ Parameters

    + session (string) - login session ID
    + tags (array[string], optional) - image tags

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            {
              "name": "centos7_64_minimal",
              "id": "sdf83kjfe-23r4wedf",
              "description": "some desc",
              "create_time": "2018-08-21 00:12:34",
              "modify_time": "2018-08-21 00:13:34",
              "size": 4872334659735,
              "tags": ["linux", "64bit", "centos"]
            },
            {
              "name": "win7_home_64",
              "id": "sdf83kjfe-23r4wertytdf",
              "description": "win desktop",
              "create_time": "2018-08-21 00:12:34",
              "modify_time": "2018-08-21 00:13:34",
              "size": 4872334659774,
              "tags": ["windows", "64bit", "windows7"]
            }
          ]
        }
        ]

## Disk Image [/disk_images/{id}]

+ Parameters

  + id (string, optional) - disk image id

### Get Disk Image [GET]

Get info of disk image

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data": {
              "name": "centos7_64_minimal",
              "created": false,
              "progress": 46,
              "description": "some desc",
              "tags": ["linux", "64bit", "centos"]
            }
        }
        ]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data": {
              "name": "centos7_64_minimal",
              "created": true,
              "description": "some desc",
              "create_time": "2018-08-21 00:12:34",
              "modify_time": "2018-08-21 00:13:34",
              "size": 4872334659735,
              "tags": ["linux", "64bit", "centos"]
            }
        }
        ]

### Create Disk Image [POST /disk_images/]

Create a new disk image

+ Attributes

    + name (string) - image name
    + guest (string) - source guest id
    + owner (string) - image creater
    + group (string) - image group
    + description (string, optional) - image description
    + tags (array[string], optional) - image tags

+ Request Create Image (application/json)

        {
          "name": "centos7_64_minimal",
          "guest": "dir723rgfyu-rre67grg-efw",
          "owner": "admin",
          "group": "manager",
          "description": "some desc",
          "tags": ["linux", "64bit", "centos"]
        }

+ Response 202 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            {
              "id": "sdf83kjfe-23r4wedf",
            }
          ]
        }
        ]

### Modify Disk Image [PUT]

Modify info of disk image

+ Attributes

    + name (string, optional) - image name
    + owner (string, optional) - image creater
    + group (string, optional) - image group
    + description (string, optional) - image description
    + tags (array[string], optional) - image tags

+ Request Modify Disk Image (application/json)

        {
          "name": "centos7_64_minimal",
          "owner": "admin",
          "group": "manager",
          "description": "some desc",
          "tags": ["linux", "64bit", "centos"]
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Delete Disk Image [DELETE]

delete a disk image

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Disk Image File [/disk_images/{id}/file/]

+ Parameters

    + id (string) - image id

### Upload image file [POST]

Upload a qcow2 file as disk image

+ Attributes

    + id (string) - image id

+ Request upload (multipart/form-data)

        -----BOUNDARY
        Content-Disposition: form-data; name="image"; filename="centos7.qcow2"

        -----BOUNDARY
        Content-Disposition: form-data; name="checksum"

        0123456789abcdef
        -----BOUNDARY

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]


# Group System Maintain

## Migration [/migrations/{id}]

+ Parameters

  + id (string, optional) - migration task id

### Start Migration [POST]

Create a new migration task

+ Attributes

    + source_pool (string) - pool of instance host
    + source_cell (string) - cell of instance host
    + target_pool (string, optional) - target pool you want to migrate
    + target_cell (string, optional) - target cell you want to migrate
    + instances (array[string], optional) - UUID list of instances need to migrate

+ Request Start Migration (application/json)

        {
          "source_pool": "default",
          "source_cell": "cell1",
          "target_pool": "another-pool",
          "target_cell": "new-cell",
          "instances": ["jhfguf85-34uyf-4t4tghjh", "dfjui-euy37-dyuy3t5qn"]
        }

+ Response 202 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            {
              "id": "abcdef-1234567890"
            }
          ]
        }
        ]

### Query Migration [GET]

Query status of migration task

+ Response 201 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "finished": false,
            "progress": 57 //limit to 100
          }
        }
        ]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "finished": true
          }
        }
        ]

### List All Migration in progress [GET /migrations/]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            {
                "id": "1234567890-abcdef",
                "finished": false,
                "progress": 57 //limit to 100
            },
            {
                "id": "1234567890-abcdef",
                "finished": true
            }
          ]
        }
        ]

## Batch Creating Guest [/batch/create_guest/{id}]

+ Parameters
  + id (string, optional) - id of batch task

### Start Creation [POST]

Create a bulk of new guests, return a task ID for query status

+ Attributes

    + name_rule: 'order' (enum[string]) - rule of generating guest name
      + Members
        + 'order' - incremental digital by order, etc 'xxx_0', 'xxx_1'
        + 'MAC' - by guest MAC address
        + 'address' - by assigned guest address
    + name_prefix (string, optional) - guest name prefix
    + owner (string) - user id
    + group (string) - user group
    + pool (string) - compute pool name
    + count (number) - instance count
    + cores (number) - vm cpu cores
    + memory (number) - vm memory
    + disks (array[number]) - [system_disk_size, data_disk_n_size...]
    + auto_start (boolean) - auto start instance
    + from_image (string, optional) - initial disk image id
    + system_version (string, optional) - os version, 'centos7', 'win2012'
    + modules (array[string], optional) - installed guest module, "qemu"/"cloud-init"
    + cloud_init (CloudInit, optional) - cloud-init params
    + qos (QoS, optional) - QoS config

+ Request Start Batch Creating (application/json)

        {
          "name_rule": "order",
          "name_prefix": "test",
          "owner": "admin",
          "group": "manager",
          "pool": "pool_1",
          "count": 3,
          "cores": 8,
          "memory": 4048,
          "disks": [4048, 38443],
          "auto_start": true,
          "system": "linux",
          "system_version": "centos7",
          "modules": ["qemu", "cloud-init"],
          "cloud_init":{
            "root_enabled": true,
            "admin_name": "nano",
            "admin_secret": "12345678",
            "data_path": "/opt/data"
          }
        }

+ Response 202 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "id": "dsfds8979847r3dsf-3r67",
          }
        }
        ]

### Query Batch Creating Status [GET]

Query task status of batch creating

+ Response 202 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            {
              "name": "test_1",
              "id": "0123456789-abcdef-1",
              "progress": 45,
              "status": "creating"
            },
            {
              "name": "test_2",
              "id": "0123456789-abcdef-2",
              "status": "fail"
              "error": "not enough space"
            },
            {
              "name": "test_3",
              "id": "0123456789-abcdef-3",
              "status": "created"
            }
          ]
        }
        ]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            {
              "name": "test_1",
              "id": "0123456789-abcdef-1",
              "status": "created"
            },
            {
              "name": "test_2",
              "id": "0123456789-abcdef-2",
              "status": "fail",
              "error": "not enough space"
            },
            {
              "name": "test_3",
              "id": "0123456789-abcdef-3",
              "status": "created"
            }
          ]
        }
        ]

## Batch Deleting Guest [/batch/delete_guest/{id}]

+ Parameters
  + id (string, optional) - id of batch task

### Start Batch Deleting [POST]

Delete a bulk of guests, return a task ID for query status

+ Attributes

    + guest (array[string]) - list of target guest UUID
    + force (boolean, optional) - stop running instance for deleting

+ Request Start Batch Deleting (application/json)

        {
          "guest": [
            "0123456789-abcdef-1",
            "0123456789-abcdef-2",
            "0123456789-abcdef-3"
          ],
          "force": false
        }

+ Response 202 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "id": "dsfds8979847r3dsf-3r67",
          }
        }
        ]

### Query Batch Deleting Status [GET]

Query task status of batch deleting

+ Response 202 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            {
              "id": "0123456789-abcdef-1",
              "status": "deleted"
            },
            {
              "id": "0123456789-abcdef-2",
              "status": "fail",
              "error": "invalid guest"
            },
            {
              "id": "0123456789-abcdef-3",
              "status": "deleting"
            }
          ]
        }
        ]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
          {
            "id": "0123456789-abcdef-1",
            "status": "deleted"
          },
          {
            "id": "0123456789-abcdef-2",
            "status": "fail",
            "error": "invalid guest"
          },
          {
            "id": "0123456789-abcdef-3",
            "status": "deleted"
          }
          ]
        }
        ]


## Batch Stopping Guest [/batch/stop_guest/{id}]

+ Parameters
  + id (string, optional) - id of batch task

### Start Batch Stopping [POST]

Stop a bulk of guests, return a task ID for query status

+ Attributes

    + guests (array[string]) - list of target guest UUID
    + force: false (boolean, optional) - force stop

+ Request Start Batch Deleting (application/json)

        {
          "guests": [
            "0123456789-abcdef-1",
            "0123456789-abcdef-2",
            "0123456789-abcdef-3"
          ]
        }

+ Response 202 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "id": "dsfds8979847r3dsf-3r67",
          }
        }
        ]

### Query Batch Stopping Status [GET]

Query task status of batch stopping

+ Response 202 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            {
              "id": "0123456789-abcdef-1",
              "status": "stopped"
            },
            {
              "id": "0123456789-abcdef-2",
              "status": "fail",
              "error": "invalid guest"
            },
            {
              "id": "0123456789-abcdef-3",
              "status": "stopping"
            }
          ]
        }
        ]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
          {
            "id": "0123456789-abcdef-1",
            "status": "stopped"
          },
          {
            "id": "0123456789-abcdef-2",
            "status": "fail",
            "error": "invalid guest"
          },
          {
            "id": "0123456789-abcdef-3",
            "status": "stopped"
          }
          ]
        }
        ]


# Group FrontEnd Management

## Role [/roles/{role_name}]

+ Parameters
  + role_name (string, optional) - name of user role

### List Roles [GET /roles/]

list all use roles

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            "maintainer",
            "auditor",
            "user"
          ]
        }
        ]

### Get Role [GET]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "menu": ["dashboard", "compute_pool", "address_pool", "instance"],
            "resource": []
          }
        }
        ]

### Add Role [POST]

add new to system

+ Attributes

    + menu (array[string]) - menu list role can access

+ Request Add Role (application/json)

        {
          "menu": ["dashboard", "compute_pool", "address_pool", "instance"]
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Modify Role [PUT]

modify current authentication

+ Attributes

    + menu (array[string]) - menu list role can access

+ Request Modify Role (application/json)

        {
          "menu": ["dashboard", "compute_pool", "address_pool", "instance"]
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Remove Role [DELETE]

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## User Group [/user_groups/{group_name}]

+ Parameters
  + group_name (string, optional) - name of user group

### List Groups [GET /user_groups/]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            {
              "name": "manager_group",
              "display": "Group of Manager",
              "member": 3
            },
            {
              "name": "auditor_group",
              "display": "Group of Auditor",
              "member": 1
            },
            {
              "name": "user_group",
              "display": "Group of User",
              "member": 10
            }
          ]
        }
        ]

### Get Group [GET]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "name": "manager_group",
            "display": "Group of Manager",
            "role": ["manager", "user"],
            "member": [
              "admin",
              "nano",
              "akumas"
            ]
          }
        }
        ]

### Add Group [POST]

+ Attributes

    + display (string) - display name of group
    + role (array[string], optional) - list of role name

+ Request Add Group (application/json)

        {
          "display": "Group of Manager",
          "role": ["manager", "user"]
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Modify Group [PUT]

+ Attributes

    + display (string, optional) - display name of group
    + role (array[string], optional) - list of role name

+ Request Modify Group (application/json)

        {
          "display": "Group of Manager",
          "role": ["manager", "user"]
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Remove Group [DELETE]

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Group Member [/user_groups/{group_name}/members/{user_name}]

+ Parameters
  + group_name (string) - name of user group
  + user_name (string, optional) - user name

### List Member [GET /user_groups/{group_name}/members/]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            "admin",
            "nano",
            "akumas"
          ]
        }
        ]

### Add Member [POST]

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Remove Member [DELETE]

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## User [/users/{user_name}]

+ Parameters
  + user_name (string, optional) - user name

### List Users [GET /users/]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            "admin",
            "nano",
            "akumas"
          ]
        }
        ]

### Get User Info [GET]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "nick": "AK",
            "mail": "<EMAIL>"
          }
        }
        ]

### Create New User [POST]

+ Attributes

    + nick (string, optional) - nick name for display
    + mail (string, optional) - user email
    + password (string) - user password

+ Request Create New user (application/json)

        {
          "nick": "AK",
          "mail": "<EMAIL>",
          "password": "abcdefg"
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Modify User [PUT]

+ Attributes

    + nick (string, optional) - nick name for display
    + mail (string, optional) - user email

+ Request Modify user (application/json)

        {
          "nick": "AK",
          "mail": "<EMAIL>"
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Delete User [DELETE]

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## User Password [/users/{user_name}/password/]

+ Parameters
  + user_name (string, optional) - user name

### Modify Password [PUT]

+ Attributes

    + old (string) - old password for verify
    + new (string) - new password

+ Request Modify Password (application/json)

        {
          "old": "123456",
          "new": "abcdef"
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## User Search [/user_search/{?group}]

### Search User [GET]

+ Parameters

  + group (string, optional) - user group

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            "nano",
            "alex",
            "sam"
            ]
        }
        ]

## Sessions [/sessions/{session_id}]

Manage authenticated session

+ Parameters
  + session_id (string, optional) - session ID

### List session [GET /sessions/]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":[
            "bsu6384js3=",
            "bsu633jtq84",
            "bsu638skjfu"
          ]
        }
        ]

### Auth Session [POST /sessions/]

+ Attributes

    + user (string) - user name
    + password (string) - new password
    + nonce (string) - nonce for crypt

+ Request Auth New Session (application/json)

        {
          "user": "alex",
          "password": "abcdef",
          "nonce": "1234567890"
        }

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "session": "bsu6384js3=",
            "timeout": 120
          }
        }
        ]

### Update Session [PUT]

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Obtain Session Authentication [GET]

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "menu": ["dashboard", "compute_pool", "address_pool", "instance"],
            "resource": [],
            "user": "nano",
            "group": "admin",
            "address": "*************"

          }
        }
        ]

## Resource Visibility [/resource_visibilities/{session}]

Manage resource visibility with login session

+ Parameters

    + session (string) - session ID

### Get Current Visibility [GET]

Get current visibility configure with the group of current user

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "instance_visible": true,
            "media_image_visible": true,
            "disk_image_visible": false
          }
        }
        ]

### Set Current Visibility [PUT]

Modify current visibility configure with the group of current user

+ Attributes (Visibility)

+ Request Modify Visibility (application/json)

        {
            "instance_visible": true,
            "media_image_visible": true,
            "disk_image_visible": false
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

## Logs [/logs/{?limit,start,after,before}]

### Query Log Entry [GET]

+ Parameters

  + limit (number) - max entries returned
  + start (number, optional) - start offset
  + after (string, optional) - query logs after this date
  + before (string, optional) - query logs before this date

+ Response 200 (application/json)

        [
        {
          "error_code": 0,
          "message": "",
          "data":{
            "logs": [
              {
                "id": "201812011457390001",
                "time": "2018-12-01 14:57:39",
                "content": "admin create new user example"
              },
              {
                "id": "201812031427190001",
                "time": "2018-12-03 14:27:19",
                "content": "nano.sabrina create new guest nano.test1"
              }
            ],
            "total": 100
          }
        }
        ]

### Add Log Entry [POST]

+ Attributes

    + format (string, optional) - format of content
    + content (string) - log content

+ Request Add Log (application/json)

        {
          "format": "string",
          "content": "here is a new log for somebody creating a new instance"
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]

### Delete Log Entry [DELETE]

+ Attributes

    + entries (array[string]) - list of log entry

+ Request Delete Log (application/json)

        {
          "entries": [
            "201812011457390001",
            "201812031427190001"
          ]
        }

+ Response 200 (application/json)

        [
        {
            "error_code": 0,
            "message": "",
            "data":""
        }
        ]
