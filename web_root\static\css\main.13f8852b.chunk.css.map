{"version": 3, "sources": ["material-dashboard-react.css"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;EAcE,CACF,SACE,yBAAgC,CAChC,gBAAiB,CACjB,oBACF,CAEA,+FAIE,yBACF,CAEA,+BAIE,sBAAuB,CAIvB,0BAA2B,CAC3B,eAAgB,CAChB,iBACF,CAEA,UACE,wBACF,CAEA,yEAGE,aAAc,CAKd,YACF,CAEA,UACE,mBAAwB,CACxB,aACF,CACA,OACE,kCAAmC,CACnC,iCACF,CACA,KAGE,QAAS,CACT,6CAAiD,CACjD,eAAgB,CAChB,iBACF,CAEA,iDAEE,qBACF,CAEA,MACE,aACF,CAEA,GACE,aAAc,CACd,kBACF,CAEA,GACE,eACF,CAEA,GACE,iBAAkB,CAElB,kBACF,CAEA,MAJE,iBAOF,CAHA,GACE,eAEF,CAEA,GACE,gBAAiB,CACjB,iBAAkB,CAClB,kBACF,CAEA,GACE,aAAc,CACd,wBAAyB,CACzB,eACF,CAEA,KACE,qBAAyB,CACzB,aACF,CAEA,aACE,iBACF,CAEA,uBAOE,mDAAuD,CACvD,eAAgB,CAChB,iBACF,CAEA,EACE,aAEF,CAEA,kBAHE,oBAOF,CAJA,gBAEE,aAEF,CAEA,OACE,eACF,CAEA,EACE,+CAAmD,CACnD,uCACF,CAEA,OACE,SACF,CAEA,+QAWE,mBACF,CAEA,OACE,kBAAmB,CACnB,cACF,CAEA,OACE,eAGF,CAEA,aAJE,cAAe,CACf,mBAQF,CALA,MAGE,UAAc,CACd,eACF,CAEA,OACE,cACF,CAEA,UACE,eAAgB,CAChB,SAAU,CACV,eACF,CAEA,aACE,oBACF,CAEA,eACE,aAAc,CACd,YAAa,CACb,eAAgB,CAChB,cAAe,CACf,wBAAyB,CACzB,iBAAkB,CAElB,iBAAkB,CAClB,aACF,CAEA,oCALE,oBAOF,CAEA,yBACE,UAEE,iBAAkB,CAClB,iBACF,CAEA,WACE,WAAY,CACZ,UAAW,CACX,cAAe,CACf,SAAU,CACV,KAAM,CACN,SAAU,CACV,WAAY,CACZ,UAAW,CACX,YAAa,CACb,iBACF,CACF,CACA,cACE,mDAAuD,CACvD,eAAgB,CAChB,iBAAkB,CAClB,cAAe,CACf,SAAU,CACV,OAAQ,CACR,UAAW,CACX,yBAA8B,CAC9B,YAAa,CACb,yBAA0B,CAC1B,iBAAkB,CAClB,SAAU,CACV,uCACF,CAEA,gEAEE,oBACF,CAEA,wCAEE,mBAAqB,CACrB,2BAA6B,CAC7B,wBAA0B,CAC1B,oBACF,CAEA,sBACE,UAAc,CACd,YAAa,CACb,yBAA0B,CAC1B,UACF,CAEA,6BACE,UAAW,CACX,SAAU,CACV,WAAY,CACZ,sBAAwB,CACxB,cAAe,CACf,iBAAkB,CAClB,qBAA0B,CAC1B,oBAAqB,CACrB,sCAA2C,CAC3C,eAAgB,CAChB,iBACF,CAEA,8BACE,UACF,CAEA,sCACE,UACF,CAEA,iIAGE,UAAc,CACd,iBACF,CAEA,kBACE,eAAgB,CAChB,UAAW,CACX,YAAa,CACb,aACF,CAEA,gFAEE,eACF,CACA,qBACE,qBAAyB,CACzB,iBAAkB,CAClB,cAAe,CACf,oBAAqB,CACrB,WAAY,CACZ,gBAAiB,CACjB,iBAAkB,CAClB,UAAW,CACX,mCACF,CAEA,uDAEE,iBACF,CAEA,4BACE,wBACF,CAEA,0BACE,wBACF,CAEA,2BACE,wBACF,CAEA,4BACE,wBACF,CAEA,yBACE,wBACF,CAEA,iBACE,cAAe,CACf,WACF,CACA,gCACE,aAAc,CACd,aAAgB,CAChB,SAAU,CACV,UACF,CAEA,kGAGE,UAAW,CACX,WAAY,CACZ,kBAAmB,CACnB,SAAY,CACZ,iBACF,CAEA,oCACE,QACF,CAEA,8GAGE,iBACF,CACA,8BACE,WAAY,CACZ,gBAAiB,CACjB,cAAe,CACf,eAAgB,CAChB,iBAAkB,CAClB,wBACF,CAEA,kCACE,UAAW,CACX,oBAAqB,CACrB,eAAgB,CAChB,aAAc,CACd,aACF,CAEA,kCACE,iBACF,CAEA,gDACE,iBAAkB,CAClB,QACF,CAEA,oFAEE,iBACF,CACA,qEACE,eAAgB,CAChB,cAAe,CACf,4BAA6B,CAC7B,eAAgB,CAChB,QACF,CAEA,6CACE,cAAe,CACf,iBAAkB,CAClB,kBAAmB,CACnB,qBAAsB,CACtB,qBAAsB,CAGtB,SAAU,CACV,cAAe,CACf,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAChB,SACF,CAEA,iDACE,eACF,CACA,sGAEE,gCACF,CAEA,kDAEE,iBAAqB,CACrB,qBACF,CACA,uCAKE,0BAA2B,CAC3B,QAAS,CACT,SAAU,CACV,oBAAqB,CACrB,YACF,CAEA,8CACE,oCAAsC,CACtC,qCAA2C,CAC3C,iCAAmC,CACnC,WAAY,CACZ,QACF,CACA,6CACE,oCAAsC,CACtC,2BAAgC,CAChC,iCAAmC,CACnC,WACF,CAEA,2FAEE,UAAW,CACX,oBAAqB,CACrB,iBAAkB,CAClB,QAAS,CACT,UAAW,CACX,0BAA2B,CAC3B,kCAAmC,CACnC,+BACF,CAEA,4CACE,aAAc,CACd,kBAAmB,CACnB,SAAU,CAKV,0BAA2B,CAC3B,oBACF,CACA,+BACE,UAAW,CACX,MAAS,CACT,yBACF,CACA,8CACE,UAAW,CACX,SACF,CACA,EACE,+BACF", "file": "main.13f8852b.chunk.css", "sourcesContent": ["/*!\n\n =========================================================\n * Material Dashboard React - v1.8.0 based on Material Dashboard - v1.2.0\n =========================================================\n\n * Product Page: http://www.creative-tim.com/product/material-dashboard-react\n * Copyright 2019 Creative Tim (http://www.creative-tim.com)\n * Licensed under MIT (https://github.com/creativetimofficial/material-dashboard-react/blob/master/LICENSE.md)\n\n =========================================================\n\n * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\n */\n.ct-grid {\n  stroke: rgba(255, 255, 255, 0.2);\n  stroke-width: 1px;\n  stroke-dasharray: 2px;\n}\n\n.ct-series-a .ct-point,\n.ct-series-a .ct-line,\n.ct-series-a .ct-bar,\n.ct-series-a .ct-slice-donut {\n  stroke: rgba(255, 255, 255, 0.8);\n}\n\n.ct-label.ct-horizontal.ct-end {\n  -webkit-box-align: flex-start;\n  -webkit-align-items: flex-start;\n  -ms-flex-align: flex-start;\n  align-items: flex-start;\n  -webkit-box-pack: flex-start;\n  -webkit-justify-content: flex-start;\n  -ms-flex-pack: flex-start;\n  justify-content: flex-start;\n  text-align: left;\n  text-anchor: start;\n}\n\n.ct-label {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.ct-chart-line .ct-label,\n.ct-chart-bar .ct-label,\n.ct-chart-pie .ct-label {\n  display: block;\n  display: -webkit-box;\n  display: -moz-box;\n  display: -ms-flexbox;\n  display: -webkit-flex;\n  display: flex;\n}\n\n.ct-label {\n  fill: rgba(0, 0, 0, 0.4);\n  line-height: 1;\n}\nhtml * {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\nbody {\n  background-color: #eeeeee;\n  color: #3c4858;\n  margin: 0;\n  font-family: Roboto, Helvetica, Arial, sans-serif;\n  font-weight: 300;\n  line-height: 1.5em;\n}\n\nblockquote footer:before,\nblockquote small:before {\n  content: \"\\2014 \\00A0\";\n}\n\nsmall {\n  font-size: 80%;\n}\n\nh1 {\n  font-size: 3em;\n  line-height: 1.15em;\n}\n\nh2 {\n  font-size: 2.4em;\n}\n\nh3 {\n  font-size: 1.825em;\n  line-height: 1.4em;\n  margin: 20px 0 10px;\n}\n\nh4 {\n  font-size: 1.3em;\n  line-height: 1.4em;\n}\n\nh5 {\n  font-size: 1.25em;\n  line-height: 1.4em;\n  margin-bottom: 15px;\n}\n\nh6 {\n  font-size: 1em;\n  text-transform: uppercase;\n  font-weight: 500;\n}\n\nbody {\n  background-color: #eeeeee;\n  color: #3c4858;\n}\n\nblockquote p {\n  font-style: italic;\n}\n\nbody,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-family: \"Roboto\", \"Helvetica\", \"Arial\", sans-serif;\n  font-weight: 300;\n  line-height: 1.5em;\n}\n\na {\n  color: #9c27b0;\n  text-decoration: none;\n}\n\na:hover,\na:focus {\n  color: #89229b;\n  text-decoration: none;\n}\n\nlegend {\n  border-bottom: 0;\n}\n\n* {\n  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);\n  -webkit-tap-highlight-color: transparent;\n}\n\n*:focus {\n  outline: 0;\n}\n\na:focus,\na:active,\nbutton:active,\nbutton:focus,\nbutton:hover,\nbutton::-moz-focus-inner,\ninput[type=\"reset\"]::-moz-focus-inner,\ninput[type=\"button\"]::-moz-focus-inner,\ninput[type=\"submit\"]::-moz-focus-inner,\nselect::-moz-focus-inner,\ninput[type=\"file\"] > input[type=\"button\"]::-moz-focus-inner {\n  outline: 0 !important;\n}\n\nlegend {\n  margin-bottom: 20px;\n  font-size: 21px;\n}\n\noutput {\n  padding-top: 8px;\n  font-size: 14px;\n  line-height: 1.42857;\n}\n\nlabel {\n  font-size: 14px;\n  line-height: 1.42857;\n  color: #aaaaaa;\n  font-weight: 400;\n}\n\nfooter {\n  padding: 15px 0;\n}\n\nfooter ul {\n  margin-bottom: 0;\n  padding: 0;\n  list-style: none;\n}\n\nfooter ul li {\n  display: inline-block;\n}\n\nfooter ul li a {\n  color: inherit;\n  padding: 15px;\n  font-weight: 500;\n  font-size: 12px;\n  text-transform: uppercase;\n  border-radius: 3px;\n  text-decoration: none;\n  position: relative;\n  display: block;\n}\n\nfooter ul li a:hover {\n  text-decoration: none;\n}\n\n@media (max-width: 991px) {\n  body,\n  html {\n    position: relative;\n    overflow-x: hidden;\n  }\n\n  #bodyClick {\n    height: 100%;\n    width: 100%;\n    position: fixed;\n    opacity: 0;\n    top: 0;\n    left: auto;\n    right: 260px;\n    content: \"\";\n    z-index: 9999;\n    overflow-x: hidden;\n  }\n}\n.fixed-plugin {\n  font-family: \"Roboto\", \"Helvetica\", \"Arial\", sans-serif;\n  font-weight: 300;\n  line-height: 1.5em;\n  position: fixed;\n  top: 180px;\n  right: 0;\n  width: 64px;\n  background: rgba(0, 0, 0, 0.3);\n  z-index: 1031;\n  border-radius: 8px 0 0 8px;\n  text-align: center;\n  top: 120px;\n  .badge-primary-background-color: #9c27b0;\n}\n\n.fixed-plugin .SocialMediaShareButton,\n.fixed-plugin .github-btn {\n  display: inline-block;\n}\n\n.fixed-plugin li > a,\n.fixed-plugin .badge {\n  transition: all 0.34s;\n  -webkit-transition: all 0.34s;\n  -moz-transition: all 0.34s;\n  text-decoration: none;\n}\n\n.fixed-plugin .fa-cog {\n  color: #ffffff;\n  padding: 10px;\n  border-radius: 0 0 6px 6px;\n  width: auto;\n}\n\n.fixed-plugin .dropdown-menu {\n  right: 80px;\n  left: auto;\n  width: 290px;\n  border-radius: 0.1875rem;\n  padding: 0 10px;\n  position: absolute;\n  color: rgba(0, 0, 0, 0.87);\n  display: inline-block;\n  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.14);\n  background: #fff;\n  border-radius: 3px;\n}\n\n.fixed-plugin .fa-circle-thin {\n  color: #ffffff;\n}\n\n.fixed-plugin .active .fa-circle-thin {\n  color: #00bbff;\n}\n\n.fixed-plugin .dropdown-menu > .active > a,\n.fixed-plugin .dropdown-menu > .active > a:hover,\n.fixed-plugin .dropdown-menu > .active > a:focus {\n  color: #777777;\n  text-align: center;\n}\n\n.fixed-plugin img {\n  border-radius: 0;\n  width: 100%;\n  height: 100px;\n  margin: 0 auto;\n}\n\n.fixed-plugin .dropdown-menu li > a:hover,\n.fixed-plugin .dropdown-menu li > a:focus {\n  box-shadow: none;\n}\n.fixed-plugin .badge {\n  border: 3px solid #ffffff;\n  border-radius: 50%;\n  cursor: pointer;\n  display: inline-block;\n  height: 23px;\n  margin-right: 5px;\n  position: relative;\n  width: 23px;\n  background-color: rgba(30, 30, 30, 0.97);\n}\n\n.fixed-plugin .badge.active,\n.fixed-plugin .badge:hover {\n  border-color: #00bbff;\n}\n\n.fixed-plugin .badge-purple {\n  background-color: #9c27b0;\n}\n\n.fixed-plugin .badge-blue {\n  background-color: #00bcd4;\n}\n\n.fixed-plugin .badge-green {\n  background-color: #4caf50;\n}\n\n.fixed-plugin .badge-orange {\n  background-color: #ff9800;\n}\n\n.fixed-plugin .badge-red {\n  background-color: #f44336;\n}\n\n.fixed-plugin h5 {\n  font-size: 14px;\n  margin: 10px;\n}\n.fixed-plugin .dropdown-menu li {\n  display: block;\n  padding: 4px 0px;\n  width: 25%;\n  float: left;\n}\n\n.fixed-plugin li.adjustments-line,\n.fixed-plugin li.header-title,\n.fixed-plugin li.button-container {\n  width: 100%;\n  height: 50px;\n  min-height: inherit;\n  padding: 0px;\n  text-align: center;\n}\n\n.fixed-plugin li.adjustments-line p {\n  margin: 0;\n}\n\n.fixed-plugin li.adjustments-line div,\n.fixed-plugin li.header-title div,\n.fixed-plugin li.button-container div {\n  margin-bottom: 5px;\n}\n.fixed-plugin li.header-title {\n  height: 30px;\n  line-height: 25px;\n  font-size: 12px;\n  font-weight: 600;\n  text-align: center;\n  text-transform: uppercase;\n}\n\n.fixed-plugin .adjustments-line p {\n  float: left;\n  display: inline-block;\n  margin-bottom: 0;\n  font-size: 1em;\n  color: #3c4858;\n}\n\n.fixed-plugin .adjustments-line a {\n  color: transparent;\n}\n\n.fixed-plugin .adjustments-line a .badge-colors {\n  position: relative;\n  top: -2px;\n}\n\n.fixed-plugin .adjustments-line a a:hover,\n.fixed-plugin .adjustments-line a a:focus {\n  color: transparent;\n}\n.fixed-plugin .adjustments-line .dropdown-menu > li.adjustments-line > a {\n  padding-right: 0;\n  padding-left: 0;\n  border-bottom: 1px solid #ddd;\n  border-radius: 0;\n  margin: 0;\n}\n\n.fixed-plugin .dropdown-menu > li > a.img-holder {\n  font-size: 16px;\n  text-align: center;\n  border-radius: 10px;\n  background-color: #fff;\n  border: 3px solid #fff;\n  padding-left: 0;\n  padding-right: 0;\n  opacity: 1;\n  cursor: pointer;\n  display: block;\n  max-height: 100px;\n  overflow: hidden;\n  padding: 0;\n}\n\n.fixed-plugin .dropdown-menu > li > a.img-holder img {\n  margin-top: auto;\n}\n.fixed-plugin .dropdown-menu > li:hover > a.img-holder,\n.fixed-plugin .dropdown-menu > li:focus > a.img-holder {\n  border-color: rgba(0, 187, 255, 0.53);\n}\n\n.fixed-plugin .dropdown-menu > .active > a.img-holder,\n.fixed-plugin .dropdown-menu > .active > a.img-holder {\n  border-color: #00bbff;\n  background-color: #ffffff;\n}\n.fixed-plugin .dropdown .dropdown-menu {\n  -webkit-transform: translateY(-15%);\n  -moz-transform: translateY(-15%);\n  -o-transform: translateY(-15%);\n  -ms-transform: translateY(-15%);\n  transform: translateY(-15%);\n  top: 27px;\n  opacity: 0;\n  transform-origin: 0 0;\n  display: none;\n}\n\n.fixed-plugin .dropdown .dropdown-menu:before {\n  border-bottom: 0.4em solid transparent;\n  border-left: 0.4em solid rgba(0, 0, 0, 0.2);\n  border-top: 0.4em solid transparent;\n  right: -16px;\n  top: 46px;\n}\n.fixed-plugin .dropdown .dropdown-menu:after {\n  border-bottom: 0.4em solid transparent;\n  border-left: 0.4em solid #ffffff;\n  border-top: 0.4em solid transparent;\n  right: -16px;\n}\n\n.fixed-plugin .dropdown .dropdown-menu:before,\n.fixed-plugin .dropdown .dropdown-menu:after {\n  content: \"\";\n  display: inline-block;\n  position: absolute;\n  top: 46px;\n  width: 16px;\n  transform: translateY(-50%);\n  -webkit-transform: translateY(-50%);\n  -moz-transform: translateY(-50%);\n}\n\n.fixed-plugin .dropdown.show .dropdown-menu {\n  display: block;\n  visibility: visible;\n  opacity: 1;\n  -webkit-transform: translateY(-13%);\n  -moz-transform: translateY(-13%);\n  -o-transform: translateY(-13%);\n  -ms-transform: translateY(-13%);\n  transform: translateY(-13%);\n  transform-origin: 0 0;\n}\n.fixed-plugin.rtl-fixed-plugin {\n  right: auto;\n  left: 0px;\n  border-radius: 0 8px 8px 0;\n}\n.fixed-plugin.rtl-fixed-plugin .dropdown-menu {\n  right: auto;\n  left: 80px;\n}\n* {\n  letter-spacing: normal !important;\n}\n"]}