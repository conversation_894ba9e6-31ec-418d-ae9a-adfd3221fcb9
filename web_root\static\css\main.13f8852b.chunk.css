/*!

 =========================================================
 * Material Dashboard React - v1.8.0 based on Material Dashboard - v1.2.0
 =========================================================

 * Product Page: http://www.creative-tim.com/product/material-dashboard-react
 * Copyright 2019 Creative Tim (http://www.creative-tim.com)
 * Licensed under MIT (https://github.com/creativetimofficial/material-dashboard-react/blob/master/LICENSE.md)

 =========================================================

 * The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

 */.ct-grid{stroke:hsla(0,0%,100%,.2);stroke-width:1px;stroke-dasharray:2px}.ct-series-a .ct-bar,.ct-series-a .ct-line,.ct-series-a .ct-point,.ct-series-a .ct-slice-donut{stroke:hsla(0,0%,100%,.8)}.ct-label.ct-horizontal.ct-end{align-items:flex-start;justify-content:flex-start;text-align:left;text-anchor:start}.ct-label{color:hsla(0,0%,100%,.7)}.ct-chart-bar .ct-label,.ct-chart-line .ct-label,.ct-chart-pie .ct-label{display:block;display:flex}.ct-label{fill:rgba(0,0,0,.4);line-height:1}html *{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}body{margin:0;font-family:Roboto,Helvetica,Arial,sans-serif;font-weight:300;line-height:1.5em}blockquote footer:before,blockquote small:before{content:"\2014 \00A0"}small{font-size:80%}h1{font-size:3em;line-height:1.15em}h2{font-size:2.4em}h3{font-size:1.825em;margin:20px 0 10px}h3,h4{line-height:1.4em}h4{font-size:1.3em}h5{font-size:1.25em;line-height:1.4em;margin-bottom:15px}h6{font-size:1em;text-transform:uppercase;font-weight:500}body{background-color:#eee;color:#3c4858}blockquote p{font-style:italic}body,h1,h2,h3,h4,h5,h6{font-family:"Roboto","Helvetica","Arial",sans-serif;font-weight:300;line-height:1.5em}a{color:#9c27b0}a,a:focus,a:hover{text-decoration:none}a:focus,a:hover{color:#89229b}legend{border-bottom:0}*{-webkit-tap-highlight-color:rgba(255,255,255,0);-webkit-tap-highlight-color:transparent}:focus{outline:0}a:active,a:focus,button::-moz-focus-inner,button:active,button:focus,button:hover,input[type=button]::-moz-focus-inner,input[type=file]>input[type=button]::-moz-focus-inner,input[type=reset]::-moz-focus-inner,input[type=submit]::-moz-focus-inner,select::-moz-focus-inner{outline:0!important}legend{margin-bottom:20px;font-size:21px}output{padding-top:8px}label,output{font-size:14px;line-height:1.42857}label{color:#aaa;font-weight:400}footer{padding:15px 0}footer ul{margin-bottom:0;padding:0;list-style:none}footer ul li{display:inline-block}footer ul li a{color:inherit;padding:15px;font-weight:500;font-size:12px;text-transform:uppercase;border-radius:3px;position:relative;display:block}footer ul li a,footer ul li a:hover{text-decoration:none}@media (max-width:991px){body,html{position:relative;overflow-x:hidden}#bodyClick{height:100%;width:100%;position:fixed;opacity:0;top:0;left:auto;right:260px;content:"";z-index:9999;overflow-x:hidden}}.fixed-plugin{font-family:"Roboto","Helvetica","Arial",sans-serif;font-weight:300;line-height:1.5em;position:fixed;top:180px;right:0;width:64px;background:rgba(0,0,0,.3);z-index:1031;border-radius:8px 0 0 8px;text-align:center;top:120px;.badge-primary-background-color:#9c27b0}.fixed-plugin .github-btn,.fixed-plugin .SocialMediaShareButton{display:inline-block}.fixed-plugin .badge,.fixed-plugin li>a{transition:all .34s;-webkit-transition:all .34s;-moz-transition:all .34s;text-decoration:none}.fixed-plugin .fa-cog{color:#fff;padding:10px;border-radius:0 0 6px 6px;width:auto}.fixed-plugin .dropdown-menu{right:80px;left:auto;width:290px;border-radius:.1875rem;padding:0 10px;position:absolute;color:rgba(0,0,0,.87);display:inline-block;box-shadow:0 1px 4px 0 rgba(0,0,0,.14);background:#fff;border-radius:3px}.fixed-plugin .fa-circle-thin{color:#fff}.fixed-plugin .active .fa-circle-thin{color:#0bf}.fixed-plugin .dropdown-menu>.active>a,.fixed-plugin .dropdown-menu>.active>a:focus,.fixed-plugin .dropdown-menu>.active>a:hover{color:#777;text-align:center}.fixed-plugin img{border-radius:0;width:100%;height:100px;margin:0 auto}.fixed-plugin .dropdown-menu li>a:focus,.fixed-plugin .dropdown-menu li>a:hover{box-shadow:none}.fixed-plugin .badge{border:3px solid #fff;border-radius:50%;cursor:pointer;display:inline-block;height:23px;margin-right:5px;position:relative;width:23px;background-color:rgba(30,30,30,.97)}.fixed-plugin .badge.active,.fixed-plugin .badge:hover{border-color:#0bf}.fixed-plugin .badge-purple{background-color:#9c27b0}.fixed-plugin .badge-blue{background-color:#00bcd4}.fixed-plugin .badge-green{background-color:#4caf50}.fixed-plugin .badge-orange{background-color:#ff9800}.fixed-plugin .badge-red{background-color:#f44336}.fixed-plugin h5{font-size:14px;margin:10px}.fixed-plugin .dropdown-menu li{display:block;padding:4px 0;width:25%;float:left}.fixed-plugin li.adjustments-line,.fixed-plugin li.button-container,.fixed-plugin li.header-title{width:100%;height:50px;min-height:inherit;padding:0;text-align:center}.fixed-plugin li.adjustments-line p{margin:0}.fixed-plugin li.adjustments-line div,.fixed-plugin li.button-container div,.fixed-plugin li.header-title div{margin-bottom:5px}.fixed-plugin li.header-title{height:30px;line-height:25px;font-size:12px;font-weight:600;text-align:center;text-transform:uppercase}.fixed-plugin .adjustments-line p{float:left;display:inline-block;margin-bottom:0;font-size:1em;color:#3c4858}.fixed-plugin .adjustments-line a{color:transparent}.fixed-plugin .adjustments-line a .badge-colors{position:relative;top:-2px}.fixed-plugin .adjustments-line a a:focus,.fixed-plugin .adjustments-line a a:hover{color:transparent}.fixed-plugin .adjustments-line .dropdown-menu>li.adjustments-line>a{padding-right:0;padding-left:0;border-bottom:1px solid #ddd;border-radius:0;margin:0}.fixed-plugin .dropdown-menu>li>a.img-holder{font-size:16px;text-align:center;border-radius:10px;background-color:#fff;border:3px solid #fff;opacity:1;cursor:pointer;display:block;max-height:100px;overflow:hidden;padding:0}.fixed-plugin .dropdown-menu>li>a.img-holder img{margin-top:auto}.fixed-plugin .dropdown-menu>li:focus>a.img-holder,.fixed-plugin .dropdown-menu>li:hover>a.img-holder{border-color:rgba(0,187,255,.53)}.fixed-plugin .dropdown-menu>.active>a.img-holder{border-color:#0bf;background-color:#fff}.fixed-plugin .dropdown .dropdown-menu{transform:translateY(-15%);top:27px;opacity:0;transform-origin:0 0;display:none}.fixed-plugin .dropdown .dropdown-menu:before{border-bottom:.4em solid transparent;border-left:.4em solid rgba(0,0,0,.2);border-top:.4em solid transparent;right:-16px;top:46px}.fixed-plugin .dropdown .dropdown-menu:after{border-bottom:.4em solid transparent;border-left:.4em solid #fff;border-top:.4em solid transparent;right:-16px}.fixed-plugin .dropdown .dropdown-menu:after,.fixed-plugin .dropdown .dropdown-menu:before{content:"";display:inline-block;position:absolute;top:46px;width:16px;transform:translateY(-50%);-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%)}.fixed-plugin .dropdown.show .dropdown-menu{display:block;visibility:visible;opacity:1;transform:translateY(-13%);transform-origin:0 0}.fixed-plugin.rtl-fixed-plugin{right:auto;left:0;border-radius:0 8px 8px 0}.fixed-plugin.rtl-fixed-plugin .dropdown-menu{right:auto;left:80px}*{letter-spacing:normal!important}
/*# sourceMappingURL=main.13f8852b.chunk.css.map */