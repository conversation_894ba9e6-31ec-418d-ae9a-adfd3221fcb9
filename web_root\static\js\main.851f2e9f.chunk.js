/*! For license information please see main.851f2e9f.chunk.js.LICENSE.txt */
(this["webpackJsonpnano-portal"]=this["webpackJsonpnano-portal"]||[]).push([[0],{193:function(e,a,t){e.exports=t.p+"static/media/login_background.327add31.jpg"},311:function(e,a,t){e.exports=t.p+"static/media/sidebar.18c01f03.jpg"},312:function(e,a,t){e.exports=t.p+"static/media/nano_white.ad11d364.svg"},343:function(e,a,t){e.exports=t(518)},517:function(e,a,t){},518:function(e,a,t){"use strict";t.r(a);var n=t(0),o=t.n(n),l=t(24),r=t.n(l),c=t(315),i=t(600),s=t(43),u=t(36),m=t(559),d=t(3),p=function(e){e=(e+="").replace("#","");if(!/[0-9A-Fa-f]/g.test(e)||3!==e.length&&6!==e.length)throw new Error("input is not a valid hex color.");if(3===e.length){var a=e[0],t=e[1],n=e[2];e=a+a+t+t+n+n}var o=(e=e.toUpperCase(e))[0]+e[1],l=e[2]+e[3],r=e[4]+e[5];return parseInt(o,16)+", "+parseInt(l,16)+", "+parseInt(r,16)},f={transition:"all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1)"},b={paddingRight:"15px",paddingLeft:"15px",marginRight:"auto",marginLeft:"auto"},g={fontFamily:'"Roboto", "Helvetica", "Arial", sans-serif',fontWeight:"300",lineHeight:"1.5em"},h=["#9c27b0","#ab47bc","#8e24aa","#af2cc5"],E=["#ff9800","#ffa726","#fb8c00","#ffa21a"],v=["#f44336","#ef5350","#e53935","#f55a4e"],y=["#4caf50","#66bb6a","#43a047","#5cb860"],x=["#00acc1","#26c6da","#00acc1","#00d3ee"],k=["#e91e63","#ec407a","#d81b60","#eb3573"],C=["#999","#777","#3C4858","#AAAAAA","#D2D2D2","#DDD","#b4b4b4","#555555","#333","#a9afbb","#eee","#e7e7e7"],S={boxShadow:"0 10px 30px -12px rgba("+p("#000")+", 0.42), 0 4px 25px 0px rgba("+p("#000")+", 0.12), 0 8px 10px -5px rgba("+p("#000")+", 0.2)"},j={boxShadow:"0 4px 20px 0 rgba("+p("#000")+",.14), 0 7px 10px -5px rgba("+p(h[0])+",.4)"},O={boxShadow:"0 4px 20px 0 rgba("+p("#000")+",.14), 0 7px 10px -5px rgba("+p(x[0])+",.4)"},w={boxShadow:"0 4px 20px 0 rgba("+p("#000")+",.14), 0 7px 10px -5px rgba("+p(y[0])+",.4)"},_={boxShadow:"0 4px 20px 0 rgba("+p("#000")+",.14), 0 7px 10px -5px rgba("+p(E[0])+",.4)"},N={boxShadow:"0 4px 20px 0 rgba("+p("#000")+",.14), 0 7px 10px -5px rgba("+p(v[0])+",.4)"},R={boxShadow:"0 4px 20px 0 rgba("+p("#000")+",.14), 0 7px 10px -5px rgba("+p(k[0])+",.4)"},T=Object(d.a)({background:"linear-gradient(60deg, "+E[1]+", "+E[2]+")"},_),I=Object(d.a)({background:"linear-gradient(60deg, "+y[1]+", "+y[2]+")"},w),D=Object(d.a)({background:"linear-gradient(60deg, "+v[1]+", "+v[2]+")"},N),A=Object(d.a)({background:"linear-gradient(60deg, "+x[1]+", "+x[2]+")"},O),P=Object(d.a)({background:"linear-gradient(60deg, "+h[1]+", "+h[2]+")"},j),F=Object(d.a)({background:"linear-gradient(60deg, "+k[1]+", "+k[2]+")"},R),B=(Object(d.a)({margin:"0 20px 10px",paddingTop:"10px",borderTop:"1px solid "+C[10],height:"auto"},g),p("#000"),p("#000"),{border:"0",borderRadius:"3px",boxShadow:"0 10px 20px -12px rgba("+p("#000")+", 0.42), 0 3px 20px 0px rgba("+p("#000")+", 0.12), 0 8px 10px -5px rgba("+p("#000")+", 0.2)",padding:"10px 0",transition:"all 150ms ease 0s"}),M={color:C[2],textDecoration:"none",fontWeight:"300",marginTop:"30px",marginBottom:"25px",minHeight:"32px",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif","& small":{color:C[1],fontWeight:"400",lineHeight:"1"}},z=(Object(d.a)(Object(d.a)({},M),{},{marginTop:"0",marginBottom:"3px",minHeight:"auto","& a":Object(d.a)(Object(d.a)({},M),{},{marginTop:".625rem",marginBottom:"0.75rem",minHeight:"auto"})}),{defaultFontStyle:Object(d.a)(Object(d.a)({},g),{},{fontSize:"14px"}),defaultHeaderMargins:{marginTop:"20px",marginBottom:"10px"},quote:{padding:"10px 20px",margin:"0 0 20px",fontSize:"17.5px",borderLeft:"5px solid "+C[10]},quoteText:{margin:"0 0 10px",fontStyle:"italic"},quoteAuthor:{display:"block",fontSize:"80%",lineHeight:"1.42857143",color:C[1]},mutedText:{color:C[1]},primaryText:{color:h[0]},infoText:{color:x[0]},successText:{color:y[0]},warningText:{color:E[0]},dangerText:{color:v[0]}}),W=Object(m.a)(z);function q(e){var a=W(),t=e.children;return o.a.createElement("div",{className:a.defaultFontStyle+" "+a.dangerText},t)}var H=t(1),L=t(35),U=t(278),G=(t(348),t(28)),V=t.n(G),$=t(608),Z=t(581),Y=t(582),J=t(580),Q=t(579),K=t(320),X=t(95),ee=t(283),ae=t.n(ee),te=t(603),ne=t(284),oe=t.n(ne),le=t(6),re=t(561),ce={button:{minHeight:"auto",minWidth:"auto",backgroundColor:C[0],color:"#FFF",boxShadow:"0 2px 2px 0 rgba("+p(C[0])+", 0.14), 0 3px 1px -2px rgba("+p(C[0])+", 0.2), 0 1px 5px 0 rgba("+p(C[0])+", 0.12)",border:"none",borderRadius:"3px",position:"relative",padding:"12px 30px",margin:".3125rem 1px",fontSize:"12px",fontWeight:"400",textTransform:"uppercase",letterSpacing:"0",willChange:"box-shadow, transform",transition:"box-shadow 0.2s cubic-bezier(0.4, 0, 1, 1), background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1)",lineHeight:"1.42857143",textAlign:"center",whiteSpace:"nowrap",verticalAlign:"middle",touchAction:"manipulation",cursor:"pointer","&:hover,&:focus":{color:"#FFF",backgroundColor:C[0],boxShadow:"0 14px 26px -12px rgba("+p(C[0])+", 0.42), 0 4px 23px 0px rgba("+p("#000")+", 0.12), 0 8px 10px -5px rgba("+p(C[0])+", 0.2)"},"& .fab,& .fas,& .far,& .fal, &.material-icons":{position:"relative",display:"inline-block",top:"0",marginTop:"-1em",marginBottom:"-1em",fontSize:"1.1rem",marginRight:"4px",verticalAlign:"middle"},"& svg":{position:"relative",display:"inline-block",top:"0",width:"18px",height:"18px",marginRight:"4px",verticalAlign:"middle"},"&$justIcon":{"& .fab,& .fas,& .far,& .fal,& .material-icons":{marginTop:"0px",position:"absolute",width:"100%",transform:"none",left:"0px",top:"0px",height:"100%",lineHeight:"41px",fontSize:"20px"}}},white:{"&,&:focus,&:hover":{backgroundColor:"#FFF",color:C[0]}},rose:{backgroundColor:k[0],boxShadow:"0 2px 2px 0 rgba("+p(k[0])+", 0.14), 0 3px 1px -2px rgba("+p(k[0])+", 0.2), 0 1px 5px 0 rgba("+p(k[0])+", 0.12)","&:hover,&:focus":{backgroundColor:k[0],boxShadow:"0 14px 26px -12px rgba("+p(k[0])+", 0.42), 0 4px 23px 0px rgba("+p("#000")+", 0.12), 0 8px 10px -5px rgba("+p(k[0])+", 0.2)"}},primary:{backgroundColor:h[0],boxShadow:"0 2px 2px 0 rgba("+p(h[0])+", 0.14), 0 3px 1px -2px rgba("+p(h[0])+", 0.2), 0 1px 5px 0 rgba("+p(h[0])+", 0.12)","&:hover,&:focus":{backgroundColor:h[0],boxShadow:"0 14px 26px -12px rgba("+p(h[0])+", 0.42), 0 4px 23px 0px rgba("+p("#000")+", 0.12), 0 8px 10px -5px rgba("+p(h[0])+", 0.2)"}},info:{backgroundColor:x[0],boxShadow:"0 2px 2px 0 rgba("+p(x[0])+", 0.14), 0 3px 1px -2px rgba("+p(x[0])+", 0.2), 0 1px 5px 0 rgba("+p(x[0])+", 0.12)","&:hover,&:focus":{backgroundColor:x[0],boxShadow:"0 14px 26px -12px rgba("+p(x[0])+", 0.42), 0 4px 23px 0px rgba("+p("#000")+", 0.12), 0 8px 10px -5px rgba("+p(x[0])+", 0.2)"}},success:{backgroundColor:y[0],boxShadow:"0 2px 2px 0 rgba("+p(y[0])+", 0.14), 0 3px 1px -2px rgba("+p(y[0])+", 0.2), 0 1px 5px 0 rgba("+p(y[0])+", 0.12)","&:hover,&:focus":{backgroundColor:y[0],boxShadow:"0 14px 26px -12px rgba("+p(y[0])+", 0.42), 0 4px 23px 0px rgba("+p("#000")+", 0.12), 0 8px 10px -5px rgba("+p(y[0])+", 0.2)"}},warning:{backgroundColor:E[0],boxShadow:"0 2px 2px 0 rgba("+p(E[0])+", 0.14), 0 3px 1px -2px rgba("+p(E[0])+", 0.2), 0 1px 5px 0 rgba("+p(E[0])+", 0.12)","&:hover,&:focus":{backgroundColor:E[0],boxShadow:"0 14px 26px -12px rgba("+p(E[0])+", 0.42), 0 4px 23px 0px rgba("+p("#000")+", 0.12), 0 8px 10px -5px rgba("+p(E[0])+", 0.2)"}},danger:{backgroundColor:v[0],boxShadow:"0 2px 2px 0 rgba("+p(v[0])+", 0.14), 0 3px 1px -2px rgba("+p(v[0])+", 0.2), 0 1px 5px 0 rgba("+p(v[0])+", 0.12)","&:hover,&:focus":{backgroundColor:v[0],boxShadow:"0 14px 26px -12px rgba("+p(v[0])+", 0.42), 0 4px 23px 0px rgba("+p("#000")+", 0.12), 0 8px 10px -5px rgba("+p(v[0])+", 0.2)"}},simple:{"&,&:focus,&:hover":{color:"#FFF",background:"transparent",boxShadow:"none"},"&$rose":{"&,&:focus,&:hover,&:visited":{color:k[0]}},"&$primary":{"&,&:focus,&:hover,&:visited":{color:h[0]}},"&$info":{"&,&:focus,&:hover,&:visited":{color:x[0]}},"&$success":{"&,&:focus,&:hover,&:visited":{color:y[0]}},"&$warning":{"&,&:focus,&:hover,&:visited":{color:E[0]}},"&$danger":{"&,&:focus,&:hover,&:visited":{color:v[0]}}},transparent:{"&,&:focus,&:hover":{color:"inherit",background:"transparent",boxShadow:"none"}},disabled:{opacity:"0.65",pointerEvents:"none"},lg:{padding:"1.125rem 2.25rem",fontSize:"0.875rem",lineHeight:"1.333333",borderRadius:"0.2rem"},sm:{padding:"0.40625rem 1.25rem",fontSize:"0.6875rem",lineHeight:"1.5",borderRadius:"0.2rem"},round:{borderRadius:"30px"},block:{width:"100% !important"},link:{"&,&:hover,&:focus":{backgroundColor:"transparent",color:C[0],boxShadow:"none"}},justIcon:{paddingLeft:"12px",paddingRight:"12px",fontSize:"20px",height:"41px",minWidth:"41px",width:"41px","& .fab,& .fas,& .far,& .fal,& svg,& .material-icons":{marginRight:"0px"},"&$lg":{height:"57px",minWidth:"57px",width:"57px",lineHeight:"56px","& .fab,& .fas,& .far,& .fal,& .material-icons":{fontSize:"32px",lineHeight:"56px"},"& svg":{width:"32px",height:"32px"}},"&$sm":{height:"30px",minWidth:"30px",width:"30px","& .fab,& .fas,& .far,& .fal,& .material-icons":{fontSize:"17px",lineHeight:"29px"},"& svg":{width:"17px",height:"17px"}}}},ie=["color","round","children","disabled","simple","size","block","link","justIcon","className","muiClasses"],se=Object(m.a)(ce);function ue(e){var a,t=se(),n=e.color,l=e.round,r=e.children,c=e.disabled,i=e.simple,s=e.size,u=e.block,m=e.link,d=e.justIcon,p=e.className,f=e.muiClasses,b=Object(L.a)(e,ie),g=V()((a={},Object(le.a)(a,t.button,!0),Object(le.a)(a,t[s],s),Object(le.a)(a,t[n],n),Object(le.a)(a,t.round,l),Object(le.a)(a,t.disabled,c),Object(le.a)(a,t.simple,i),Object(le.a)(a,t.block,u),Object(le.a)(a,t.link,m),Object(le.a)(a,t.justIcon,d),Object(le.a)(a,p,p),a));return o.a.createElement(re.a,Object.assign({},b,{classes:f,className:g}),r)}var me=t(169),de=t.n(me),pe=t(568),fe=t(567),be=t(319),ge=t(228),he=t(530),Ee=t(529),ve=t(578),ye=t(604),xe=t(572),ke=t(566),Ce=t(609),Se=t(605),je=t(569),Oe=t(570),we=t(565),_e=t(522),Ne=t(571),Re=t(526),Te=t(606),Ie=t(612);function De(e){var{type:a,label:t,value:n,onChange:l,required:r,oneRow:c,disabled:i,options:s,on:u,off:m,rows:d,step:p,maxStep:f,minStep:b,marks:g,helper:h,valueLabelFormat:E,valueLabelDisplay:v,...y}=e;let x;switch(a){case"text":x=o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:t,onChange:l,value:n,margin:"normal",required:r,disabled:i,helperText:h,fullWidth:!0}));break;case"password":x=o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:t,onChange:l,value:n,type:"password",margin:"normal",required:r,disabled:i,fullWidth:!0}));break;case"file":x=o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:t,onChange:l,type:"file",margin:"normal",required:r,disabled:i,fullWidth:!0}));break;case"textarea":d<2&&(d=3),x=o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:t,onChange:l,value:n,margin:"normal",required:r,disabled:i,rowsMax:d,multiline:!0,fullWidth:!0}));break;case"select":x=o.a.createElement($.a,{m:0,pt:2},o.a.createElement(ke.a,null,t),o.a.createElement(ye.a,{value:n,onChange:l,required:r,disabled:i,fullWidth:!0},s.map((e,a)=>o.a.createElement(pe.a,{value:e.value,key:a,disabled:e.disabled},e.label))));break;case"radio":x=o.a.createElement($.a,{m:0,pt:2},o.a.createElement(we.a,{component:"fieldset",fullWidth:!0,disabled:i},o.a.createElement(_e.a,{component:"legend"},t),o.a.createElement(je.a,{name:"type",value:n,onChange:l,row:!0},o.a.createElement($.a,{display:"flex",alignItems:"center"},s.map((e,a)=>o.a.createElement($.a,{key:a},o.a.createElement(Oe.a,{value:e.value,control:o.a.createElement(Se.a,null),label:e.label})))))));break;case"switch":x=o.a.createElement($.a,{m:0,pt:2},o.a.createElement(ke.a,null,t),o.a.createElement(Ne.a,{item:!0},m,o.a.createElement(xe.a,{checked:n,onChange:l,color:"primary"}),u));break;case"checkbox":x=o.a.createElement($.a,{m:0,pt:2},o.a.createElement(we.a,{component:"fieldset",fullWidth:!0},o.a.createElement(_e.a,{component:"legend"},t),o.a.createElement(Re.a,null,o.a.createElement(Ne.a,{container:!0},s.map((e,a)=>{const t=e.value,r=e.label;let c;return c=!!n.has(t)&&n.get(t),o.a.createElement(Ne.a,{item:!0,xs:6,sm:3,key:a},o.a.createElement(Oe.a,{control:o.a.createElement(Te.a,{checked:c,onChange:l(t),value:t}),label:r}))})))));break;case"slider":v||(v="auto"),x=o.a.createElement($.a,{m:0,pt:2},o.a.createElement(_e.a,{component:"legend"},t),o.a.createElement(Ie.a,{color:"secondary",value:n,max:f,min:b,step:p,valueLabelDisplay:v,marks:g,onChange:l,valueLabelFormat:E}));break;default:return o.a.createElement("div",null)}var k=o.a.createElement(Ne.a,Object.assign({item:!0},y),x);return c?o.a.createElement(Ne.a,{item:!0,xs:12},k):k}function Ae(e){const{inputs:a}=e;return o.a.createElement(Ne.a,{container:!0},a.map((e,a)=>o.a.createElement(De,Object.assign({key:a},e))))}var Pe=t(573),Fe=t(577),Be=t(575),Me=t(574),ze=t(576),We=t(527),qe=["children"],He=Object(m.a)((function(e){return{backdrop:{zIndex:e.zIndex.drawer+1,color:"#fff"}}}));function Le(e){var a=He(),t=e.children,n=Object(L.a)(e,qe);return o.a.createElement(We.a,Object.assign({className:a.backdrop},n),t)}var Ue=["children"],Ge=Object(m.a)({grid:{padding:"0 15px !important"}});function Ve(e){var a=Ge(),t=e.children,n=Object(L.a)(e,Ue);return o.a.createElement(Ne.a,Object.assign({item:!0},n,{className:a.grid}),t)}var $e=t(528),Ze=t(168),Ye=t.n(Ze),Je={root:Object(d.a)(Object(d.a)({},g),{},{flexWrap:"unset",position:"relative",padding:"20px 15px",lineHeight:"20px",marginBottom:"20px",fontSize:"14px",backgroundColor:"#FFF",color:C[7],borderRadius:"3px",minWidth:"unset",maxWidth:"unset",boxShadow:"0 12px 20px -10px rgba("+p("#FFF")+", 0.28), 0 4px 20px 0px rgba("+p("#000")+", 0.12), 0 7px 8px -5px rgba("+p("#FFF")+", 0.2)"}),top20:{top:"20px"},top40:{top:"40px"},info:Object(d.a)({backgroundColor:x[3],color:"#FFF"},O),success:Object(d.a)({backgroundColor:y[3],color:"#FFF"},w),warning:Object(d.a)({backgroundColor:E[3],color:"#FFF"},_),danger:Object(d.a)({backgroundColor:v[3],color:"#FFF"},N),primary:Object(d.a)({backgroundColor:h[3],color:"#FFF"},j),rose:Object(d.a)({backgroundColor:k[3],color:"#FFF"},R),message:{padding:"0",display:"block",maxWidth:"89%"},close:{width:"11px",height:"11px"},iconButton:{width:"24px",height:"24px",padding:"0px"},icon:{display:"block",left:"15px",position:"absolute",top:"50%",marginTop:"-15px",width:"30px",height:"30px"},infoIcon:{color:x[3]},successIcon:{color:y[3]},warningIcon:{color:E[3]},dangerIcon:{color:v[3]},primaryIcon:{color:h[3]},roseIcon:{color:k[3]},iconMessage:{paddingLeft:"50px",display:"block"},actionRTL:{marginLeft:"-8px",marginRight:"auto"}},Qe=Object(m.a)(Je);function Ke(e){var a=Qe(),t=e.message,n=e.color,l=e.close,r=e.icon,c=e.rtlActive,i=[],s=V()(Object(le.a)({},a.iconMessage,void 0!==r));return void 0!==l&&(i=[o.a.createElement(K.a,{className:a.iconButton,key:"close","aria-label":"Close",color:"inherit"},o.a.createElement(Ye.a,{className:a.close}))]),o.a.createElement($e.a,{message:o.a.createElement("div",null,void 0!==r?o.a.createElement(e.icon,{className:a.icon}):null,o.a.createElement("span",{className:s},t)),classes:{root:a.root+" "+a[n],message:a.message,action:V()(Object(le.a)({},a.actionRTL,c))},action:i})}function Xe(e){var a,t,n=e.open,l=e.size,r=e.operatable,c=e.promptPosition,i=e.prompt,s=e.title,u=e.content,m=e.buttons,d=e.hideBackdrop;a=i?o.a.createElement(Ve,{xs:12},o.a.createElement(Ke,{message:i,color:"warning"})):o.a.createElement(Ve,{xs:12}),t="top"===c?o.a.createElement(Ne.a,{container:!0},a,o.a.createElement(Ve,{xs:12},u)):o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:12},u),a);var p=[];return r&&m.forEach((function(e,a){p.push(o.a.createElement(ue,{onClick:e.onClick,color:e.color,key:a},e.label))})),o.a.createElement(Pe.a,{open:n,maxWidth:l,fullWidth:!0},o.a.createElement(Me.a,null,s),o.a.createElement(Be.a,null,o.a.createElement(Le,{open:!d&&!r},o.a.createElement(ze.a,{color:"inherit"})),t),o.a.createElement(Fe.a,null,o.a.createElement($.a,{display:"block",displayPrint:"none"},p)))}var ea=t(117),aa=t.n(ea);function ta(){var e=localStorage.getItem("nano-session-data");if(!e||0===e.length)return null;var a=JSON.parse(e);return a.id?a:null}function na(){localStorage.setItem("nano-session-data","")}function oa(){return o.a.createElement(u.a,{to:"/login/?previous="+encodeURIComponent(window.location.pathname+window.location.search)})}function la(e){var a=function(e,a,t){return 0===e%a?(e/a).toString()+" "+t:(e/a).toFixed(2)+" "+t};return e>=1<<30?a(e,1<<30,"GB"):e>=1<<20?a(e,1<<20,"MB"):e>=1024?a(e,1024,"KB"):e.toString()+" Bytes"}function ra(e){var a=function(e,a,t){return 0===e%a?(e/a).toString()+" "+t:(e/a).toFixed(2)+" "+t};return e>=1<<27?a(e,1<<27,"Gb/s"):e>=1<<17?a(e,1<<17,"Mb/s"):e>=128?a(e,128,"Kb/s"):e.toString()+" Bits/s"}function ca(e,a){var t=Math.pow(10,a);return Math.round(e*t)/t}var ia=t(94);const sa=ia.version;let ua="/api/v1";function ma(e){const a={en:{dashboard:"Dashboard",computePool:"Compute Pools",addressPool:"Address Pools",storagePool:"Storage Pools",instance:"Instances",diskImage:"Disk Image",mediaImage:"Media Image",systemTempaltes:"System Templates",securityPolicies:"Security Policies",user:"Users",log:"Logs",visibility:"Resource Visibility"},cn:{dashboard:"\u4eea\u8868\u76d8",computePool:"\u8ba1\u7b97\u8d44\u6e90\u6c60",addressPool:"\u5730\u5740\u8d44\u6e90\u6c60",storagePool:"\u5b58\u50a8\u8d44\u6e90\u6c60",instance:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",diskImage:"\u78c1\u76d8\u955c\u50cf",mediaImage:"\u5149\u76d8\u955c\u50cf",systemTempaltes:"\u7cfb\u7edf\u6a21\u677f",securityPolicies:"\u5b89\u5168\u7b56\u7565\u7ec4",user:"\u7528\u6237",log:"\u65e5\u5fd7",visibility:"\u8d44\u6e90\u53ef\u89c1\u6027"}}[e];return[{value:"dashboard",label:a.dashboard},{value:"compute_pool",label:a.computePool},{value:"address_pool",label:a.addressPool},{value:"storage_pool",label:a.storagePool},{value:"instance",label:a.instance},{value:"image",label:a.diskImage},{value:"media",label:a.mediaImage},{value:"templates",label:a.systemTempaltes},{value:"policies",label:a.securityPolicies},{value:"user",label:a.user},{value:"log",label:a.log},{value:"visibility",label:a.visibility}]}function da(e,a){Wa("/compute_pools/",e,a)}function pa(e,a,t){Wa("/compute_pool_cells/"+e,a,t)}function fa(e,a,t,n,o){Ga("/compute_pool_cells/"+e+"/"+a,{enable:t},()=>{n(e,a,t)},o)}function ba(e,a){Wa("/storage_pools/",e,a)}function ga(e,a){Wa("/address_pools/",e,a)}function ha(e,a,t){Wa("/address_pools/"+e,a,t)}function Ea(e,a,t,n){if(e){var o="/guest_search/?pool="+e;a&&(o+="&cell="+a),Wa(o,t,n)}else n("must specify pool name")}function va(e,a,t,n){qa("/guests/"+e,(e,o)=>{if(201===e){const{progress:e,name:a,created:t}=o;n(e,a,t)}else 200===e?a(o):t("unexpected status "+e.toString())},t)}function ya(e,a,t,n){qa("/instances/"+e,(e,o)=>{if(201===e){const{progress:e,name:a,created:t}=o;n(e,a,t)}else 200===e?a(o):t("unexpected status "+e.toString())},t)}function xa(e,a,t){Va("/instances/"+e+"/media",()=>{a(e)},t)}function ka(e,a,t){Za("/instances/"+e,{reboot:!1,force:!1},()=>{a(e)},t)}function Ca(e,a,t){Za("/instances/"+e,{reboot:!0,force:!1},()=>{a(e)},t)}function Sa(e,a,t){Za("/instances/"+e,{reboot:!0,force:!0},()=>{a(e)},t)}function ja(e,a,t){Wa("/guests/"+e+"/auth",t=>{let{user:n,password:o}=t;a(n,o,e)},t)}function Oa(e,a){Wa("/media_image_search/",e,a)}function wa(e,a,t){Va("/media_images/"+e,()=>{a(e)},t)}function _a(e,a){Wa("/disk_image_search/",e,a)}function Na(e,a,t){Wa("/disk_images/"+e,a,t)}function Ra(e,a,t,n,o,l){var r=ta();if(null===r)return void l("session expired");var c={name:e,description:t,tags:n,owner:r.user,group:r.group};a&&(c.guest=a);Ha("/disk_images/",c,e=>{o(e.id)},l)}function Ta(e,a,t){Va("/disk_images/"+e,()=>{a(e)},t)}function Ia(e,a,t,n){qa("/batch/create_guest/"+e,(e,o)=>{202===e?a(o):200===e?t(o):n("unexpected status "+e.toString())},n)}function Da(e,a,t,n){qa("/batch/delete_guest/"+e,(e,o)=>{202===e?a(o):200===e?t(o):n("unexpected status "+e.toString())},n)}function Aa(e,a,t,n){qa("/batch/stop_guest/"+e,(e,o)=>{202===e?a(o):200===e?t(o):n("unexpected status "+e.toString())},n)}function Pa(e,a){Wa("/templates/",e,a)}function Fa(e,a,t,n,o,l){let r="/search/security_policy_groups/";var c=new URLSearchParams;e&&c.append("owner",e),a&&c.append("group",a),t&&c.append("enabled_only",t),n&&c.append("global_only",n),""!==c.toString()&&(r+="?"+c.toString()),Wa(r,o,l)}function Ba(e,a){Wa("/roles/",e,a)}function Ma(e,a,t){var n=ta();if(null===n)return void t("session expired");var o=n.group+"."+n.user;n.address?o+="("+n.address+") : "+e:o+=": "+e;Ha("/logs/",{content:o},a,t)}function za(e,a){!function(e,a,t){Ka("get",e,null,a,t)}("/system/",e,a)}function Wa(e,a,t){Ja("get",e,null,a,t)}function qa(e,a,t){Qa("get",e,null,a,t)}function Ha(e,a,t,n){Ja("post",e,a,t,n)}function La(e,a,t,n){Qa("post",e,a,t,n)}function Ua(e,a,t,n){Ka("post",e,a,t,n)}function Ga(e,a,t,n){Ja("put",e,a,t,n)}function Va(e,a,t){Ja("delete",e,null,a,t)}function $a(e,a,t,n){Ja("patch",e,a,t,n)}function Za(e,a,t,n){Ja("delete",e,a,t,n)}function Ya(e,a,t,n,o,l){var r=ta();if(null===r)return void l("session expired");const c={"Nano-Session":r.id};var i=new FormData;i.append(a,t),aa.a.post(ua+e,i,{onUploadProgress:e=>{var a=100*e.loaded/e.total;n(a)},headers:c}).then(e=>{let{data:a}=e;0===a.error_code?o(a.data):l(a.message)}).catch(e=>{l(e.message)})}function Ja(e,a,t,n,o){var l=ta();if(null!==l){var r={method:e,url:ua+a,headers:{"Nano-Session":l.id}};t&&(r.data=t),aa()(r).then(e=>{let{data:a}=e;0===a.error_code?n&&n(a.data):o&&o(a.message)}).catch(e=>{o&&o(e.message)})}else o&&o("session expired")}function Qa(e,a,t,n,o){var l=ta();if(null!==l){var r={method:e,url:ua+a,headers:{"Nano-Session":l.id}};t&&(r.data=t),aa()(r).then(e=>{let{data:a,status:t}=e;0===a.error_code?n&&n(t,a.data):o&&o(a.message)}).catch(e=>{o&&o(e.message)})}else o&&o("session expired")}function Ka(e,a,t,n,o){var l={method:e,url:ua+a};t&&(l.data=t),aa()(l).then(e=>{let{data:a}=e;0===a.error_code?n&&n(a.data):o&&o(a.message)}).catch(e=>{o&&o(e.message)})}ia.service&&ia.service.host&&(ua=ia.service.port?"http://".concat(ia.service.host,":").concat(ia.service.port,"/api/v1"):"http://".concat(ia.service.host,"/api/v1"));var Xa={en:{title:"Modify Password",current:"Current Password",new:"New Password",confirmNew:"Confirm New Password",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u5bc6\u7801",current:"\u5f53\u524d\u5bc6\u7801",new:"\u65b0\u5bc6\u7801",confirmNew:"\u786e\u8ba4\u65b0\u5bc6\u7801",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function et(e){var a={old:"",new:"",new2:""},t=e.lang,n=e.user,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!0),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(!1),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(""),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(a),k=Object(H.a)(x,2),C=k[0],S=k[1],j=Xa[t],O=j.title,w=function(){y(""),S(a)},_=function(e){b&&(m(!0),y(e))},N=function(){Ma("change password of "+n),b&&(w(),m(!0),r())},R=function(e){return function(a){var t=a.target.value;S((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}};o.a.useEffect((function(){return g(!0),function(){g(!1)}}),[]);var T=[{type:"password",label:j.current,onChange:R("old"),value:C.old,required:!0,xs:12},{type:"password",label:j.new,onChange:R("new"),value:C.new,required:!0,xs:12},{type:"password",label:j.confirmNew,onChange:R("new2"),value:C.new2,required:!0,xs:12}],I=o.a.createElement(Ae,{inputs:T}),D=[{color:"transparent",label:j.cancel,onClick:function(){w(),c()}},{color:"info",label:j.confirm,onClick:function(){m(!1),""!==C.old?""!==C.new?C.new2===C.new?function(e,a,t,n,o){Ga("/users/"+e+"/password/",{old:a,new:t},()=>{n(e)},o)}(n,C.old,C.new,N,_):_("confirm password mismatched"):_("new password required"):_("previous password required")}}];return o.a.createElement(Xe,{size:"sm",open:l,prompt:v,title:O,buttons:D,content:I,operatable:u})}var at=function(e){return{buttonLink:Object(le.a)({},e.breakpoints.down("md"),{display:"flex",marginLeft:"30px",width:"auto"}),links:Object(le.a)({width:"20px",height:"20px",zIndex:"4"},e.breakpoints.down("md"),{display:"block",width:"30px",height:"30px",color:C[9],marginRight:"15px"}),linkText:Object(d.a)(Object(d.a)({zIndex:"4"},g),{},{fontSize:"14px"}),popperClose:{pointerEvents:"none"},pooperResponsive:Object(le.a)({},e.breakpoints.down("md"),{zIndex:"1640",position:"static",float:"none",width:"auto",marginTop:"0",backgroundColor:"transparent",border:"0",WebkitBoxShadow:"none",boxShadow:"none",color:"black"}),popperNav:Object(le.a)({},e.breakpoints.down("sm"),{position:"static !important",left:"unset !important",top:"unset !important",transform:"none !important",willChange:"unset !important","& > div":{boxShadow:"none !important",marginLeft:"0rem",marginRight:"0rem",transition:"none !important",marginTop:"0px !important",marginBottom:"0px !important",padding:"0px !important",backgroundColor:"transparent !important","& ul li":{color:"#FFF !important",margin:"10px 15px 0!important",padding:"10px 15px !important","&:hover":{backgroundColor:"hsla(0,0%,78%,.2)",boxShadow:"none"}}}}),dropdown:{borderRadius:"3px",border:"0",boxShadow:"0 2px 5px 0 rgba("+p("#000")+", 0.26)",top:"100%",zIndex:"1000",minWidth:"160px",padding:"5px 0",margin:"2px 0 0",fontSize:"14px",textAlign:"left",listStyle:"none",backgroundColor:"#FFF",WebkitBackgroundClip:"padding-box",backgroundClip:"padding-box"},dropdownItem:Object(d.a)(Object(d.a)({},g),{},{fontSize:"13px",padding:"10px 20px",margin:"0 5px",borderRadius:"2px",WebkitTransition:"all 150ms linear",MozTransition:"all 150ms linear",OTransition:"all 150ms linear",MsTransition:"all 150ms linear",transition:"all 150ms linear",display:"block",clear:"both",fontWeight:"400",lineHeight:"1.42857143",color:C[8],whiteSpace:"nowrap",height:"unset",minHeight:"unset","&:hover":Object(d.a)({backgroundColor:h[0],color:"#FFF"},j)})}},tt=function(e){var a,t,n;return Object(d.a)(Object(d.a)({},at(e)),{},{search:Object(le.a)({"& > div":{marginTop:"0"}},e.breakpoints.down("sm"),{margin:"10px 15px !important",float:"none !important",paddingTop:"1px",paddingBottom:"1px",padding:"0!important",width:"60%",marginTop:"40px","& input":{color:"#FFF"}}),linkText:Object(d.a)(Object(d.a)({zIndex:"4"},g),{},{fontSize:"14px",margin:"0px"}),buttonLink:Object(le.a)({},e.breakpoints.down("sm"),{display:"flex",margin:"10px 15px 0",width:"-webkit-fill-available","& svg":{width:"24px",height:"30px",marginRight:"15px",marginLeft:"-15px"},"& .fab,& .fas,& .far,& .fal,& .material-icons":{fontSize:"24px",lineHeight:"30px",width:"24px",height:"30px",marginRight:"15px",marginLeft:"-15px"},"& > span":{justifyContent:"flex-start",width:"100%"}}),searchButton:Object(le.a)({},e.breakpoints.down("sm"),{top:"-50px !important",marginRight:"22px",float:"right"}),margin:{zIndex:"4",margin:"0"},searchIcon:{width:"17px",zIndex:"4"},notifications:(a={zIndex:"4"},Object(le.a)(a,e.breakpoints.up("md"),{position:"absolute",top:"2px",border:"1px solid #FFF",right:"4px",fontSize:"9px",background:v[0],color:"#FFF",minWidth:"16px",height:"16px",borderRadius:"10px",textAlign:"center",lineHeight:"16px",verticalAlign:"middle",display:"block"}),Object(le.a)(a,e.breakpoints.down("sm"),Object(d.a)(Object(d.a)({},g),{},{fontSize:"14px",marginRight:"8px"})),a),manager:(t={},Object(le.a)(t,e.breakpoints.down("sm"),{width:"100%"}),Object(le.a)(t,"display","inline-block"),t),searchWrapper:(n={},Object(le.a)(n,e.breakpoints.down("sm"),{width:"-webkit-fill-available",margin:"10px 15px 0"}),Object(le.a)(n,"display","inline-block"),n)})},nt=Object(m.a)(tt),ot={en:{modify:"Modify Password",logout:"Logout"},cn:{modify:"\u4fee\u6539\u5bc6\u7801",logout:"\u6ce8\u9500"}};function lt(e){var a=nt(),t=e.lang,n=ot[t],l=o.a.useState(!1),r=Object(H.a)(l,2),c=r[0],i=r[1],s=o.a.useState(""),u=Object(H.a)(s,2),m=u[0],d=u[1],p=o.a.useState(null),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(!1),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState((function(){return null!==ta()})),k=Object(H.a)(x,2),C=k[0],S=k[1],j=function(){g(null)},O=function(){var e=function(){na(),S(!1)};Ma("logout",e,e)},w=function(){y(!0)},_=function(){y(!1)},N=o.a.useCallback((function(){!function(e){var a=ta();if(null!=a){var t=ua+"/sessions/"+a.id,n={"Nano-Session":a.id};aa.a.put(t,"",{headers:n}).then(a=>{let{data:t}=a;0===t.error_code||e(t.message)}).catch(a=>{e(a.message)})}else e("session expired")}((function(){na(),c&&S(!1)}))}),[c]);return o.a.useEffect((function(){i(!0),N();var e=ta();if(null!==e){c&&d(e.user);var a=1e3*e.timeout*2/3,t=setInterval((function(){N()}),a);return function(){i(!1),clearInterval(t)}}}),[c,N]),C?o.a.createElement("div",{className:a.manager},o.a.createElement(ue,{color:window.innerWidth>959?"transparent":"white",simple:!(window.innerWidth>959),"aria-owns":b?"profile-menu-list-grow":null,"aria-haspopup":"true",onClick:function(e){b&&b.contains(e.target)?g(null):g(e.currentTarget)},className:a.buttonLink},m,o.a.createElement(de.a,{className:a.icons})),o.a.createElement(Ee.a,{open:Boolean(b),anchorEl:b,transition:!0,disablePortal:!0,className:V()(Object(le.a)({},a.popperClose,!b))+" "+a.popperNav},(function(e){var t=e.TransitionProps,l=e.placement;return o.a.createElement(be.a,Object.assign({},t,{id:"profile-menu-list-grow",style:{transformOrigin:"bottom"===l?"center top":"center bottom"}}),o.a.createElement(ge.a,null,o.a.createElement(he.a,{onClickAway:j},o.a.createElement(fe.a,{role:"menu"},o.a.createElement(pe.a,{onClick:w,className:a.dropdownItem},n.modify),o.a.createElement(ve.a,{light:!0}),o.a.createElement(pe.a,{onClick:O,className:a.dropdownItem},n.logout)))))})),o.a.createElement(et,{lang:t,open:v,user:m,onSuccess:_,onCancel:_})):oa()}function rt(e){return o.a.createElement("div",null,o.a.createElement(lt,e))}var ct=t(280),it=t(281),st=t(127),ut=t(317),mt=t(316),dt=t(318),pt=t(282),ft=t.n(pt),bt=["lang","setLang"],gt=["buttonClass"],ht=function(e){Object(ut.a)(t,e);var a=Object(mt.a)(t);function t(e){var n;Object(ct.a)(this,t),(n=a.call(this,e)).openMenu=n.openMenu.bind(Object(st.a)(n)),n.closeMenu=n.closeMenu.bind(Object(st.a)(n)),n.languages=[{locale:"cn",name:"\u7b80\u4f53\u4e2d\u6587"},{locale:"en",name:"English"}];var o,l=e.lang,r=e.setLang,c=Object(L.a)(e,bt);return n.restProps=c,n.changeLanguage=r,n.languages.forEach((function(e){l===e.locale&&(o=e.name)})),n.state={language:l,anchorEl:null,displayText:o},n}return Object(it.a)(t,[{key:"updateLanguage",value:function(e){var a=this;this.languages.forEach((function(t){e===t.locale&&a.setState({displayText:t.name,anchorEl:null})})),function(e){var a=localStorage.getItem("nano-language-data");if(!a||0===a.length)return!1;var t=JSON.parse(a);!!t.lang&&(t.lang===e||(t.lang=e,localStorage.setItem("nano-language-data",JSON.stringify(t))))}(e),this.changeLanguage(e)}},{key:"openMenu",value:function(e){this.setState({anchorEl:e.currentTarget})}},{key:"closeMenu",value:function(){this.setState({anchorEl:null})}},{key:"render",value:function(){var e=this,a=this.state.language,t=this.restProps,n=t.buttonClass,l=Object(L.a)(t,gt);return o.a.createElement(X.a,{component:"div"},o.a.createElement(ue,Object.assign({},l,{onClick:this.openMenu,color:"transparent",size:"sm"}),o.a.createElement(ft.a,{fontSize:"small",className:n}),this.state.displayText),o.a.createElement(dt.a,{keepMounted:!0,anchorEl:this.state.anchorEl,onClose:this.closeMenu,open:Boolean(this.state.anchorEl)},this.languages.map((function(t){return o.a.createElement(pe.a,{key:t.locale,selected:t.locale===a,onClick:function(){e.updateLanguage(t.locale)}},o.a.createElement(X.a,{component:"div",variant:"overline"},t.name))}))))}}]),t}(o.a.Component),Et=function(){return{appBar:{backgroundColor:"transparent",boxShadow:"none",borderBottom:"0",marginBottom:"0",position:"absolute",width:"100%",paddingTop:"10px",zIndex:"1029",color:C[7],border:"0",borderRadius:"3px",padding:"10px 0",transition:"all 150ms ease 0s",minHeight:"50px",display:"block"},container:Object(d.a)(Object(d.a)({},b),{},{minHeight:"50px"}),flex:{flex:1},title:Object(d.a)(Object(d.a)({},g),{},{letterSpacing:"unset",lineHeight:"30px",fontSize:"18px",borderRadius:"3px",textTransform:"none",color:"inherit",margin:"0","&:hover,&:focus":{background:"transparent"}}),appResponsive:{top:"8px"},primary:Object(d.a)({backgroundColor:h[0],color:"#FFF"},B),info:Object(d.a)({backgroundColor:x[0],color:"#FFF"},B),success:Object(d.a)({backgroundColor:y[0],color:"#FFF"},B),warning:Object(d.a)({backgroundColor:E[0],color:"#FFF"},B),danger:Object(d.a)({backgroundColor:v[0],color:"#FFF"},B)}};const vt=Object(m.a)(Et),yt={en:{manual:"Online Manual",manualURL:"https://nanocloud.readthedocs.io/projects/guide/en/latest/"},cn:{manual:"\u5728\u7ebf\u6587\u6863",manualURL:"https://nanocloud.readthedocs.io/projects/guide/zh_CN/latest/"}};function xt(e){const a=vt();const{color:t,lang:n,setLang:l}=e,r=yt[n],c=V()({[" "+a[t]]:t});let i=(new Date).getFullYear();const s=o.a.createElement($.a,{mr:2},o.a.createElement(X.a,{component:"span"},"Project Nano ".concat(sa," \xa9 2018~").concat(i))),u=o.a.createElement(Q.a,{title:r.manual,placement:"top"},o.a.createElement(J.a,{target:"_blank",href:r.manualURL},o.a.createElement(K.a,{color:"default",size:"small"},o.a.createElement(ae.a,null))));return o.a.createElement(Z.a,{className:a.appBar+c},o.a.createElement(Y.a,{className:a.container},o.a.createElement("div",{className:a.flex},o.a.createElement(ue,{color:"transparent",href:"#",className:a.title},function(){var a="";return e.routes.every(t=>-1===window.location.href.indexOf(t.layout+t.path)||(a=t.display[e.lang],!1)),a}())),o.a.createElement(te.a,{smDown:!0},s,u),o.a.createElement(ht,{lang:n,setLang:l}),o.a.createElement(te.a,{smDown:!0,implementation:"css"},o.a.createElement(rt,{lang:n})),o.a.createElement(te.a,{mdUp:!0,implementation:"css"},o.a.createElement(K.a,{color:"inherit","aria-label":"open drawer",onClick:e.handleDrawerToggle},o.a.createElement(oe.a,null)))))}var kt=t(22),Ct=t(610),St=t(524),jt=t(525),Ot=t(584),wt=t(583),_t=function(e){var a,t;return{drawerPaper:Object(d.a)(Object(d.a)({border:"none",position:"fixed",top:"0",bottom:"0",left:"0",zIndex:"1"},S),{},(a={width:260},Object(le.a)(a,e.breakpoints.up("md"),{width:260,position:"fixed",height:"100%"}),Object(le.a)(a,e.breakpoints.down("sm"),Object(d.a)(Object(d.a)({width:260},S),{},{position:"fixed",display:"block",top:"0",height:"100vh",right:"0",left:"auto",zIndex:"1032",visibility:"visible",overflowY:"visible",borderTop:"none",textAlign:"left",paddingRight:"0px",paddingLeft:"0",transform:"translate3d(".concat(260,"px, 0, 0)")},f)),a)),drawerPaperRTL:(t={},Object(le.a)(t,e.breakpoints.up("md"),{left:"auto !important",right:"0 !important"}),Object(le.a)(t,e.breakpoints.down("sm"),{left:"0  !important",right:"auto !important"}),t),logo:{position:"relative",padding:"15px 15px",zIndex:"4","&:after":{content:'""',position:"absolute",bottom:"0",height:"1px",right:"15px",width:"calc(100% - 30px)",backgroundColor:"rgba("+p(C[6])+", 0.3)"}},logoLink:Object(d.a)(Object(d.a)({},g),{},{textTransform:"uppercase",padding:"5px 0",display:"block",fontSize:"18px",textAlign:"left",fontWeight:"400",lineHeight:"30px",textDecoration:"none",backgroundColor:"transparent","&,&:hover":{color:"#FFF"}}),logoLinkRTL:{textAlign:"right"},logoImage:{width:"30px",display:"inline-block",maxHeight:"30px",marginLeft:"10px",marginRight:"15px"},img:{width:"35px",top:"22px",position:"absolute",verticalAlign:"middle",border:"0"},background:{position:"absolute",zIndex:"1",height:"100%",width:"100%",display:"block",top:"0",left:"0",backgroundSize:"cover",backgroundPosition:"center center","&:after":{position:"absolute",zIndex:"3",width:"100%",height:"100%",content:'""',display:"block",background:"#000",opacity:".8"}},list:{marginTop:"20px",paddingLeft:"0",paddingTop:"0",paddingBottom:"0",marginBottom:"0",listStyle:"none",position:"unset"},item:{position:"relative",display:"block",textDecoration:"none","&:hover,&:focus,&:visited,&":{color:"#FFF"}},itemLink:Object(d.a)({width:"auto",transition:"all 300ms linear",margin:"10px 15px 0",borderRadius:"3px",position:"relative",display:"block",padding:"10px 15px",backgroundColor:"transparent"},g),itemIcon:{width:"24px",height:"30px",fontSize:"24px",lineHeight:"30px",float:"left",marginRight:"15px",textAlign:"center",verticalAlign:"middle",color:"rgba("+p("#FFF")+", 0.8)"},itemIconRTL:{marginRight:"3px",marginLeft:"15px",float:"right"},itemText:Object(d.a)(Object(d.a)({},g),{},{margin:"0",lineHeight:"30px",fontSize:"14px",color:"#FFF"}),itemTextRTL:{textAlign:"right"},whiteFont:{color:"#FFF"},purple:Object(d.a)(Object(d.a)({backgroundColor:h[0]},j),{},{"&:hover,&:focus":Object(d.a)({backgroundColor:h[0]},j)}),blue:{backgroundColor:x[0],boxShadow:"0 12px 20px -10px rgba("+p(x[0])+",.28), 0 4px 20px 0 rgba("+p("#000")+",.12), 0 7px 8px -5px rgba("+p(x[0])+",.2)","&:hover,&:focus":{backgroundColor:x[0],boxShadow:"0 12px 20px -10px rgba("+p(x[0])+",.28), 0 4px 20px 0 rgba("+p("#000")+",.12), 0 7px 8px -5px rgba("+p(x[0])+",.2)"}},green:{backgroundColor:y[0],boxShadow:"0 12px 20px -10px rgba("+p(y[0])+",.28), 0 4px 20px 0 rgba("+p("#000")+",.12), 0 7px 8px -5px rgba("+p(y[0])+",.2)","&:hover,&:focus":{backgroundColor:y[0],boxShadow:"0 12px 20px -10px rgba("+p(y[0])+",.28), 0 4px 20px 0 rgba("+p("#000")+",.12), 0 7px 8px -5px rgba("+p(y[0])+",.2)"}},orange:{backgroundColor:E[0],boxShadow:"0 12px 20px -10px rgba("+p(E[0])+",.28), 0 4px 20px 0 rgba("+p("#000")+",.12), 0 7px 8px -5px rgba("+p(E[0])+",.2)","&:hover,&:focus":{backgroundColor:E[0],boxShadow:"0 12px 20px -10px rgba("+p(E[0])+",.28), 0 4px 20px 0 rgba("+p("#000")+",.12), 0 7px 8px -5px rgba("+p(E[0])+",.2)"}},red:{backgroundColor:v[0],boxShadow:"0 12px 20px -10px rgba("+p(v[0])+",.28), 0 4px 20px 0 rgba("+p("#000")+",.12), 0 7px 8px -5px rgba("+p(v[0])+",.2)","&:hover,&:focus":{backgroundColor:v[0],boxShadow:"0 12px 20px -10px rgba("+p(v[0])+",.28), 0 4px 20px 0 rgba("+p("#000")+",.12), 0 7px 8px -5px rgba("+p(v[0])+",.2)"}},sidebarWrapper:{position:"relative",height:"calc(100vh - 75px)",overflow:"auto",width:"260px",zIndex:"4",overflowScrolling:"touch"},activePro:Object(le.a)({},e.breakpoints.up("md"),{position:"absolute",width:"100%",bottom:"13px"})}},Nt=Object(m.a)(_t);function Rt(e){var a=Nt();function t(e){return window.location.href.indexOf(e)>-1}var n=e.color,l=e.logo,r=e.image,c=e.logoText,i=e.routes,s=e.lang,u=o.a.createElement(St.a,{className:a.list},i.map((function(l,r){var c;c=V()(Object(le.a)({}," "+a[n],t(l.layout+l.path)));var i=V()(Object(le.a)({}," "+a.whiteFont,t(l.layout+l.path)));return o.a.createElement(kt.b,{to:l.layout+l.path,className:" "+a.item,activeClassName:"active",key:r},o.a.createElement(jt.a,{button:!0,className:a.itemLink+c},"string"===typeof l.icon?o.a.createElement(wt.a,{className:V()(a.itemIcon,i,Object(le.a)({},a.itemIconRTL,e.rtlActive))},l.icon):o.a.createElement(l.icon,{className:V()(a.itemIcon,i,Object(le.a)({},a.itemIconRTL,e.rtlActive))}),o.a.createElement(Ot.a,{primary:l.display[s],className:V()(a.itemText,i,Object(le.a)({},a.itemTextRTL,e.rtlActive)),disableTypography:!0})))}))),m=o.a.createElement("div",{className:a.logo},o.a.createElement("a",{href:"https://nanos.cloud/",className:V()(a.logoLink,Object(le.a)({},a.logoLinkRTL,e.rtlActive)),target:"_blank"},o.a.createElement("div",{className:a.logoImage},o.a.createElement("img",{src:l,alt:"logo",className:a.img})),c));return o.a.createElement("div",null,o.a.createElement(te.a,{mdUp:!0,implementation:"css"},o.a.createElement(Ct.a,{variant:"temporary",anchor:e.rtlActive?"left":"right",open:e.open,classes:{paper:V()(a.drawerPaper,Object(le.a)({},a.drawerPaperRTL,e.rtlActive))},onClose:e.handleDrawerToggle,ModalProps:{keepMounted:!0}},m,o.a.createElement("div",{className:a.sidebarWrapper},o.a.createElement(rt,{lang:s}),u),void 0!==r?o.a.createElement("div",{className:a.background,style:{backgroundImage:"url("+r+")"}}):null)),o.a.createElement(te.a,{smDown:!0,implementation:"css"},o.a.createElement(Ct.a,{anchor:e.rtlActive?"right":"left",variant:"permanent",open:!0,classes:{paper:V()(a.drawerPaper,Object(le.a)({},a.drawerPaperRTL,e.rtlActive))}},m,o.a.createElement("div",{className:a.sidebarWrapper},u),void 0!==r?o.a.createElement("div",{className:a.background,style:{backgroundImage:"url("+r+")"}}):null)))}var Tt=t(591),It=t(26),Dt=t.n(It),At=t(607),Pt=t(64),Ft=t.n(Pt),Bt=t(53),Mt=t.n(Bt),zt=t(174),Wt=t.n(zt),qt=t(21),Ht=t.n(qt),Lt=t(51),Ut=t.n(Lt),Gt=t(176),Vt=t.n(Gt),$t=t(171),Zt=t.n($t),Yt=t(172),Jt=t.n(Yt),Qt=t(175),Kt=t.n(Qt),Xt=["children"],en=Object(m.a)({grid:{margin:"0 -15px !important",width:"unset"}});function an(e){var a=en(),t=e.children,n=Object(L.a)(e,Xt);return o.a.createElement(Ne.a,Object.assign({container:!0},n,{className:a.grid}),t)}var tn={card:{border:"0",marginBottom:"30px",marginTop:"30px",borderRadius:"6px",color:"rgba("+p("#000")+", 0.87)",background:"#FFF",width:"100%",boxShadow:"0 1px 4px 0 rgba("+p("#000")+", 0.14)",position:"relative",display:"flex",flexDirection:"column",minWidth:"0",wordWrap:"break-word",fontSize:".875rem"},cardPlain:{background:"transparent",boxShadow:"none"},cardProfile:{marginTop:"30px",textAlign:"center"},cardChart:{"& p":{marginTop:"0px",paddingTop:"0px"}}},nn=["className","children","plain","profile","chart"],on=Object(m.a)(tn);function ln(e){var a,t=on(),n=e.className,l=e.children,r=e.plain,c=e.profile,i=e.chart,s=Object(L.a)(e,nn),u=V()((a={},Object(le.a)(a,t.card,!0),Object(le.a)(a,t.cardPlain,r),Object(le.a)(a,t.cardProfile,c),Object(le.a)(a,t.cardChart,i),Object(le.a)(a,n,void 0!==n),a));return o.a.createElement("div",Object.assign({className:u},s),l)}var rn={cardHeader:{padding:"0.75rem 1.25rem",marginBottom:"0",borderBottom:"none",background:"transparent",zIndex:"3 !important","&$cardHeaderPlain,&$cardHeaderIcon,&$cardHeaderStats,&$warningCardHeader,&$successCardHeader,&$dangerCardHeader,&$infoCardHeader,&$primaryCardHeader,&$roseCardHeader":{margin:"0 15px",padding:"0",position:"relative",color:"#FFF"},"&:first-child":{borderRadius:"calc(.25rem - 1px) calc(.25rem - 1px) 0 0"},"&$warningCardHeader,&$successCardHeader,&$dangerCardHeader,&$infoCardHeader,&$primaryCardHeader,&$roseCardHeader":{"&:not($cardHeaderIcon)":{borderRadius:"3px",marginTop:"-20px",padding:"15px"}},"&$cardHeaderStats svg":{fontSize:"36px",lineHeight:"56px",textAlign:"center",width:"36px",height:"36px",margin:"10px 10px 4px"},"&$cardHeaderStats i,&$cardHeaderStats .material-icons":{fontSize:"36px",lineHeight:"56px",width:"56px",height:"56px",textAlign:"center",overflow:"unset",marginBottom:"1px"},"&$cardHeaderStats$cardHeaderIcon":{textAlign:"right"}},cardHeaderPlain:{marginLeft:"0px !important",marginRight:"0px !important"},cardHeaderStats:{"& $cardHeaderIcon":{textAlign:"right"},"& h1,& h2,& h3,& h4,& h5,& h6":{margin:"0 !important"}},cardHeaderIcon:{"&$warningCardHeader,&$successCardHeader,&$dangerCardHeader,&$infoCardHeader,&$primaryCardHeader,&$roseCardHeader":{background:"transparent",boxShadow:"none"},"& i,& .material-icons":{width:"33px",height:"33px",textAlign:"center",lineHeight:"33px"},"& svg":{width:"24px",height:"24px",textAlign:"center",lineHeight:"33px",margin:"5px 4px 0px"}},warningCardHeader:{color:"#FFF","&:not($cardHeaderIcon)":Object(d.a)({},T)},successCardHeader:{color:"#FFF","&:not($cardHeaderIcon)":Object(d.a)({},I)},dangerCardHeader:{color:"#FFF","&:not($cardHeaderIcon)":Object(d.a)({},D)},infoCardHeader:{color:"#FFF","&:not($cardHeaderIcon)":Object(d.a)({},A)},primaryCardHeader:{color:"#FFF","&:not($cardHeaderIcon)":Object(d.a)({},P)},roseCardHeader:{color:"#FFF","&:not($cardHeaderIcon)":Object(d.a)({},F)}},cn=["className","children","color","plain","stats","icon"],sn=Object(m.a)(rn);function un(e){var a,t=sn(),n=e.className,l=e.children,r=e.color,c=e.plain,i=e.stats,s=e.icon,u=Object(L.a)(e,cn),m=V()((a={},Object(le.a)(a,t.cardHeader,!0),Object(le.a)(a,t[r+"CardHeader"],r),Object(le.a)(a,t.cardHeaderPlain,c),Object(le.a)(a,t.cardHeaderStats,i),Object(le.a)(a,t.cardHeaderIcon,s),Object(le.a)(a,n,void 0!==n),a));return o.a.createElement("div",Object.assign({className:m},u),l)}var mn={cardBody:{padding:"0.9375rem 20px",flex:"1 1 auto",WebkitBoxFlex:"1",position:"relative"},cardBodyPlain:{paddingLeft:"5px",paddingRight:"5px"},cardBodyProfile:{marginTop:"15px"}},dn=["className","children","plain","profile"],pn=Object(m.a)(mn);function fn(e){var a,t=pn(),n=e.className,l=e.children,r=e.plain,c=e.profile,i=Object(L.a)(e,dn),s=V()((a={},Object(le.a)(a,t.cardBody,!0),Object(le.a)(a,t.cardBodyPlain,r),Object(le.a)(a,t.cardBodyProfile,c),Object(le.a)(a,n,void 0!==n),a));return o.a.createElement("div",Object.assign({className:s},i),l)}var bn=Object(m.a)(z);function gn(e){var a=bn(),t=e.children;return o.a.createElement("div",{className:a.defaultFontStyle+" "+a.infoText},t)}var hn=t(585),En=Object(m.a)(Je);function vn(e){var a=En(),t=e.message,n=e.color,l=e.close,r=e.icon,c=e.place,i=e.open,s=e.rtlActive,u=[],m=V()(Object(le.a)({},a.iconMessage,void 0!==r));return void 0!==l&&(u=[o.a.createElement(K.a,{className:a.iconButton,key:"close","aria-label":"Close",color:"inherit",onClick:function(){return e.closeNotification()}},o.a.createElement(Ye.a,{className:a.close}))]),o.a.createElement(hn.a,{anchorOrigin:{vertical:-1===c.indexOf("t")?"bottom":"top",horizontal:-1!==c.indexOf("l")?"left":-1!==c.indexOf("c")?"center":"right"},open:i,message:o.a.createElement("div",null,void 0!==r?o.a.createElement(e.icon,{className:a.icon}):null,o.a.createElement("span",{className:m},t)),action:u,ContentProps:{classes:{root:a.root+" "+a[n],message:a.message,action:V()(Object(le.a)({},a.actionRTL,s))}}})}var yn=t(586),xn=t(587),kn=t(588),Cn=t(590),Sn=t(589),jn=function(e){return{warningTableHeader:{color:E[0]},primaryTableHeader:{color:h[0]},dangerTableHeader:{color:v[0]},successTableHeader:{color:y[0]},infoTableHeader:{color:x[0]},roseTableHeader:{color:k[0]},grayTableHeader:{color:C[0]},table:{marginBottom:"0",width:"100%",maxWidth:"100%",backgroundColor:"transparent",borderSpacing:"0",borderCollapse:"collapse"},tableHeadCell:Object(d.a)(Object(d.a)({color:"inherit"},g),{},{"&, &$tableCell":{fontSize:"1em"}}),tableCell:Object(d.a)(Object(d.a)({},g),{},{lineHeight:"1.42857143",padding:"12px 8px",verticalAlign:"middle",fontSize:"0.8125rem"}),tableResponsive:{width:"100%",marginTop:e.spacing(3),overflowX:"auto"},tableHeadRow:{height:"56px",color:"inherit",display:"table-row",outline:"none",verticalAlign:"middle"},tableBodyRow:{height:"48px",color:"inherit",display:"table-row",outline:"none",verticalAlign:"middle"}}},On=Object(m.a)(jn);function wn(e){var a=On(),t=e.color,n=e.headers,l=e.rows;return o.a.createElement("div",{className:a.tableResponsive},o.a.createElement(yn.a,{className:a.table},o.a.createElement(xn.a,{className:a[t+"TableHeader"]},o.a.createElement(kn.a,{className:a.tableHeadRow},n.map((function(e,t){return o.a.createElement(Sn.a,{className:a.tableCell+" "+a.tableHeadCell,key:t},e)})))),o.a.createElement(Cn.a,null,l.map((function(e,t){return o.a.createElement(kn.a,{key:t,className:a.tableBodyRow},e.map((function(e,t){return o.a.createElement(Sn.a,{className:a.tableCell,key:t},e)})))})))))}function _n(e){var a,t,n,l=e.label,r=e.icon,c=e.href,i=e.placement,s=e.color,u=e.onClick;return a=i||"top",t=s||"inherit",n=u?o.a.createElement(K.a,{onClick:u,color:t},o.a.createElement(r)):c?o.a.createElement(kt.a,{to:c},o.a.createElement(K.a,{color:t},o.a.createElement(r))):o.a.createElement(K.a,{color:t},o.a.createElement(r)),o.a.createElement(Q.a,{title:l,placement:a},n)}wn.defaultProps={color:"gray"};var Nn={en:{title:"Remove Cell",content:"Are you sure to remove compute cell ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u79fb\u9664\u8d44\u6e90\u8282\u70b9",content:"\u662f\u5426\u79fb\u9664\u8d44\u6e90\u8282\u70b9 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}},Rn=function(e){var a=e.lang,t=e.pool,n=e.cell,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!0),s=Object(H.a)(i,2),u=s[0],m=s[1],d=o.a.useState(""),p=Object(H.a)(d,2),f=p[0],b=p[1],g=Nn[a],h=g.title,E=g.content+n,v=function(e){m(!0),b(e)},y=function(e,a){m(!0),b(""),r(a)},x=[{color:"transparent",label:g.cancel,onClick:function(){b(""),c()}},{color:"info",label:g.confirm,onClick:function(){m(!1),function(e,a,t,n){Va("/compute_pool_cells/"+e+"/"+a,()=>{t(e,a)},t=>{n('remove cell "'+a+'" from pool "'+e+'" fail: '+t)})}(t,n,y,v)}}];return o.a.createElement(Xe,{size:"xs",open:l,prompt:f,title:h,buttons:x,content:E,operatable:u})},Tn={en:{title:"Add Compute Cell To Pool ",name:"Cell Name",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6dfb\u52a0\u8282\u70b9\u5230\u8d44\u6e90\u6c60 ",name:"\u8d44\u6e90\u8282\u70b9\u540d\u79f0",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}},In=function(e){var a={cell:""},t=e.lang,n=e.pool,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!1),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(!0),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(""),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(a),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState([]),O=Object(H.a)(j,2),w=O[0],_=O[1],N=Tn[t],R=N.title,T=function(e){g(!0),y(e)},I=function(){y(""),S(a),m(!1)},D=function(e,a){g(!0),I(),r(a)};o.a.useEffect((function(){if(l&&!u){!function(e,a){Wa("/compute_pool_cells/",e,a)}((function(e){var a=[];if(e.forEach((function(e){var t={label:e.address?e.name+" ("+e.address+")":e.name,value:e.name};a.push(t)})),0===a.length)return T("no unallocated cells available"),void m(!0);_(a),m(!0)}),T)}}),[u,l]);var A,P,F=[{color:"transparent",label:N.cancel,onClick:function(){I(),c()}}];if(u){var B=[{type:"select",label:N.name,onChange:(P="cell",function(e){var a=e.target.value;S((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},P,a))}))}),value:C.cell,options:w,required:!0,oneRow:!0,xs:8}];A=o.a.createElement(Ae,{inputs:B}),F.push({color:"info",label:N.confirm,onClick:function(){g(!1);var e=C.cell;""!==e?function(e,a,t,n){Ha("/compute_pool_cells/"+e+"/"+a,"",()=>{t(e,a)},n)}(n,e,D,T):T("must select a cell")}})}else A=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:l,prompt:v,title:R,buttons:F,content:A,operatable:b})},Dn=t(173),An=t.n(Dn),Pn=t(285),Fn=t.n(Pn),Bn={en:{name:"Name",address:"Address",alive:"Alive",status:"Status",enabled:"Enabled",disabled:"Disabled",online:"Online",offline:"Offline",title:"Current Cell Status",cancel:"Cancel",confirm:"Confirm",attached:"Attached",storage:"Backend Storage",localStorage:"Use local storage"},cn:{name:"\u540d\u79f0",address:"\u5730\u5740",alive:"\u6d3b\u52a8",status:"\u72b6\u6001",enabled:"\u5df2\u542f\u7528",disabled:"\u5df2\u7981\u7528",online:"\u5728\u7ebf",offline:"\u79bb\u7ebf",title:"\u5f53\u524d\u8282\u70b9\u72b6\u6001",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a",attached:"\u5df2\u6302\u8f7d",storage:"\u540e\u7aef\u5b58\u50a8",localStorage:"\u4f7f\u7528\u672c\u5730\u5b58\u50a8"}},Mn=function(e){var a,t,n,l,r,c,i=Object(m.a)(jn)(),s=Object(m.a)(z)(),u=e.lang,d=e.pool,p=e.cell,f=e.open,b=e.onCancel,g=o.a.useState(!1),h=Object(H.a)(g,2),E=h[0],v=h[1],y=o.a.useState(""),x=Object(H.a)(y,2),k=x[0],C=x[1],S=o.a.useState(null),j=Object(H.a)(S,2),O=j[0],w=j[1],_=Bn[u],N=function(e){C(e)};(o.a.useEffect((function(){if(d&&p&&f&&!E){Wa("/compute_pool_cells/"+d+"/"+p,(function(e){w(e),v(!0)}),N)}}),[E,f,d,p]),E)?(O.enabled?(t=_.enabled,n=o.a.createElement(Ft.a,{className:s.successText})):(t=_.disabled,n=o.a.createElement(Mt.a,{className:s.warningText})),O.alive?(l=_.online,r=o.a.createElement(Zt.a,{className:s.successText})):(l=_.offline,r=o.a.createElement(Jt.a,{className:s.warningText})),a=o.a.createElement("div",{className:i.tableResponsive},o.a.createElement(yn.a,{className:i.table},o.a.createElement(Cn.a,null,o.a.createElement(kn.a,{className:i.tableBodyRow},o.a.createElement(Sn.a,{className:i.tableCell},_.name),o.a.createElement(Sn.a,{className:i.tableCell},O.name)),o.a.createElement(kn.a,{className:i.tableBodyRow},o.a.createElement(Sn.a,{className:i.tableCell},_.address),o.a.createElement(Sn.a,{className:i.tableCell},O.address?O.address:"")),o.a.createElement(kn.a,{className:i.tableBodyRow},o.a.createElement(Sn.a,{className:i.tableCell},_.status),o.a.createElement(Sn.a,{className:i.tableCell},o.a.createElement(Q.a,{title:t,placement:"top"},n))),o.a.createElement(kn.a,{className:i.tableBodyRow},o.a.createElement(Sn.a,{className:i.tableCell},_.alive),o.a.createElement(Sn.a,{className:i.tableCell},o.a.createElement(Q.a,{title:l,placement:"top"},r))),O.storage?O.storage.map((function(e){var a;return a=e.attached?o.a.createElement(Q.a,{title:_.attached,placement:"top"},o.a.createElement(An.a,{className:s.successText})):o.a.createElement(Q.a,{title:e.error,placement:"top"},o.a.createElement(Fn.a,{className:s.warningText})),o.a.createElement(kn.a,{className:i.tableBodyRow},o.a.createElement(Sn.a,{className:i.tableCell},e.name),o.a.createElement(Sn.a,{className:i.tableCell},a))})):o.a.createElement(kn.a,{className:i.tableBodyRow},o.a.createElement(Sn.a,{className:i.tableCell},_.storage),o.a.createElement(Sn.a,{className:i.tableCell},_.localStorage)))))):a=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return c=k&&""!==k?o.a.createElement(Ve,{xs:12},o.a.createElement(Ke,{message:k,color:"danger"})):o.a.createElement(Ve,{xs:12}),o.a.createElement(Pe.a,{open:f,"aria-labelledby":_.title,maxWidth:"sm",fullWidth:!0},o.a.createElement(Me.a,null,_.title),o.a.createElement(Be.a,null,o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:12},a),c)),o.a.createElement(Fe.a,null,o.a.createElement(ue,{onClick:function(){C(""),w(null),v(!1),b()},color:"transparent",autoFocus:!0},_.cancel)))},zn={en:{title:"Migrate All Instance",sourcePool:"Source Pool",sourceCell:"Source Cell",targetCell:"Target Cell",offline:"Offline",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u8fc1\u79fb\u6240\u6709\u4e91\u4e3b\u673a\u5b9e\u4f8b",sourcePool:"\u6e90\u8d44\u6e90\u6c60",sourceCell:"\u6e90\u8282\u70b9",targetCell:"\u76ee\u6807\u8282\u70b9",offline:"\u79bb\u7ebf",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function Wn(e){var a={targetCell:""},t=e.lang,n=e.open,l=e.sourcePool,r=e.sourceCell,c=e.onSuccess,i=e.onCancel,s=o.a.useState(!1),u=Object(H.a)(s,2),m=u[0],p=u[1],f=o.a.useState(!0),b=Object(H.a)(f,2),g=b[0],h=b[1],E=o.a.useState(""),v=Object(H.a)(E,2),y=v[0],x=v[1],k=o.a.useState(!1),C=Object(H.a)(k,2),S=C[0],j=C[1],O=o.a.useState(a),w=Object(H.a)(O,2),_=w[0],N=w[1],R=o.a.useState({cells:[]}),T=Object(H.a)(R,2),I=T[0],D=T[1],A=zn[t],P=A.title,F=o.a.useCallback((function(e){S&&(h(!0),x(e))}),[S]),B=function(){x(""),N(a),p(!1)},M=function(e,a){S&&(h(!0),B(),c(e,a))};o.a.useEffect((function(){if(n){j(!0);return pa(l,(function(e){if(S){var a=[];e.forEach((function(e){var t;e.name!==r&&(t=e.alive?e.name+"("+e.address+")":e.name+"("+A.offline+")",a.push({label:t,value:e.name,disabled:!e.alive}))})),0!==a.length?(D({cells:a}),p(!0)):F("no target cell available")}}),F),function(){j(!1)}}}),[S,n,l,r,F,A.offline]);var z,W,q=[{color:"transparent",label:A.cancel,onClick:function(){B(),i()}}];if(m){var L=[{type:"text",label:A.sourcePool,value:l,disabled:!0,oneRow:!0,xs:12,sm:8},{type:"text",label:A.sourceCell,value:r,disabled:!0,oneRow:!0,xs:12,sm:8},{type:"select",label:A.targetCell,onChange:(W="targetCell",function(e){if(S){var a=e.target.value;N((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},W,a))}))}}),value:_.targetCell,options:I.cells,required:!0,oneRow:!0,xs:12,sm:10}];z=o.a.createElement(Ae,{inputs:L}),q.push({color:"info",label:A.confirm,onClick:function(){h(!1);var e=_.targetCell;""!==e?function(e,a,t,n,o){Ha("/migrations/",{source_pool:e,source_cell:a,target_cell:t},()=>{n(a,t)},o)}(l,r,e,M,F):F("select a target cell")}})}else z=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:n,prompt:y,title:P,buttons:q,content:z,operatable:g})}var qn={en:{title:"Change Storage Path",current:"Current Storage Path",location:"New Storage Location",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u5b58\u50a8\u8def\u5f84",current:"\u5f53\u524d\u5b58\u50a8\u8def\u5f84",location:"\u65b0\u5b58\u50a8\u8def\u5f84",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function Hn(e){var a={current:"",path:""},t=e.lang,n=e.open,l=e.pool,r=e.cell,c=e.onSuccess,i=e.onCancel,s=o.a.useState(!1),u=Object(H.a)(s,2),m=u[0],p=u[1],f=o.a.useState(!0),b=Object(H.a)(f,2),g=b[0],h=b[1],E=o.a.useState(""),v=Object(H.a)(E,2),y=v[0],x=v[1],k=o.a.useState(!1),C=Object(H.a)(k,2),S=C[0],j=C[1],O=o.a.useState(a),w=Object(H.a)(O,2),_=w[0],N=w[1],R=qn[t],T=R.title,I=o.a.useCallback((function(e){S&&(h(!0),x(e))}),[S]),D=function(){x(""),N(a),p(!1)},A=function(){S&&(h(!0),D(),c(_.path,r,l))};o.a.useEffect((function(){if(n&&l&&r){j(!0);return function(e,a,t,n){Wa("/compute_cell_status/"+e+"/"+a+"/storages/",t,n)}(l,r,(function(e){if(S)if(e.system)if(0!==e.system.length){var a=e.system[0];a?(N({current:a,path:""}),p(!0)):I("no system paths available")}else I("no system paths available");else I("no system paths available")}),I),function(){j(!1)}}}),[S,n,l,r,I]);var P,F,B=[{color:"transparent",label:R.cancel,onClick:function(){D(),i()}}];if(m){var M=[{type:"text",label:R.current,value:_.current,disabled:!0,oneRow:!0,xs:12,sm:8},{type:"text",label:R.location,onChange:(F="path",function(e){if(S){var a=e.target.value;N((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},F,a))}))}}),value:_.path,required:!0,oneRow:!0,xs:12,sm:8}];P=o.a.createElement(Ae,{inputs:M}),B.push({color:"info",label:R.confirm,onClick:function(){var e=_.path;""!==e?(x(""),h(!1),function(e,a,t,n,o){Ga("/compute_cell_status/"+e+"/"+a+"/storages/",{default:t},()=>{n(a,e)},o)}(l,r,e,A,I)):I("input a new location")}})}else P=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:n,prompt:y,title:T,buttons:B,content:P,operatable:g})}var Ln=Object(d.a)(Object(d.a)({},z),{},{cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),Un=Object(m.a)(Ln),Gn={en:{addButton:"Add Compute Cell",tableTitle:"Compute Cells",name:"Name",address:"Address",alive:"Alive",status:"Status",operates:"Operates",noResource:"No compute cell available",computePools:"Compute Pools",enable:"Enable",disable:"Disable",enabled:"Enabled",disabled:"Disabled",instances:"Instances",detail:"Detail",remove:"Remove",migrate:"Migrate",online:"Online",offline:"Offline",changeStorage:"Change Storage Path"},cn:{addButton:"\u6dfb\u52a0\u8d44\u6e90\u8282\u70b9",tableTitle:"\u8ba1\u7b97\u8d44\u6e90\u8282\u70b9",name:"\u540d\u79f0",address:"\u5730\u5740",alive:"\u6d3b\u52a8",status:"\u72b6\u6001",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u8ba1\u7b97\u8d44\u6e90\u8282\u70b9",computePools:"\u8ba1\u7b97\u8d44\u6e90\u6c60",enable:"\u542f\u7528",disable:"\u7981\u7528",enabled:"\u5df2\u542f\u7528",disabled:"\u5df2\u7981\u7528",instances:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",detail:"\u8be6\u60c5",remove:"\u79fb\u9664",migrate:"\u8fc1\u79fb",online:"\u5728\u7ebf",offline:"\u79bb\u7ebf",changeStorage:"\u4fee\u6539\u5b58\u50a8\u8def\u5f84"}};function Vn(e){var a,t=Un(),n=e.lang,l=Gn[n],r=o.a.useState(!1),c=Object(H.a)(r,2),i=c[0],s=c[1],m=o.a.useState(null),p=Object(H.a)(m,2),f=p[0],b=p[1],g=Object(u.g)(),h=new URLSearchParams(g.search).get("pool"),E=o.a.useState(!1),v=Object(H.a)(E,2),y=v[0],x=v[1],k=o.a.useState(!1),C=Object(H.a)(k,2),S=C[0],j=C[1],O=o.a.useState(!1),w=Object(H.a)(O,2),_=w[0],N=w[1],R=o.a.useState(!1),T=Object(H.a)(R,2),I=T[0],D=T[1],A=o.a.useState(!1),P=Object(H.a)(A,2),F=P[0],B=P[1],M=o.a.useState(""),z=Object(H.a)(M,2),W=z[0],q=z[1],L=o.a.useState("warning"),U=Object(H.a)(L,2),G=U[0],V=U[1],Z=o.a.useState(""),Y=Object(H.a)(Z,2),J=Y[0],K=Y[1],ee=function(){K("")},ae=o.a.useCallback((function(e){if(i){V("warning"),K(e),setTimeout(ee,3e3)}}),[V,K,i]),te=o.a.useCallback((function(){if(i){pa(h,b,(function(e){i&&ae(e)}))}}),[h,ae,i]),ne=function(e){V("info"),K(e),Ma(e),setTimeout(ee,3e3)},oe=function(){N(!1)},le=function(){x(!1)},re=function(){D(!1)},ce=function(){B(!1)};if(o.a.useEffect((function(){s(!0),te();var e=setInterval((function(){i&&te()}),5e3);return function(){s(!1),clearInterval(e)}}),[te,i]),null===f)a=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});else if(0===f.length)a=o.a.createElement($.a,{display:"flex",justifyContent:"center"},o.a.createElement(gn,null,l.noResource));else{var ie=[];f.forEach((function(e){var a,n,r=[{label:l.instances,icon:Wt.a,href:"/admin/instances/range/?pool="+h+"&cell="+e.name},{onClick:function(a){return t=e.name,j(!0),void q(t);var t},icon:Ut.a,label:l.detail},{onClick:function(a){return t=e.name,B(!0),void q(t);var t},icon:Kt.a,label:l.changeStorage},{onClick:function(a){return t=e.name,N(!0),void q(t);var t},icon:Ht.a,label:l.remove},{onClick:function(a){return t=e.name,D(!0),void q(t);var t},icon:Vt.a,label:l.migrate}],c=e.name,s=e.address,u=e.enabled,m=e.alive;u?(a=o.a.createElement(Q.a,{title:l.enabled,placement:"top"},o.a.createElement(Ft.a,{className:t.successText})),r=[{label:l.disable,icon:Mt.a,onClick:function(){fa(h,c,!1,(function(){i&&te()}),ae)}}].concat(r)):(a=o.a.createElement(Q.a,{title:l.disabled,placement:"top"},o.a.createElement(Mt.a,{className:t.warningText})),r=[{label:l.enable,icon:Ft.a,onClick:function(){fa(h,c,!0,(function(){i&&te()}),ae)}}].concat(r));n=m?o.a.createElement(Q.a,{title:l.online,placement:"top"},o.a.createElement(Zt.a,{className:t.successText})):o.a.createElement(Q.a,{title:l.offline,placement:"top"},o.a.createElement(Jt.a,{className:t.warningText}));var p=r.map((function(e,a){return o.a.createElement(_n,Object(d.a)(Object(d.a)({},e),{},{key:a}))})),f=[c,s,n,a,p];ie.push(f)})),a=o.a.createElement(wn,{color:"primary",headers:[l.name,l.address,l.alive,l.status,l.operates],rows:ie})}return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement(At.a,{separator:"\u203a","aria-label":"breadcrumb"},o.a.createElement(kt.a,{to:"/admin/compute_pools/"},l.computePools),o.a.createElement(X.a,{color:"textPrimary"},h))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12},o.a.createElement(an,null,o.a.createElement(Ve,{xs:3},o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){x(!0)}},o.a.createElement(Dt.a,null),l.addButton)))),o.a.createElement(Ve,{xs:12},o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",{className:t.cardTitleWhite},l.tableTitle)),o.a.createElement(fn,null,a))),o.a.createElement(vn,{place:"tr",color:G,message:J,open:""!==J,closeNotification:ee,close:!0}),o.a.createElement(In,{lang:n,open:y,pool:h,onSuccess:function(e){le(),ne("cell "+e+" added to "+h),te()},onCancel:le}),o.a.createElement(Mn,{lang:n,open:S,pool:h,cell:W,onCancel:function(){j(!1)}}),o.a.createElement(Rn,{lang:n,open:_,pool:h,cell:W,onSuccess:function(e){oe(),ne("cell "+e+" removed from "+h),te()},onCancel:oe}),o.a.createElement(Wn,{lang:n,open:I,sourcePool:h,sourceCell:W,onSuccess:function(){re(),te()},onCancel:re}),o.a.createElement(Hn,{lang:n,open:F,pool:h,cell:W,onSuccess:function(e,a){ce(),ne("storage path of  "+a+" changed to "+e)},onCancel:ce}))}var $n=t(112),Zn=t.n($n),Yn=t(90);function Jn(e){var a=e.series,t=[],n=[],l=[];a.forEach((function(e){t.push(e.label),n.push(e.value),l.push(e.color)}));var r={labels:t,datasets:[{data:n,backgroundColor:l,borderWidth:1,hoverBorderWidth:0}]};return o.a.createElement(Yn.Pie,{data:r,options:{cutoutPercentage:5,legend:{display:!1},layout:{padding:{left:0,right:0,top:0,bottom:0}}}})}var Qn={successText:{color:y[0]},upArrowCardCategory:{width:"16px",height:"16px"},stats:{color:C[0],display:"inline-flex",fontSize:"12px",lineHeight:"22px","& svg":{top:"4px",width:"16px",height:"16px",position:"relative",marginRight:"3px",marginLeft:"3px"},"& .fab,& .fas,& .far,& .fal,& .material-icons":{top:"4px",fontSize:"16px",position:"relative",marginRight:"3px",marginLeft:"3px"}},cardCategory:{color:C[0],margin:"0",fontSize:"14px",marginTop:"0",paddingTop:"10px",marginBottom:"0"},cardCategoryWhite:{color:"rgba("+p("#FFF")+",.62)",margin:"0",fontSize:"14px",marginTop:"0",marginBottom:"0"},cardTitle:{color:C[2],marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:C[1],fontWeight:"400",lineHeight:"1"}},cardTitleWhite:{color:"#FFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:C[1],fontWeight:"400",lineHeight:"1"}}};function Kn(e){var a=e.title,t=e.series,n=e.displayValue,l=0,r=Object(d.a)({},Qn);t.forEach((function(e,a){l+=e.value;var t="series-"+a.toString();r[t]=Object(d.a)(Object(d.a)({},Qn.cardCategory),{},{color:e.color})})),r.topDivider={borderTop:"1px solid "+C[10]};var c,i=Object(m.a)(r)();return c=n?n(l):l.toString(),o.a.createElement(ln,{chart:!0},o.a.createElement(un,null,o.a.createElement(X.a,{className:i.cardCategory},a+": "+c),o.a.createElement(Jn,{series:t})),o.a.createElement(fn,null,o.a.createElement($.a,{m:0,p:2,className:i.topDivider},o.a.createElement($.a,{display:"flex"},t.map((function(e,a){var t;return t=n?n(e.value):e.value.toString(),o.a.createElement($.a,{m:1,key:e.label},o.a.createElement(X.a,{component:"span",className:i["series-"+a.toString()]},e.label),o.a.createElement(X.a,{component:"span"},": "+t))}))))))}function Xn(e){var a,t=e.series,n=e.minTickStep,l=e.displayValue,r=e.light,c=e.maxTicks,i=e.maxValue,s=c||10,u=r?"#000":"#FFF",m=t[0].data.length,d=new Array(m).fill(""),p=!!i;a=p?i:n;var f=[];t.forEach((function(e){p||e.data.forEach((function(e){a=Math.max(a,e)})),f.push({data:e.data,label:e.label,pointBackgroundColor:e.color,pointBorderColor:e.color,pointRadius:5,borderColor:u,borderWidth:4,lineTension:0})}));var b,g={labels:d,datasets:f};b=a<=s*n?n:Math.ceil(a/s/n)*n;var h={scales:{xAxes:[{gridLines:{drawBorder:!1,lineWidth:0,zeroLineColor:u,zeroLineWidth:2}}],yAxes:[{gridLines:{borderDash:[2,4],color:u,zeroLineColor:u,zeroLineWidth:2,drawBorder:!1},ticks:{stepSize:b,fontColor:u,suggestedMax:a,suggestedMin:0,callback:function(e){return l?l(e):e.toString()}}}]},legend:{display:!1}};return o.a.createElement(Yn.Line,{data:g,options:h})}function eo(e){var a=e.title,t=e.color,n=e.series,l=e.displayValue,r=e.minTickStep,c=e.maxValue,i=Object(d.a)(Object(d.a)({},Qn),{},{topDivider:{borderTop:"1px solid "+C[10]}}),s=[];n.forEach((function(e){var a,t=e.data,n=e.label,o=t[t.length-1];a=l?l(o):o.toString(),s.push(n+": "+a)}));var u=Object(m.a)(i)();return o.a.createElement(ln,{chart:!0},o.a.createElement(un,{color:t},o.a.createElement(Xn,{series:n,minTickStep:r,displayValue:l,maxValue:c})),o.a.createElement(fn,null,o.a.createElement(X.a,{variant:"h5",component:"div",className:u.cardTitle},a),o.a.createElement($.a,{m:0,p:2,className:u.topDivider},o.a.createElement(X.a,{component:"span",className:u.cardCategory},s.join(" / ")+" / "+c.toString()))))}function ao(e){var a=e.series,t=e.minTickStep,n=e.displayValue,l=e.light,r=e.maxTicks,c=r||10,i=l?"#000":"#FFF",s=a[0].data.length,u=new Array(s).fill(""),m=t,d=[];a.forEach((function(e){d.push({data:e.data,label:e.label,backgroundColor:e.color,barPercentage:.6,borderColor:i,borderWidth:1,stack:"default"})}));for(var p=0;p<s;p++){for(var f=0,b=0;b<a.length;b++)f+=a[b].data[p];m=Math.max(m,f)}var g,h={labels:u,datasets:d};g=m<=c*t?t:Math.ceil(m/c/t)*t;var E={scales:{xAxes:[{display:!1}],yAxes:[{stacked:!0,gridLines:{borderDash:[2,4],color:i,zeroLineColor:i,zeroLineWidth:2},ticks:{stepSize:g,fontColor:i,suggestedMax:m,suggestedMin:0,callback:function(e){return n?n(e):e.toString()}}}]},legend:{display:!1}};return o.a.createElement(Yn.Bar,{data:h,options:E})}function to(e){var a=e.title,t=e.color,n=e.series,l=e.displayValue,r=e.minTickStep,c=Object(d.a)(Object(d.a)({},Qn),{},{topDivider:{borderTop:"1px solid "+C[10]}}),i=[];n.forEach((function(e){var a,t=e.data,n=e.label,o=t[t.length-1];a=l?l(o):o.toString(),i.push(n+": "+a)}));var s=Object(m.a)(c)();return o.a.createElement(ln,{chart:!0},o.a.createElement(un,{color:t},o.a.createElement(ao,{series:n,minTickStep:r,displayValue:l})),o.a.createElement(fn,null,o.a.createElement(X.a,{variant:"h5",component:"div",className:s.cardTitle},a),o.a.createElement($.a,{m:0,p:2,className:s.topDivider},o.a.createElement(X.a,{component:"span",className:s.cardCategory},i.join(" / ")))))}function no(e){var a=e.series,t=e.minTickStep,n=e.displayValue,l=e.light,r=e.maxTicks,c=r||10,i=l?"#000":"#FFF",s=a[0].data.length,u=new Array(s).fill(""),m=t,d=[];a.forEach((function(e){e.data.forEach((function(e){m=Math.max(m,e)})),d.push({data:e.data,label:e.label,backgroundColor:e.color,barPercentage:.6,borderColor:i,borderWidth:1})}));var p,f={labels:u,datasets:d};p=m<=c*t?t:Math.ceil(m/c/t)*t;var b={scales:{xAxes:[{display:!1}],yAxes:[{gridLines:{borderDash:[2,4],color:i,zeroLineColor:i,zeroLineWidth:2},ticks:{stepSize:p,fontColor:i,suggestedMax:m,suggestedMin:0,callback:function(e){return n?n(e):e.toString()}}}]},legend:{display:!1}};return o.a.createElement(Yn.Bar,{data:f,options:b})}function oo(e){var a=e.title,t=e.color,n=e.series,l=e.displayValue,r=e.minTickStep,c=Object(d.a)(Object(d.a)({},Qn),{},{topDivider:{borderTop:"1px solid "+C[10]}}),i=[];n.forEach((function(e){var a,t=e.data,n=e.label,o=t[t.length-1];a=l?l(o):o.toString(),i.push(n+": "+a)}));var s=Object(m.a)(c)();return o.a.createElement(ln,{chart:!0},o.a.createElement(un,{color:t},o.a.createElement(no,{series:n,minTickStep:r,displayValue:l})),o.a.createElement(fn,null,o.a.createElement(X.a,{variant:"h5",component:"div",className:s.cardTitle},a),o.a.createElement($.a,{m:0,p:2,className:s.topDivider},o.a.createElement(X.a,{component:"span",className:s.cardCategory},i.join(" / ")))))}var lo={en:{allButton:"Show All Compute Pools",pools:"Compute Pools Summary",cells:"Compute Cells Summary",instances:"Instances Summary",disks:"Storage Space Summary",coreUsage:"Core Usage",memoryUsage:"Memory Usage",ioUsage:"IO Usage",enabled:"Enabled",disabled:"Disabled",online:"Online",offline:"Offline",stopped:"Stopped",running:"Running",lost:"Lost",migrate:"Migrating",used:"Used",available:"Available",coresUsed:"Cores Used",network:"Network Usage",diskIO:"Disk IO",receive:"Receive",send:"Send",write:"Write",read:"Read"},cn:{allButton:"\u67e5\u770b\u6240\u6709\u8d44\u6e90\u6c60",pools:"\u8ba1\u7b97\u8d44\u6e90\u6c60\u603b\u6570",cells:"\u8d44\u6e90\u8282\u70b9\u603b\u6570",instances:"\u4e91\u4e3b\u673a\u603b\u6570",disks:"\u603b\u78c1\u76d8\u7a7a\u95f4",coreUsage:"CPU\u7528\u91cf",memoryUsage:"\u5185\u5b58\u7528\u91cf",ioUsage:"IO\u541e\u5410\u91cf",enabled:"\u542f\u7528",disabled:"\u7981\u7528",online:"\u5728\u7ebf",offline:"\u79bb\u7ebf",stopped:"\u505c\u6b62",running:"\u8fd0\u884c",lost:"\u5931\u8054",migrate:"\u8fc1\u79fb\u4e2d",used:"\u5df2\u7528",available:"\u53ef\u7528",coresUsed:"\u6838\u5fc3\u5df2\u5360\u7528",network:"\u7f51\u7edc\u6d41\u91cf",diskIO:"\u78c1\u76d8IO",receive:"\u63a5\u6536",send:"\u53d1\u9001",write:"\u5199\u78c1\u76d8",read:"\u8bfb\u78c1\u76d8"}},ro=x[0],co=y[0],io=E[0],so=h[0],uo=C[2];function mo(e){var a,t=o.a.useState(null),n=Object(H.a)(t,2),l=n[0],r=n[1],c=e.lang,i=lo[c];if(o.a.useEffect((function(){var e=!0,a=new Array(6).fill({current:0,max:0}),t=new Array(10).fill({available:0,used:0}),n=new Array(10).fill({receive:0,send:0}),o=new Array(10).fill({write:0,read:0}),l=function(){var e;Wa("/compute_zone_status/",(function(e){a.shift(),a.push({current:ca(e.cpu_usage,2),max:e.max_cpu}),t.shift(),t.push({available:ca(e.available_memory/(1<<20),2),used:ca((e.max_memory-e.available_memory)/(1<<20),2)}),n.shift(),n.push({receive:e.receive_speed,send:e.send_speed}),o.shift(),o.push({write:e.write_speed,read:e.read_speed}),r(Object(d.a)(Object(d.a)({},e),{},{coreRecords:a,memoryRecords:t,networkRecords:n,diskRecords:o}))}),e)};l();var c=setInterval((function(){e&&l()}),2e3);return function(){e=!1,clearInterval(c)}}),[]),null===ta())return oa();if(l){var s=new Date(l.start_time.replace(" ","T")),u=new Date,m=Math.floor((u.getTime()-s.getTime())/1e3),p=Math.floor(m/86400);m-=24*p*3600;var f=Math.floor(m/3600);m-=3600*f;var b,g=Math.floor(m/60);m-=60*g,b="cn"===c?"\u7cfb\u7edf\u542f\u52a8\u65f6\u95f4 "+l.start_time+", \u5df2\u8fd0\u884c "+p+" \u5929 "+f+" \u5c0f\u65f6 "+g+" \u5206 "+m+" \u79d2":"System start at "+l.start_time+", Uptime: "+p+" day, "+f+" hour, "+g+" min, "+m+" secs";var E=o.a.createElement(Ve,{xs:12,key:"uptime"},o.a.createElement($.a,{align:"center"},o.a.createElement(X.a,{component:"span"},b))),v=Object(H.a)(l.pools,2),k=v[0],S=v[1],j=[{value:k,label:i.disabled,color:uo},{value:S,label:i.enabled,color:ro}],O=o.a.createElement(Ve,{xs:12,sm:4,md:3,key:"pool"},o.a.createElement(Kn,{title:i.pools,series:j})),w=Object(H.a)(l.cells,2),_=w[0],N=w[1],R=[{value:_,label:i.offline,color:uo},{value:N,label:i.online,color:co}],T=o.a.createElement(Ve,{xs:12,sm:4,md:3,key:"cell"},o.a.createElement(Kn,{title:i.cells,series:R})),I=Object(H.a)(l.instances,4),D=I[0],A=I[1],P=I[2],F=I[3],B=[{value:D,label:i.stopped,color:uo},{value:A,label:i.running,color:ro},{value:P,label:i.lost,color:io},{value:F,label:i.migrate,color:so}],M=o.a.createElement(Ve,{xs:12,sm:4,md:3,key:"instance"},o.a.createElement(Kn,{title:i.instances,series:B})),z=ca(l.available_disk/(1<<30),2),W=ca((l.max_disk-l.available_disk)/(1<<30),2),q=[{value:z,label:i.available,color:ro},{value:W,label:i.used,color:so}],L=o.a.createElement(Ve,{xs:12,sm:4,md:3,key:"disks"},o.a.createElement(Kn,{title:i.disks,series:q,displayValue:function(e){return Number.isInteger(e)?e.toString()+" GB":e.toFixed(2)+" GB"}})),U={label:i.coresUsed,color:"#FFF",data:[]},G=0;l.coreRecords.forEach((function(e){U.data.push(e.current),G=Math.max(G,e.max)}));var V=o.a.createElement(Ve,{xs:12,sm:6,md:4,key:"cores-usage"},o.a.createElement(eo,{title:i.coreUsage,series:[U],color:"success",minTickStep:1,maxValue:G})),Z={label:i.used,color:C[4],data:[]},Y={label:i.available,color:y[1],data:[]};l.memoryRecords.forEach((function(e){Z.data.push(e.used),Y.data.push(e.available)}));var J=o.a.createElement(Ve,{xs:12,sm:6,md:4,key:"memory-usage"},o.a.createElement(to,{title:i.memoryUsage,series:[Z,Y],color:"info",minTickStep:1024,displayValue:function(e){return 0===e?"0":e>=1024?0===e%1024?(e/1024).toString()+" GB":(e/1024).toFixed(2)+" GB":e.toString()+" MB"}})),Q={label:i.receive,color:x[3],data:[]},K={label:i.send,color:h[1],data:[]};l.networkRecords.forEach((function(e){Q.data.push(e.receive),K.data.push(e.send)}));var ee=[Q,K],ae=o.a.createElement(Ve,{xs:12,sm:6,md:4,key:"network-usage"},o.a.createElement(oo,{title:i.network,series:ee,displayValue:ra,minTickStep:1<<20,color:"warning"})),te={label:i.write,color:y[1],data:[]},ne={label:i.read,color:x[3],data:[]};l.diskRecords.forEach((function(e){te.data.push(e.write),ne.data.push(e.read)}));var oe=[te,ne];a=[E,O,T,M,L,V,J,ae,o.a.createElement(Ve,{xs:12,sm:6,md:4,key:"io-usage"},o.a.createElement(oo,{title:i.diskIO,series:oe,displayValue:ra,minTickStep:1<<20,color:"rose"}))]}else a=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement(an,null,o.a.createElement(Ve,{xs:6,sm:4,md:3},o.a.createElement(kt.a,{to:"/admin/dashboard/pools/"},o.a.createElement(ue,{size:"sm",color:"info",round:!0},o.a.createElement(Zn.a,null),i.allButton))))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),a)}function po(e){var a=e.title,t=e.series,n=e.valueName,l=e.colorName,r=e.labelName,c=e.displayValue,i=e.baseClass,s={};s.title=i?Object(d.a)({},i):{},t.forEach((function(e,a){var t="series-"+a.toString();s[t]=i?Object(d.a)(Object(d.a)({},i),{},{color:e[l]}):{color:e[l]}}));var u=Object(m.a)(s)();return o.a.createElement($.a,{display:"flex"},o.a.createElement($.a,{m:1},o.a.createElement(X.a,{component:"span",className:u.title},a)),t.map((function(e,a){var t,l=e[n],i=e[r],s="series-"+a.toString();return t=c?c(l):l.toString(),o.a.createElement($.a,{m:1,key:i},o.a.createElement(X.a,{component:"span",className:u[s]},i),o.a.createElement(X.a,{component:"span"},": "+t))})))}var fo={en:{viewButton:"View Cell Status",zone:"Zone Status",title:"Compute Pool",pools:"All Compute Pools",cells:"Compute Cells",instances:"Instances",disks:"Storage(GB)",memory:"Memory",ioUsage:"IO Usage",enabled:"Enabled",disabled:"Disabled",online:"Online ",offline:"Offline ",stopped:"Stopped ",running:"Running ",lost:"Lost ",migrate:"Migrating ",used:"Used ",available:"Available ",coresUsed:"Cores Used",throughput:"Throughput",receive:"Receive ",send:"Send ",write:"Write ",read:"Read "},cn:{viewButton:"\u67e5\u770b\u8282\u70b9\u72b6\u6001",zone:"\u5168\u57df\u72b6\u6001",title:"\u8ba1\u7b97\u8d44\u6e90\u6c60",pools:"\u6240\u6709\u8ba1\u7b97\u8d44\u6e90\u6c60",cells:"\u8d44\u6e90\u8282\u70b9",instances:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",disks:"\u78c1\u76d8\u7a7a\u95f4(GB)",memory:"\u5185\u5b58",ioUsage:"IO\u541e\u5410\u91cf",enabled:"\u542f\u7528",disabled:"\u7981\u7528",online:"\u5728\u7ebf",offline:"\u79bb\u7ebf",stopped:"\u505c\u6b62",running:"\u8fd0\u884c",lost:"\u5931\u8054",migrate:"\u8fc1\u79fb\u4e2d",used:"\u5df2\u7528",available:"\u53ef\u7528",coresUsed:"\u6838\u5fc3\u5df2\u5360\u7528",throughput:"\u541e\u5410\u91cf",receive:"\u63a5\u6536",send:"\u53d1\u9001",write:"\u5199\u5165",read:"\u8bfb\u53d6"}},bo=x[0],go=y[0],ho=E[0],Eo=h[0],vo=C[2],yo={borderRadius:"3px",marginTop:"-20px",padding:"15px"},xo=Object(d.a)(Object(d.a)(Object(d.a)({},z),Qn),{},{cardWithDivider:{borderTop:"1px solid "+C[10]},coresChart:Object(d.a)(Object(d.a)({},yo),{},{background:y[0]}),memoryChart:Object(d.a)(Object(d.a)({},yo),{},{background:x[0]}),networkChart:Object(d.a)(Object(d.a)({},yo),{},{background:E[0]}),diskChart:Object(d.a)(Object(d.a)({},yo),{},{background:k[0]})}),ko=Object(m.a)(xo),Co=function(e){var a=e.lang,t=e.status,n=e.poolName,l=ko(),r=fo[a],c=r.title+": "+n,i=Object(H.a)(t.cells,2),s=i[0],u=i[1],m=[{value:s,label:r.offline,color:vo},{value:u,label:r.online,color:go}],d=o.a.createElement(po,{key:"cells-labels",title:r.cells,series:m,valueName:"value",colorName:"color",labelName:"label",baseClass:Qn.cardCategory}),p=Object(H.a)(t.instances,4),f=p[0],b=p[1],g=p[2],E=p[3],v=[{value:f,label:r.stopped,color:vo},{value:b,label:r.running,color:bo},{value:g,label:r.lost,color:ho},{value:E,label:r.migrate,color:Eo}],k=o.a.createElement(po,{key:"instances-labels",title:r.instances,series:v,valueName:"value",colorName:"color",labelName:"label",baseClass:Qn.cardCategory}),S=ca(t.available_disk/(1<<30),2),j=ca((t.max_disk-t.available_disk)/(1<<30),2),O=[{value:S,label:r.available,color:bo},{value:j,label:r.used,color:Eo}],w=o.a.createElement(po,{key:"storage-labels",title:r.disks,series:O,valueName:"value",colorName:"color",labelName:"label",baseClass:Qn.cardCategory}),_={label:r.coresUsed,color:"#FFF",data:[]},N=0;t.coreRecords.forEach((function(e){_.data.push(e.current),N=Math.max(N,e.max)}));var R=o.a.createElement(Ve,{xs:12,sm:6,md:3,key:"cores-usage"},o.a.createElement($.a,{m:0,p:0,className:l.coresChart,boxShadow:2},o.a.createElement(Xn,{series:[_],minTickStep:1,maxValue:N}))),T={label:r.used+r.memory,color:C[4],data:[]},I={label:r.available+r.memory,color:y[1],data:[]};t.memoryRecords.forEach((function(e){T.data.push(e.used),I.data.push(e.available)}));var D=o.a.createElement(Ve,{xs:12,sm:6,md:3,key:"memory-usage"},o.a.createElement($.a,{m:0,p:0,className:l.memoryChart,boxShadow:2},o.a.createElement(ao,{series:[T,I],minTickStep:1024,displayValue:function(e){return 0===e?"0":e>=1024?0===e%1024?(e/1024).toString()+" GB":(e/1024).toFixed(2)+" GB":e.toString()+" MB"}}))),A={label:r.receive+r.throughput,color:x[3],data:[]},P={label:r.send+r.throughput,color:h[1],data:[]};t.networkRecords.forEach((function(e){A.data.push(ca(e.receive/(1<<20),2)),P.data.push(ca(e.send/(1<<20),2))}));var F=function(e){return e>=1024?0===e%1024?(e/1024).toString()+" GB/s":(e/1024).toFixed(2)+" GB/s":Number.isInteger(e)?e.toString()+" MB/s":e.toFixed(2)+" MB/s"},B=[A,P],M=o.a.createElement(Ve,{xs:12,sm:6,md:3,key:"network-usage"},o.a.createElement($.a,{m:0,p:0,className:l.networkChart,boxShadow:2},o.a.createElement(no,{series:B,displayValue:F,minTickStep:1}))),z={label:r.write+r.throughput,color:y[1],data:[]},W={label:r.read+r.throughput,color:x[3],data:[]};t.diskRecords.forEach((function(e){z.data.push(ca(e.write/(1<<20),2)),W.data.push(ca(e.read/(1<<20),2))}));var q=[z,W],L=[R,D,M,o.a.createElement(Ve,{xs:12,sm:6,md:3,key:"io-usage"},o.a.createElement($.a,{m:0,p:0,className:l.diskChart,boxShadow:2},o.a.createElement(no,{series:q,displayValue:F,minTickStep:1})))],U=[d,k,w];return o.a.createElement(ln,{chart:!0},o.a.createElement(un,null,o.a.createElement(an,null,L)),o.a.createElement(fn,null,o.a.createElement(X.a,{component:"span",className:l.cardTitle},c),U,o.a.createElement($.a,{m:0,p:2,className:l.cardWithDivider},o.a.createElement(an,null,o.a.createElement(Ve,{xs:6,sm:4,md:3},o.a.createElement(kt.a,{to:"/admin/dashboard/pools/"+n},o.a.createElement(ue,{size:"sm",color:"info",round:!0},o.a.createElement(Zn.a,null),r.viewButton)))))))};function So(e){var a,t=o.a.useState(null),n=Object(H.a)(t,2),l=n[0],r=n[1],c=fo[e.lang];if(o.a.useEffect((function(){var e=!0,a=new Map,t=function(){var e;Wa("/compute_pool_status/",(function(e){e.forEach((function(e){var t,n,o,l,r=e.name;if(a.has(r)){var c=a.get(r);(t=c.coreRecords).shift(),(n=c.memoryRecords).shift(),(o=c.networkRecords).shift(),(l=c.diskRecords).shift()}else t=new Array(4).fill({current:0,max:0}),n=new Array(4).fill({available:0,used:0}),o=new Array(4).fill({receive:0,send:0}),l=new Array(4).fill({write:0,read:0});t.push({current:ca(e.cpu_usage,2),max:e.max_cpu}),n.push({available:ca(e.available_memory/(1<<20),2),used:ca((e.max_memory-e.available_memory)/(1<<20),2)}),o.push({receive:e.receive_speed,send:e.send_speed}),l.push({write:e.write_speed,read:e.read_speed});var i=Object(d.a)(Object(d.a)({},e),{},{coreRecords:t,memoryRecords:n,networkRecords:o,diskRecords:l});a.set(r,i)}));var t=new Map;a.forEach((function(e,a){t.set(a,e)})),r(t)}),e)};t();var n=setInterval((function(){e&&t()}),2e3);return function(){e=!1,clearInterval(n)}}),[]),null===ta())return oa();if(l){var i=[];l.forEach((function(e,a){i.push(a)})),i.sort(),a=[],i.forEach((function(t){var n=l.get(t);a.push(o.a.createElement(Ve,{xs:12,key:t},o.a.createElement(Co,{status:n,lang:e.lang,poolName:t})))}))}else a=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});var s=[o.a.createElement(kt.a,{to:"/admin/dashboard/",key:c.zone},c.zone),o.a.createElement(X.a,{color:"textPrimary",key:c.pools},c.pools)];return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement(At.a,{separator:"\u203a","aria-label":"breadcrumb"},s)),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),a)}var jo=t(287),Oo=t.n(jo),wo=t(286),_o=t.n(wo),No={en:{viewButton:"View Instances",zone:"Zone Status",title:"Compute Cell",pools:"All Compute Pools",cells:"Compute Cells",instances:"Instances",disks:"Storage(GB)",memory:"Memory",ioUsage:"IO Usage",enabled:"Enabled",disabled:"Disabled",online:"Online",offline:"Offline",stopped:"Stopped ",running:"Running ",lost:"Lost ",migrate:"Migrating ",used:"Used ",available:"Available ",coresUsed:"Cores Used",throughput:"Throughput",receive:"Receive ",send:"Send ",write:"Write ",read:"Read "},cn:{viewButton:"\u67e5\u770b\u627f\u8f7d\u4e91\u4e3b\u673a",zone:"\u5168\u57df\u72b6\u6001",title:"\u8ba1\u7b97\u8d44\u6e90\u8282\u70b9",pools:"\u6240\u6709\u8ba1\u7b97\u8d44\u6e90\u6c60",cells:"\u8d44\u6e90\u8282\u70b9",instances:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",disks:"\u78c1\u76d8\u7a7a\u95f4(GB)",memory:"\u5185\u5b58",ioUsage:"IO\u541e\u5410\u91cf",enabled:"\u542f\u7528",disabled:"\u7981\u7528",online:"\u5728\u7ebf",offline:"\u79bb\u7ebf",stopped:"\u505c\u6b62",running:"\u8fd0\u884c",lost:"\u5931\u8054",migrate:"\u8fc1\u79fb\u4e2d",used:"\u5df2\u7528",available:"\u53ef\u7528",coresUsed:"\u6838\u5fc3\u5df2\u5360\u7528",throughput:"\u541e\u5410\u91cf",receive:"\u63a5\u6536",send:"\u53d1\u9001",write:"\u5199\u5165",read:"\u8bfb\u53d6"}},Ro=x[0],To=E[0],Io=h[0],Do=C[2],Ao={borderRadius:"3px",marginTop:"-20px",padding:"15px"},Po=Object(d.a)(Object(d.a)(Object(d.a)({},Qn),z),{},{cardWithDivider:{borderTop:"1px solid "+C[10]},coresChart:Object(d.a)(Object(d.a)({},Ao),{},{background:y[0]}),memoryChart:Object(d.a)(Object(d.a)({},Ao),{},{background:x[0]}),networkChart:Object(d.a)(Object(d.a)({},Ao),{},{background:E[0]}),diskChart:Object(d.a)(Object(d.a)({},Ao),{},{background:k[0]}),disableChart:Object(d.a)(Object(d.a)({},Ao),{},{background:C[5]})}),Fo=Object(m.a)(Po),Bo=function(e){var a,t,n,l,r=e.lang,c=e.status,i=e.cellName,s=e.poolName,u=Fo(),m=No[r];if(a=c.alive?c.enabled?o.a.createElement(X.a,{component:"span",className:u.cardTitle},o.a.createElement(Oo.a,{className:u.successText}),m.title+": "+i+" ( "+m.online+" )"):o.a.createElement(X.a,{component:"span",className:u.cardTitle},o.a.createElement(Mt.a,{className:u.mutedText}),m.title+": "+i+" ( "+m.disabled+" )"):o.a.createElement(X.a,{component:"span",className:u.cardTitle},o.a.createElement(_o.a,{className:u.mutedText}),m.title+": "+i+" ( "+m.offline+" )"),c.alive){l=[o.a.createElement(Ve,{xs:6,sm:4,md:3,key:"view"},o.a.createElement(kt.a,{to:"/admin/instances/?pool="+s+"&cell="+i},o.a.createElement(ue,{size:"sm",color:"info",round:!0},o.a.createElement(Zn.a,null),m.viewButton)))];var d=Object(H.a)(c.instances,4),p=d[0],f=d[1],b=d[2],g=d[3],E=[{value:p,label:m.stopped,color:Do},{value:f,label:m.running,color:Ro},{value:b,label:m.lost,color:To},{value:g,label:m.migrate,color:Io}],v=o.a.createElement(po,{key:"instances-labels",title:m.instances,series:E,valueName:"value",colorName:"color",labelName:"label",baseClass:Qn.cardCategory}),k=ca(c.available_disk/(1<<30),2),S=ca((c.max_disk-c.available_disk)/(1<<30),2),j=[{value:k,label:m.available,color:Ro},{value:S,label:m.used,color:Io}],O=o.a.createElement(po,{key:"storage-labels",title:m.disks,series:j,valueName:"value",colorName:"color",labelName:"label",baseClass:Qn.cardCategory}),w={label:m.coresUsed,color:"#FFF",data:[]},_=0;c.coreRecords.forEach((function(e){w.data.push(e.current),_=Math.max(_,e.max)}));var N=o.a.createElement(Ve,{xs:12,sm:6,md:3,key:"cores-usage"},o.a.createElement($.a,{m:0,p:0,className:u.coresChart,boxShadow:2},o.a.createElement(Xn,{series:[w],minTickStep:1,maxValue:_}))),R={label:m.used+m.memory,color:C[4],data:[]},T={label:m.available+m.memory,color:y[1],data:[]};c.memoryRecords.forEach((function(e){R.data.push(e.used),T.data.push(e.available)}));var I=o.a.createElement(Ve,{xs:12,sm:6,md:3,key:"memory-usage"},o.a.createElement($.a,{m:0,p:0,className:u.memoryChart,boxShadow:2},o.a.createElement(ao,{series:[R,T],minTickStep:1024,displayValue:function(e){return 0===e?"0":e>=1024?0===e%1024?(e/1024).toString()+" GB":(e/1024).toFixed(2)+" GB":e.toString()+" MB"}}))),D={label:m.receive+m.throughput,color:x[3],data:[]},A={label:m.send+m.throughput,color:h[1],data:[]};c.networkRecords.forEach((function(e){D.data.push(ca(e.receive/(1<<20),2)),A.data.push(ca(e.send/(1<<20),2))}));var P=function(e){return e>=1024?0===e%1024?(e/1024).toString()+" GB/s":(e/1024).toFixed(2)+" GB/s":Number.isInteger(e)?e.toString()+" MB/s":e.toFixed(2)+" MB/s"},F=[D,A],B=o.a.createElement(Ve,{xs:12,sm:6,md:3,key:"network-usage"},o.a.createElement($.a,{m:0,p:0,className:u.networkChart,boxShadow:2},o.a.createElement(no,{series:F,displayValue:P,minTickStep:1}))),M={label:m.write+m.throughput,color:y[1],data:[]},z={label:m.read+m.throughput,color:x[3],data:[]};c.diskRecords.forEach((function(e){M.data.push(ca(e.write/(1<<20),2)),z.data.push(ca(e.read/(1<<20),2))}));var W=[M,z];t=[N,I,B,o.a.createElement(Ve,{xs:12,sm:6,md:3,key:"io-usage"},o.a.createElement($.a,{m:0,p:0,className:u.diskChart,boxShadow:2},o.a.createElement(no,{series:W,displayValue:P,minTickStep:1})))],n=[v,O]}else t=["core-usage","memory-usage","disk-io","network-io"].map((function(e){return o.a.createElement(Ve,{xs:12,sm:6,md:3,key:e},o.a.createElement($.a,{m:0,p:0,className:u.disableChart,boxShadow:2}))})),n=[],l=[];return o.a.createElement(ln,{chart:!0},o.a.createElement(un,null,o.a.createElement(an,null,t)),o.a.createElement(fn,null,a,n,o.a.createElement($.a,{m:0,p:2,className:u.cardWithDivider},o.a.createElement(an,null,l))))};function Mo(e){var a,t=e.match.params.pool,n=o.a.useState(null),l=Object(H.a)(n,2),r=l[0],c=l[1],i=No[e.lang];if(o.a.useEffect((function(){var e=!0,a=new Map,n=function(){!function(e,a,t){Wa("/compute_cell_status/"+e,a,t)}(t,(function(e){e.forEach((function(e){var t,n,o,l,r=e.name;if(a.has(r)){var c=a.get(r);(t=c.coreRecords).shift(),(n=c.memoryRecords).shift(),(o=c.networkRecords).shift(),(l=c.diskRecords).shift()}else t=new Array(4).fill({current:0,max:0}),n=new Array(4).fill({available:0,used:0}),o=new Array(4).fill({receive:0,send:0}),l=new Array(4).fill({write:0,read:0});t.push({current:ca(e.cpu_usage,2),max:e.max_cpu}),n.push({available:ca(e.available_memory/(1<<20),2),used:ca((e.max_memory-e.available_memory)/(1<<20),2)}),o.push({receive:e.receive_speed,send:e.send_speed}),l.push({write:e.write_speed,read:e.read_speed});var i=Object(d.a)(Object(d.a)({},e),{},{coreRecords:t,memoryRecords:n,networkRecords:o,diskRecords:l});a.set(r,i)}));var t=new Map;a.forEach((function(e,a){t.set(a,e)})),c(t)}))};n();var o=setInterval((function(){e&&n()}),2e3);return function(){e=!1,clearInterval(o)}}),[t]),null===ta())return oa();if(r){var s=[];r.forEach((function(e,a){s.push(a)})),s.sort(),a=[],s.forEach((function(n){var l=r.get(n);a.push(o.a.createElement(Ve,{xs:12,key:n},o.a.createElement(Bo,{status:l,lang:e.lang,cellName:n,poolName:t})))}))}else a=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});var u=[o.a.createElement(kt.a,{to:"/admin/dashboard/",key:i.zone},i.zone),o.a.createElement(kt.a,{to:"/admin/dashboard/pools/",key:i.pools},i.pools),o.a.createElement(X.a,{color:"textPrimary",key:t},t)];return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement(At.a,{separator:"\u203a","aria-label":"breadcrumb"},u)),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),a)}var zo=t(288),Wo=t.n(zo),qo={en:{title:"Delete Pool",content:"Are you sure to delete compute pool ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u8d44\u6e90\u6c60",content:"\u662f\u5426\u5220\u9664\u8ba1\u7b97\u8d44\u6e90\u6c60 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}},Ho=function(e){var a=e.lang,t=e.pool,n=e.open,l=e.onSuccess,r=e.onCancel,c=o.a.useState(!0),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(""),d=Object(H.a)(m,2),p=d[0],f=d[1],b=qo[a],g=b.title,h=b.content+t,E=function(e){u(!0),f(e)},v=function(e){u(!0),f(""),l(e)},y=[{color:"transparent",label:b.cancel,onClick:function(){f(""),r()}},{color:"info",label:b.confirm,onClick:function(){u(!1),function(e,a,t){Va("/compute_pools/"+e,()=>{a(e)},a=>{t('delete compute pool "'+e+'" fail: '+a)})}(t,v,E)}}];return o.a.createElement(Xe,{size:"xs",open:n,prompt:p,title:g,buttons:y,content:h,operatable:s})},Lo={en:{title:"Create Pool",localStorage:"Use local storage",noAddressPool:"Don't use address pool",name:"Pool Name",storage:"Backend Storage",network:"Address Pool",failover:"Failover",off:"Off",on:"On",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u521b\u5efa\u8d44\u6e90\u6c60",localStorage:"\u4f7f\u7528\u672c\u5730\u5b58\u50a8",noAddressPool:"\u4e0d\u4f7f\u7528\u5730\u5740\u6c60",name:"\u8d44\u6e90\u6c60\u540d\u79f0",storage:"\u540e\u7aef\u5b58\u50a8",network:"\u5730\u5740\u6c60",failover:"\u6545\u969c\u5207\u6362",off:"\u5173\u95ed",on:"\u5f00\u542f",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}},Uo=function(e){var a={name:"",storage:"__default",network:"__default",failover:!1},t=e.lang,n=e.open,l=e.onSuccess,r=e.onCancel,c=o.a.useState(!0),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(""),p=Object(H.a)(m,2),f=p[0],b=p[1],g=o.a.useState(!1),h=Object(H.a)(g,2),E=h[0],v=h[1],y=o.a.useState(a),x=Object(H.a)(y,2),k=x[0],C=x[1],S=o.a.useState({storage:[],network:[]}),j=Object(H.a)(S,2),O=j[0],w=j[1],_=Lo[t],N=_.title,R=function(e){u(!0),b(e)},T=function(){b(""),C(a)},I=function(e){u(!0),T(),l(e)},D=function(e){return function(a){var t=a.target.value;C((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}};o.a.useEffect((function(){if(n&&!E){var e=[{label:_.localStorage,value:"__default"}],a=[{label:_.noAddressPool,value:"__default"}],t=function(t){t.forEach((function(e){var t={label:e.name+" ("+e.allocated+"/"+e.addresses+" allocated via gateway "+e.gateway+")",value:e.name};a.push(t)})),w({storage:e,network:a}),v(!0)};ba((function(a){a.forEach((function(a){var t={label:a.name+" ("+a.type+":"+a.host+")",value:a.name};e.push(t)})),ga(t,R)}),R)}}),[E,n,_.localStorage,_.noAddressPool]);var A,P,F=[{color:"transparent",label:_.cancel,onClick:function(){T(),r()}}];if(E){var B=[{type:"text",label:_.name,onChange:D("name"),value:k.name,required:!0,oneRow:!0,xs:6},{type:"select",label:_.storage,onChange:D("storage"),value:k.storage,options:O.storage,required:!0,oneRow:!0,xs:8},{type:"select",label:_.network,onChange:D("network"),value:k.network,options:O.network,required:!0,oneRow:!0,xs:10},{type:"switch",label:_.failover,onChange:(P="failover",function(e){var a=e.target.checked;C((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},P,a))}))}),value:k.failover,on:_.on,off:_.off,oneRow:!0,xs:6}];A=o.a.createElement(Ae,{inputs:B}),F.push({color:"info",label:_.confirm,onClick:function(){u(!1);var e=k.name;""!==e?function(e,a,t,n,o,l){Ha("/compute_pools/"+e,{storage:a,network:t,failover:n},()=>{o(e)},l)}(e,"__default"===k.storage?"":k.storage,"__default"===k.network?"":k.network,k.failover,I,R):R("must specify pool name")}})}else A=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:n,prompt:f,title:N,buttons:F,content:A,operatable:s})},Go={en:{title:"Modify Pool",localStorage:"Use local storage",noAddressPool:"Don't use address pool",storage:"Backend Storage",network:"Address Pool",failover:"Failover",off:"Off",on:"On",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u8d44\u6e90\u6c60",localStorage:"\u4f7f\u7528\u672c\u5730\u5b58\u50a8",noAddressPool:"\u4e0d\u4f7f\u7528\u5730\u5740\u6c60",storage:"\u540e\u7aef\u5b58\u50a8",network:"\u5730\u5740\u6c60",failover:"\u6545\u969c\u5207\u6362",off:"\u5173\u95ed",on:"\u5f00\u542f",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}},Vo=function(e){var a={storage:"__default",network:"__default",failover:!1},t=e.lang,n=e.pool,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!1),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(!0),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(""),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(!1),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState(a),O=Object(H.a)(j,2),w=O[0],_=O[1],N=o.a.useState({storage:[],network:[]}),R=Object(H.a)(N,2),T=R[0],I=R[1],D=Go[t],A=D.title+" "+n,P=o.a.useCallback((function(e){C&&(g(!0),y(e))}),[C]),F=function(){y(""),_(a),m(!1)},B=function(e){C&&(g(!0),F(),r(e))},M=function(e){return function(a){var t=a.target.value;_((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}};o.a.useEffect((function(){if(n&&l){S(!0);var e=[{label:D.localStorage,value:"__default"}],a=[{label:D.noAddressPool,value:"__default"}],t=function(t){C&&(I({storage:e,network:a}),_({storage:t.storage?t.storage:"__default",network:t.network?t.network:"__default",failover:t.failover}),m(!0))},o=function(e){C&&(e.forEach((function(e){var t={label:e.name+" ("+e.allocated+"/"+e.addresses+" allocated via gateway "+e.gateway+")",value:e.name};a.push(t)})),function(e,a,t){Wa("/compute_pools/"+e,a,t)}(n,t,P))};return ba((function(a){C&&(a.forEach((function(a){var t={label:a.name+" ("+a.type+":"+a.host+")",value:a.name};e.push(t)})),ga(o,P))}),P),function(){S(!1)}}}),[C,l,n,D.localStorage,D.noAddressPool,P]);var z,W,q=[{color:"transparent",label:D.cancel,onClick:function(){F(),c()}}];if(u){var L=[{type:"select",label:D.storage,onChange:M("storage"),value:w.storage,options:T.storage,required:!0,oneRow:!0,xs:8},{type:"select",label:D.network,onChange:M("network"),value:w.network,options:T.network,required:!0,oneRow:!0,xs:10},{type:"switch",label:D.failover,onChange:(W="failover",function(e){var a=e.target.checked;_((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},W,a))}))}),value:w.failover,on:D.on,off:D.off,oneRow:!0,xs:6}];z=o.a.createElement(Ae,{inputs:L}),q.push({color:"info",label:D.confirm,onClick:function(){var e,a;y(""),g(!1),e="__default"===w.storage?"":w.storage,a="__default"===w.network?"":w.network,function(e,a,t,n,o,l){Ga("/compute_pools/"+e,{storage:a,network:t,failover:n},()=>{o(e)},l)}(n,e,a,w.failover,B,P)}})}else z=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:l,prompt:v,title:A,buttons:q,content:z,operatable:b})},$o=Object(d.a)(Object(d.a)({},z),{},{cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),Zo=Object(m.a)($o),Yo={en:{createButton:"Create Compute Pool",tableTitle:"Compute Pools",name:"Name",cells:"Cells",storage:"Backend Storage",network:"Address Pool",failover:"FailOver",status:"Status",operates:"Operates",noPools:"No compute pool available",computePools:"Compute Pools",enable:"Enable",disable:"Disable",enabled:"Enabled",disabled:"Disabled",instances:"Instances",modify:"Modify",delete:"Delete",on:"on",off:"off",localStorage:"Use local storage",noAddressPool:"Don't use address pool"},cn:{createButton:"\u521b\u5efa\u8d44\u6e90\u6c60",tableTitle:"\u8ba1\u7b97\u8d44\u6e90\u6c60",name:"\u540d\u79f0",cells:"\u8d44\u6e90\u8282\u70b9",storage:"\u540e\u7aef\u5b58\u50a8",network:"\u5730\u5740\u6c60",failover:"\u6545\u969c\u5207\u6362",status:"\u72b6\u6001",operates:"\u64cd\u4f5c",noPools:"\u6ca1\u6709\u8ba1\u7b97\u8d44\u6e90\u6c60",computePools:"\u8ba1\u7b97\u8d44\u6e90\u6c60",enable:"\u542f\u7528",disable:"\u7981\u7528",enabled:"\u5df2\u542f\u7528",disabled:"\u5df2\u7981\u7528",instances:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",modify:"\u4fee\u6539",delete:"\u5220\u9664",on:"\u5f00\u542f",off:"\u5173\u95ed",localStorage:"\u4f7f\u7528\u672c\u5730\u5b58\u50a8",noAddressPool:"\u4e0d\u4f7f\u7528\u5730\u5740\u6c60"}};var Jo=t(37),Qo=t.n(Jo),Ko=t(74),Xo=t.n(Ko),el={en:{title:"Delete Address Pool",content:"Are you sure to delete address pool ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u5730\u5740\u8d44\u6e90\u6c60",content:"\u662f\u5426\u5220\u9664\u5730\u5740\u8d44\u6e90\u6c60 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function al(e){var a=e.lang,t=e.pool,n=e.open,l=e.onSuccess,r=e.onCancel,c=o.a.useState(!0),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(""),d=Object(H.a)(m,2),p=d[0],f=d[1],b=el[a],g=b.title,h=function(e){u(!0),f(e)},E=function(e){u(!0),f(""),l(e)},v=b.content+t,y=[{color:"transparent",label:b.cancel,onClick:function(){f(""),r()}},{color:"info",label:b.confirm,onClick:function(){u(!1),function(e,a,t){Va("/address_pools/"+e,()=>{a(e)},t)}(t,E,h)}}];return o.a.createElement(Xe,{size:"xs",open:n,prompt:p,title:g,buttons:y,content:v,operatable:s})}var tl={en:{title:"Create Network Pool",name:"Name",provider:"Provider",interface:"Interface Mode",internal:"Internal",external:"External",both:"Both",gateway:"Gateway",dns1:"DNS1",dns2:"DNS2",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u521b\u5efa\u5730\u5740\u8d44\u6e90\u6c60",name:"\u540d\u79f0",provider:"\u5206\u914d\u6a21\u5f0f",interface:"\u63a5\u53e3\u7c7b\u578b",internal:"\u5185\u90e8",external:"\u5916\u90e8",both:"\u5185\u5916\u90e8",gateway:"\u7f51\u5173\u5730\u5740",dns1:"\u4e3bDNS",dns2:"\u526fDNS",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function nl(e){var a={name:"",gateway:"",provider:"dhcp",mode:"internal",dns1:"",dns2:""},t=e.lang,n=e.open,l=e.onSuccess,r=e.onCancel,c=o.a.useState(!0),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(""),p=Object(H.a)(m,2),f=p[0],b=p[1],g=o.a.useState(a),h=Object(H.a)(g,2),E=h[0],v=h[1],y=tl[t],x=y.title,k=[{label:y.internal,value:"internal"},{label:y.external,value:"external"},{label:y.both,value:"both"}],C=function(e){u(!0),b(e)},S=function(){b(""),v(a)},j=function(e){u(!0),S(),l(e)},O=function(e){return function(a){var t=a.target.value;v((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}},w=[{type:"text",label:y.name,onChange:O("name"),value:E.name,required:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"radio",label:y.provider,onChange:O("provider"),value:E.provider,oneRow:!0,disabled:!0,options:[{label:"DHCP",value:"dhcp"},{label:"Cloud-Init",value:"cloudinit"}],xs:12,sm:8,md:6},{type:"text",label:y.gateway,onChange:O("gateway"),value:E.gateway,required:!0,oneRow:!0,xs:12,sm:10,md:8},{type:"text",label:y.dns1,onChange:O("dns1"),value:E.dns1,required:!0,oneRow:!0,xs:12,sm:10,md:8},{type:"text",label:y.dns2,onChange:O("dns2"),value:E.dns2,oneRow:!0,xs:12,sm:10,md:8},{type:"radio",label:y.interface,onChange:O("mode"),value:E.mode,oneRow:!0,disabled:!0,options:k,xs:12,sm:8,md:6}],_=o.a.createElement(Ae,{inputs:w}),N=[{color:"transparent",label:y.cancel,onClick:function(){S(),r()}},{color:"info",label:y.confirm,onClick:function(){u(!1);var e=new RegExp("^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?).){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");if(E.name)if(E.gateway)if(e.test(E.gateway))if(E.dns1)if(e.test(E.dns1)){var a=[E.dns1];if(E.dns2){if(!e.test(E.dns2))return void C("invalid secondary DNS format");a.push(E.dns2)}!function(e,a,t,n,o,l){Ha("/address_pools/"+e,{gateway:a,provider:t,dns:n},()=>{o(e)},l)}(E.name,E.gateway,E.provider,a,j,C)}else C("invalid primary DNS format");else C("must specify primary DNS");else C("invalid gateway format");else C("must specify gateway");else C("must specify pool name")}}];return o.a.createElement(Xe,{size:"sm",open:n,prompt:f,title:x,buttons:N,content:_,operatable:s})}var ol={en:{title:"Modify Address Pool",name:"Name",gateway:"Gateway",provider:"Provider",interface:"Interface Mode",internal:"Internal",external:"External",both:"Both",dns1:"DNS1",dns2:"DNS2",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u5730\u5740\u8d44\u6e90\u6c60",name:"\u540d\u79f0",gateway:"\u7f51\u5173\u5730\u5740",provider:"\u5206\u914d\u6a21\u5f0f",interface:"\u63a5\u53e3\u7c7b\u578b",internal:"\u5185\u90e8",external:"\u5916\u90e8",both:"\u5185\u5916\u90e8",dns1:"\u4e3bDNS",dns2:"\u526fDNS",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function ll(e){var a={name:"",gateway:"",provider:"dhcp",mode:"internal",dns1:"",dns2:""},t=e.lang,n=e.pool,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!1),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(!0),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(""),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(!1),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState(a),O=Object(H.a)(j,2),w=O[0],_=O[1],N=ol[t],R=N.title+" "+n,T=[{label:N.internal,value:"internal"},{label:N.external,value:"external"},{label:N.both,value:"both"}],I=o.a.useCallback((function(e){C&&(g(!0),y(e))}),[C]),D=function(){y(""),_(a),m(!1)},A=function(e){g(!0),D(),r(e)},P=function(e){return function(a){var t=a.target.value;_((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}};o.a.useEffect((function(){if(n&&l){S(!0);return ha(n,(function(e){var a,t;if(C)if(0!==e.dns.length){1===e.dns.length?(a=e.dns[0],t=""):2===e.dns.length&&(a=e.dns[0],t=e.dns[1]);var n="dhcp";e.provider&&(n=e.provider);var o="internal";e.mode&&(o=e.mode),_({name:e.name,gateway:e.gateway,provider:n,mode:o,dns1:a,dns2:t}),m(!0)}else I("no DNS available for pool "+e)}),I),function(){S(!1)}}}),[C,l,n,I]);var F,B=[{color:"transparent",label:N.cancel,onClick:function(){D(),c()}}];if(u){var M=[{type:"text",label:N.name,value:n,disabled:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"radio",label:N.provider,onChange:P("provider"),value:w.provider,oneRow:!0,disabled:!0,options:[{label:"DHCP",value:"dhcp"},{label:"Cloud-Init",value:"cloudinit"}],xs:12,sm:8,md:6},{type:"text",label:N.gateway,onChange:P("gateway"),value:w.gateway,required:!0,oneRow:!0,xs:12,sm:10,md:8},{type:"text",label:N.dns1,onChange:P("dns1"),value:w.dns1,required:!0,oneRow:!0,xs:12,sm:10,md:8},{type:"text",label:N.dns2,onChange:P("dns2"),value:w.dns2,oneRow:!0,xs:12,sm:10,md:8},{type:"radio",label:N.interface,onChange:P("mode"),value:w.mode,oneRow:!0,disabled:!0,options:T,xs:12,sm:8,md:6}];F=o.a.createElement(Ae,{inputs:M}),B.push({color:"info",label:N.confirm,onClick:function(){g(!1);var e=new RegExp("^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?).){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");if(w.gateway)if(e.test(w.gateway))if(w.dns1)if(e.test(w.dns1)){var a=[w.dns1];if(w.dns2){if(!e.test(w.dns2))return void I("invalid secondary DNS format");a.push(w.dns2)}!function(e,a,t,n,o,l){Ga("/address_pools/"+e,{gateway:a,provider:t,dns:n},()=>{o(e)},l)}(n,w.gateway,w.provider,a,A,I)}else I("invalid primary DNS format");else I("must specify primary DNS");else I("invalid gateway format");else I("must specify gateway")}})}else F=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:l,prompt:v,title:R,buttons:B,content:F,operatable:b})}var rl=Object(m.a)({cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),cl={en:{createButton:"Create Address Pool",tableTitle:"Address Pools",name:"Name",gateway:"Gateway",address:"Total Address",allocated:"Allocated Address",provider:"Provider",operates:"Operates",noResource:"No address pool available",modify:"Modify",delete:"Delete",detail:"Detail"},cn:{createButton:"\u521b\u5efa\u5730\u5740\u6c60",tableTitle:"\u5730\u5740\u8d44\u6e90\u6c60",name:"\u540d\u79f0",gateway:"\u7f51\u5173",address:"\u5730\u5740\u6570\u91cf",allocated:"\u5df2\u5206\u914d\u5730\u5740",provider:"\u5206\u914d\u6a21\u5f0f",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u5730\u5740\u6c60",modify:"\u4fee\u6539",delete:"\u5220\u9664",detail:"\u8be6\u60c5"}};function il(e){var a=rl(),t=o.a.useState(!1),n=Object(H.a)(t,2),l=n[0],r=n[1],c=o.a.useState(null),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(!1),d=Object(H.a)(m,2),p=d[0],f=d[1],b=o.a.useState(!1),g=Object(H.a)(b,2),h=g[0],E=g[1],v=o.a.useState(!1),y=Object(H.a)(v,2),x=y[0],k=y[1],C=o.a.useState(""),S=Object(H.a)(C,2),j=S[0],O=S[1],w=o.a.useState("warning"),_=Object(H.a)(w,2),N=_[0],R=_[1],T=o.a.useState(""),I=Object(H.a)(T,2),D=I[0],A=I[1],P=function(){A("")},F=o.a.useCallback((function(e){if(l){R("warning"),A(e),setTimeout(P,3e3)}}),[R,A,l]),B=o.a.useCallback((function(){if(l){ga(u,(function(e){l&&F(e)}))}}),[F,l]),M=function(e){if(l){R("info"),A(e),Ma(e),setTimeout(P,3e3)}},z=function(){E(!1)},W=function(){k(!1)},q=function(){f(!1)};o.a.useEffect((function(){return r(!0),B(),function(){r(!1)}}),[B]);var L,U=e.lang,G=cl[U];if(null===s)L=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});else if(0===s.length)L=o.a.createElement($.a,{display:"flex",justifyContent:"center"},o.a.createElement(gn,null,G.noResource));else{var V=[];s.forEach((function(e){var a=[{onClick:function(a){return t=e.name,E(!0),void O(t);var t},icon:Qo.a,label:G.modify},{icon:Xo.a,label:G.detail,href:"/admin/address_pools/"+e.name},{onClick:function(a){return t=e.name,k(!0),void O(t);var t},icon:Ht.a,label:G.delete}];V.push(function(e,a){var t=a.map((function(e,a){return o.a.createElement(_n,{label:e.label,icon:e.icon,onClick:e.onClick,href:e.href,key:a})})),n=e.name,l=e.gateway,r=e.provider,c=e.addresses,i=e.allocated,s="DHCP";return"cloudinit"===r&&(s="Cloud-Init"),[n,s,l,c.toString(),i.toString(),t]}(e,a))})),L=o.a.createElement(wn,{color:"primary",headers:[G.name,G.provider,G.gateway,G.address,G.allocated,G.operates],rows:V})}return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12,sm:12,md:12},o.a.createElement(an,null,o.a.createElement(Ve,{xs:3,sm:3,md:3},o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){f(!0)}},o.a.createElement(Dt.a,null),G.createButton)))),o.a.createElement(Ve,{xs:12,sm:12,md:12},o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",{className:a.cardTitleWhite},G.tableTitle)),o.a.createElement(fn,null,L))),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:N,message:D,open:""!==D,closeNotification:P,close:!0})),o.a.createElement(Ve,null,o.a.createElement(nl,{lang:U,open:p,onSuccess:function(e){q(),M("pool "+e+" created"),B()},onCancel:q})),o.a.createElement(Ve,null,o.a.createElement(ll,{lang:U,open:h,pool:j,onSuccess:function(e){z(),M("pool "+e+" modified"),B()},onCancel:z})),o.a.createElement(Ve,null,o.a.createElement(al,{lang:U,open:x,pool:j,onSuccess:function(e){W(),M("pool "+e+" deleted"),B()},onCancel:W})))}var sl=t(52),ul=t.n(sl),ml=t(79),dl=t.n(ml),pl={en:{title:"Remove Address Range",content:"Are you sure to remove address range ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u5730\u5740\u6bb5",content:"\u662f\u5426\u5220\u9664\u5730\u5740\u6bb5 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function fl(e){var a=e.lang,t=e.open,n=e.poolName,l=e.rangeType,r=e.startAddress,c=e.onSuccess,i=e.onCancel,s=o.a.useState(!0),u=Object(H.a)(s,2),m=u[0],d=u[1],p=o.a.useState(""),f=Object(H.a)(p,2),b=f[0],g=f[1],h=pl[a],E=h.title,v=function(e){d(!0),g(e)},y=function(){d(!0),g(""),c(l,r)},x=h.content+r,k=[{color:"transparent",label:h.cancel,onClick:function(){g(""),i()}},{color:"info",label:h.confirm,onClick:function(){d(!1),function(e,a,t,n,o){Va("/address_pools/"+e+"/"+a+"/ranges/"+t,()=>{n(t,e)},o)}(n,l,r,y,v)}}];return o.a.createElement(Xe,{size:"xs",open:t,prompt:b,title:E,buttons:k,content:x,operatable:m})}var bl={en:{title:"Add Address Range",type:"Range Type",internal:"Internal Address",external:"External Address",start:"Start Address",end:"End Address",netmask:"Netmask",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6dfb\u52a0\u5730\u5740\u6bb5",type:"\u7c7b\u578b",internal:"\u5185\u90e8\u5730\u5740\u6bb5",external:"\u5916\u90e8\u5730\u5740\u6bb5",start:"\u8d77\u59cb\u5730\u5740",end:"\u7ed3\u675f\u5730\u5740",netmask:"\u5b50\u7f51\u63a9\u7801",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function gl(e){var a={type:"internal",start:"",end:"",netmask:""},t=e.lang,n=e.poolName,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!0),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(""),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(a),E=Object(H.a)(h,2),v=E[0],y=E[1],x=bl[t],k=x.title,C=function(e){m(!0),g(e)},S=function(){g(""),y(a)},j=function(){m(!0),S(),r(v.type,v.start)},O=function(e){return function(a){var t=a.target.value;y((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}},w=[{type:"radio",label:x.type,onChange:O("type"),value:v.type,options:[{label:x.internal,value:"internal"}],required:!0,oneRow:!0,xs:12},{type:"text",label:x.start,onChange:O("start"),value:v.start,required:!0,oneRow:!0,xs:12,sm:8,md:6},{type:"text",label:x.end,onChange:O("end"),value:v.end,required:!0,oneRow:!0,xs:12,sm:8,md:6},{type:"text",label:x.netmask,onChange:O("netmask"),value:v.netmask,oneRow:!0,xs:12,sm:8,md:6}],_=o.a.createElement(Ae,{inputs:w}),N=[{color:"transparent",label:x.cancel,onClick:function(){S(),c()}},{color:"info",label:x.confirm,onClick:function(){m(!1);var e=new RegExp("^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?).){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$");v.start?e.test(v.start)?v.end?e.test(v.end)?v.netmask?e.test(v.netmask)?function(e,a,t,n,o,l,r){Ha("/address_pools/"+e+"/"+a+"/ranges/"+t,{end:n,netmask:o},()=>{l(t,e)},r)}(n,v.type,v.start,v.end,v.netmask,j,C):C("invalid netmask format"):C("must specify netmask"):C("invalid end address format"):C("must specify end address"):C("invalid start start format"):C("must specify start address")}}];return o.a.createElement(Xe,{size:"sm",open:l,prompt:b,title:k,buttons:N,content:_,operatable:u})}var hl=Object(m.a)({cardCategoryWhite:{"&,& a,& a:hover,& a:focus":{color:"rgba(255,255,255,.62)",margin:"0",fontSize:"14px",marginTop:"0",marginBottom:"0"},"& a,& a:hover,& a:focus":{color:"#FFFFFF"}},cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),El={en:{back:"Back",createButton:"Add Address Range",tableTitle:"Address Pool Status",internal:"Internal Address Range",allocated:"Allocated Address",startAddress:"Start Address",endAddress:"End Address",netmask:"Netmask",noInternalRange:"No internal range available",noAllocated:"No address allocated",operates:"Operates",allocatedAddress:"Allocated Address",instance:"Instance",detail:"Detail",remove:"Remove"},cn:{back:"\u8fd4\u56de",createButton:"\u6dfb\u52a0\u5730\u5740\u6bb5",tableTitle:"\u5730\u5740\u8d44\u6e90\u6c60\u72b6\u6001",internal:"\u5185\u90e8\u5730\u5740\u6bb5",allocated:"\u5df2\u5206\u914d\u5730\u5740",startAddress:"\u5f00\u59cb\u5730\u5740",endAddress:"\u7ed3\u675f\u5730\u5740",netmask:"\u5b50\u7f51\u63a9\u7801",noInternalRange:"\u6ca1\u6709\u5185\u90e8\u5730\u5740\u6bb5",noAllocated:"\u672a\u5206\u914d\u5730\u5740",operates:"\u64cd\u4f5c",allocatedAddress:"\u5df2\u5206\u914d\u5730\u5740",instance:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",detail:"\u8be6\u60c5",remove:"\u5220\u9664"}};function vl(e){var a,t=e.lang,n=El[t],l=e.match.params.pool,r=hl(),c=o.a.useState(!1),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(null),p=Object(H.a)(m,2),f=p[0],b=p[1],g=o.a.useState(!1),h=Object(H.a)(g,2),E=h[0],v=h[1],y=o.a.useState(!1),x=Object(H.a)(y,2),k=x[0],C=x[1],S=o.a.useState({type:"",start:""}),j=Object(H.a)(S,2),O=j[0],w=j[1],_=o.a.useState("warning"),N=Object(H.a)(_,2),R=N[0],T=N[1],I=o.a.useState(""),D=Object(H.a)(I,2),A=D[0],P=D[1],F=function(){P("")},B=o.a.useCallback((function(e){if(s){T("warning"),P(e),setTimeout(F,3e3)}}),[T,P,s]),M=o.a.useCallback((function(){if(s){ha(l,b,(function(e){s&&B(e)}))}}),[B,l,s]),z=function(e){if(s){T("info"),P(e),Ma(e),setTimeout(F,3e3)}},W=function(){C(!1)},q=function(){v(!1)};if(o.a.useEffect((function(){return u(!0),M(),function(){u(!1)}}),[M]),null===f)a=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});else{var L;if(f.ranges&&0!==f.ranges.length){var U=[];f.ranges.forEach((function(e){var a=[{icon:Xo.a,label:n.detail,href:"/admin/address_pools/"+l+"/internal/ranges/"+e.start},{onClick:function(a){return t="internal",n=e.start,C(!0),void w({type:t,start:n});var t,n},icon:Ht.a,label:n.remove}];U.push(function(e,a){var t=a.map((function(e,a){return o.a.createElement(_n,Object(d.a)(Object(d.a)({},e),{},{key:a}))}));return[e.start,e.end,e.netmask,t]}(e,a))})),L=o.a.createElement(wn,{color:"primary",headers:[n.startAddress,n.endAddress,n.netmask,n.operates],rows:U})}else L=o.a.createElement($.a,{display:"flex",justifyContent:"center"},o.a.createElement(gn,null,n.noInternalRange));var G,V=o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",{className:r.cardTitleWhite},n.internal)),o.a.createElement(fn,null,L));f.allocated&&0!==f.allocated.length?(U=[],f.allocated.forEach((function(e){var a=[{icon:dl.a,label:n.detail,href:"/admin/instances/details/"+e.instance}];U.push(function(e,a){var t=e.address,n=e.instance,l=a.map((function(e,a){return o.a.createElement(_n,Object(d.a)(Object(d.a)({},e),{},{key:a}))}));return[t,n].concat(l)}(e,a))})),G=o.a.createElement(wn,{color:"primary",headers:[n.allocatedAddress,n.instance,""],rows:U})):G=o.a.createElement($.a,{display:"flex",justifyContent:"center"},o.a.createElement(gn,null,n.noAllocated));var Z=o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",{className:r.cardTitleWhite},n.allocated)),o.a.createElement(fn,null,G));a=o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},V),o.a.createElement(Ve,{xs:12},Z))}var Y=[o.a.createElement(ue,{key:"back",size:"sm",color:"info",round:!0,onClick:function(){e.history.goBack()}},o.a.createElement(ul.a,null),n.back),o.a.createElement(ue,{key:"add",size:"sm",color:"info",round:!0,onClick:function(){v(!0)}},o.a.createElement(Dt.a,null),n.createButton)];return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{display:"flex"},Y.map((function(e,a){return o.a.createElement($.a,{key:a,m:1},e)})))),o.a.createElement(Ve,{xs:12},a),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:R,message:A,open:""!==A,closeNotification:F,close:!0})),o.a.createElement(Ve,null,o.a.createElement(gl,{lang:t,poolName:l,open:E,onSuccess:function(e,a){q(),z('range "'+a+'" of '+e+" address added"),M()},onCancel:q})),o.a.createElement(Ve,null,o.a.createElement(fl,{lang:t,open:k,poolName:l,rangeType:O.type,startAddress:O.start,onSuccess:function(e,a){W(),z('range "'+a+'" of '+e+" address removed"),M()},onCancel:W})))}var yl=Object(m.a)({cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),xl={en:{back:"Back",internal:"Address Range Status",allocated:"Allocated Address",startAddress:"Start Address",endAddress:"End Address",netmask:"Netmask",noAllocated:"No address allocated",allocatedAddress:"Allocated Address",instance:"Instance",detail:"Detail"},cn:{back:"\u8fd4\u56de",internal:"\u5730\u5740\u6bb5\u72b6\u6001",allocated:"\u5df2\u5206\u914d\u5730\u5740",startAddress:"\u5f00\u59cb\u5730\u5740",endAddress:"\u7ed3\u675f\u5730\u5740",netmask:"\u5b50\u7f51\u63a9\u7801",noAllocated:"\u672a\u5206\u914d\u5730\u5740",allocatedAddress:"\u5df2\u5206\u914d\u5730\u5740",instance:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",detail:"\u8be6\u60c5"}};function kl(e){var a,t=e.match.params.pool,n=e.match.params.type,l=e.match.params.start,r=e.lang,c=xl[r],i=yl(),s=o.a.useState(!1),u=Object(H.a)(s,2),m=u[0],p=u[1],f=o.a.useState(null),b=Object(H.a)(f,2),g=b[0],h=b[1],E=o.a.useState("warning"),v=Object(H.a)(E,2),y=v[0],x=v[1],k=o.a.useState(""),C=Object(H.a)(k,2),S=C[0],j=C[1],O=function(){j("")},w=o.a.useCallback((function(e){if(m){x("warning"),j(e),setTimeout(O,3e3)}}),[x,j,m]),_=o.a.useCallback((function(){if(m){!function(e,a,t,n,o){Wa("/address_pools/"+e+"/"+a+"/ranges/"+t,n,o)}(t,n,l,h,(function(e){m&&w(e)}))}}),[w,t,n,l,m]);if(o.a.useEffect((function(){return p(!0),_(),function(){p(!1)}}),[_]),null===g)a=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});else{var N,R=o.a.createElement(wn,{color:"primary",headers:[c.startAddress,c.endAddress,c.netmask],rows:[[l,g.end,g.netmask]]}),T=o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",{className:i.cardTitleWhite},c.internal)),o.a.createElement(fn,null,R));if(g.allocated&&0!==g.allocated.length){var I=[];g.allocated.forEach((function(e){var a=[{icon:dl.a,label:c.detail,href:"/admin/instances/details/"+e.instance}];I.push(function(e,a){var t=e.address,n=e.instance,l=a.map((function(e,a){return o.a.createElement(_n,Object(d.a)(Object(d.a)({},e),{},{key:a}))}));return[t,n].concat(l)}(e,a))})),N=o.a.createElement(wn,{color:"primary",headers:[c.allocatedAddress,c.instance,""],rows:I})}else N=o.a.createElement($.a,{display:"flex",justifyContent:"center"},o.a.createElement(gn,null,c.noAllocated));var D=o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",{className:i.cardTitleWhite},c.allocated)),o.a.createElement(fn,null,N));a=o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},T),o.a.createElement(Ve,{xs:12},D))}var A=[o.a.createElement(ue,{key:"back",size:"sm",color:"info",round:!0,onClick:function(){e.history.goBack()}},o.a.createElement(ul.a,null),c.back)];return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{display:"flex"},A.map((function(e,a){return o.a.createElement($.a,{key:a,m:1},e)})))),o.a.createElement(Ve,{xs:12},a),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:y,message:S,open:""!==S,closeNotification:O,close:!0})))}var Cl={en:{title:"Delete Storage Pool",content:"Are you sure to delete storage pool ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u5b58\u50a8\u8d44\u6e90\u6c60",content:"\u662f\u5426\u5220\u9664\u5b58\u50a8\u8d44\u6e90\u6c60 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}},Sl=function(e){var a=e.lang,t=e.pool,n=e.open,l=e.onSuccess,r=e.onCancel,c=o.a.useState(!0),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(""),d=Object(H.a)(m,2),p=d[0],f=d[1],b=Cl[a],g=b.title,h=function(e){u(!0),f(e)},E=function(e){u(!0),f(""),l(e)},v=b.content+t,y=[{color:"transparent",label:b.cancel,onClick:function(){f(""),r()}},{color:"info",label:b.confirm,onClick:function(){u(!1),function(e,a,t){Va("/storage_pools/"+e,()=>{a(e)},t)}(t,E,h)}}];return o.a.createElement(Xe,{size:"xs",open:n,prompt:p,title:g,buttons:y,content:v,operatable:s})},jl={en:{title:"Create Storage Pool",name:"Name",type:"Type",host:"Host",target:"Target",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u521b\u5efa\u5b58\u50a8\u8d44\u6e90\u6c60",name:"\u540d\u79f0",type:"\u7c7b\u578b",host:"\u4e3b\u673a",target:"\u76ee\u6807",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Ol(e){var a={name:"",type:"nfs",host:"",target:""},t=e.lang,n=e.open,l=e.onSuccess,r=e.onCancel,c=o.a.useState(!0),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(""),p=Object(H.a)(m,2),f=p[0],b=p[1],g=o.a.useState(a),h=Object(H.a)(g,2),E=h[0],v=h[1],y={type:[{value:"nfs",label:"NFS"}]},x=jl[t],k=function(e){u(!0),b(e)},C=function(){b(""),v(a)},S=function(e){C(),u(!0),l(e)},j=function(e){return function(a){var t=a.target.value;v((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}},O=[{type:"text",label:x.name,onChange:j("name"),value:E.name,required:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"select",label:x.type,onChange:j("type"),value:E.type,options:y.type,required:!0,oneRow:!0,xs:12,sm:8,md:6},{type:"text",label:x.host,onChange:j("host"),value:E.host,required:!0,oneRow:!0,xs:12,sm:10,md:8},{type:"text",label:x.target,onChange:j("target"),value:E.target,required:!0,oneRow:!0,xs:12,sm:10,md:8}],w=o.a.createElement(Ae,{inputs:O}),_=[{color:"transparent",label:x.cancel,onClick:function(){C(),r()}},{color:"info",label:x.confirm,onClick:function(){if(u(!1),E.name)if(E.type)if(E.host)if(E.target){var e=new RegExp("^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?).){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]).)+([A-Za-z]|[A-Za-z][A-Za-z0-9-]*[A-Za-z0-9])$"),a=new RegExp("^(/[^/ ]*)+/?$");e.test(E.host)?a.test(E.target)?function(e,a,t,n,o,l){Ha("/storage_pools/"+e,{type:a,host:t,target:n},()=>{o(e)},l)}(E.name,E.type,E.host,E.target,S,k):k("invalid target format"):k("invalid host format")}else k("must specify storage target");else k("must specify storage host");else k("must specify storage type");else k("must specify storage name")}}];return o.a.createElement(Xe,{size:"sm",open:n,prompt:f,title:x.title,buttons:_,content:w,operatable:s})}var wl={en:{title:"Modify Storage Pool",name:"Name",type:"Type",host:"Host",target:"Target",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u5b58\u50a8\u8d44\u6e90\u6c60",name:"\u540d\u79f0",type:"\u7c7b\u578b",host:"\u4e3b\u673a",target:"\u76ee\u6807",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function _l(e){var a,t={name:"",type:"",host:"",target:""},n=e.lang,l=e.pool,r=e.open,c=e.onSuccess,i=e.onCancel,s=o.a.useState(!1),u=Object(H.a)(s,2),m=u[0],p=u[1],f=o.a.useState(!0),b=Object(H.a)(f,2),g=b[0],h=b[1],E=o.a.useState(""),v=Object(H.a)(E,2),y=v[0],x=v[1],k=o.a.useState(!1),C=Object(H.a)(k,2),S=C[0],j=C[1],O=o.a.useState(t),w=Object(H.a)(O,2),_=w[0],N=w[1],R={type:[{value:"nfs",label:"NFS"}]},T=wl[n],I=o.a.useCallback((function(e){S&&(h(!0),x(e))}),[S]),D=function(){x(""),N(t),p(!1)},A=function(e){S&&(D(),h(!0),c(e))},P=function(e){return function(a){var t=a.target.value;N((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}};if(o.a.useEffect((function(){if(l&&r){j(!0);return function(e,a,t){Wa("/storage_pools/"+e,a,t)}(l,(function(e){S&&(N({type:e.type,host:e.host,target:e.target}),p(!0))}),I),function(){j(!1)}}}),[m,r,l,S,I]),m){var F=[{type:"text",label:T.name,value:l,disabled:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"select",label:T.type,onChange:P("type"),value:_.type,options:R.type,required:!0,oneRow:!0,xs:12,sm:8,md:6},{type:"text",label:T.host,onChange:P("host"),value:_.host,required:!0,oneRow:!0,xs:12,sm:10,md:8},{type:"text",label:T.target,onChange:P("target"),value:_.target,required:!0,oneRow:!0,xs:12,sm:10,md:8}];a=o.a.createElement(Ae,{inputs:F})}else a=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});var B=[{color:"transparent",label:T.cancel,onClick:function(){D(),i()}},{color:"info",label:T.confirm,onClick:function(){if(h(!1),_.type)if(_.host)if(_.target){var e=new RegExp("^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?).){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]).)+([A-Za-z]|[A-Za-z][A-Za-z0-9-]*[A-Za-z0-9])$"),a=new RegExp("^(/[^/ ]*)+/?$");e.test(_.host)?a.test(_.target)?function(e,a,t,n,o,l){Ga("/storage_pools/"+e,{type:a,host:t,target:n},()=>{o(e)},l)}(l,_.type,_.host,_.target,A,I):I("invalid target format"):I("invalid host format")}else I("must specify storage target");else I("must specify storage host");else I("must specify storage type")}}],M=T.title+" "+l;return o.a.createElement(Xe,{size:"sm",open:r,prompt:y,title:M,buttons:B,content:a,operatable:g})}var Nl=Object(m.a)({cardCategoryWhite:{"&,& a,& a:hover,& a:focus":{color:"rgba(255,255,255,.62)",margin:"0",fontSize:"14px",marginTop:"0",marginBottom:"0"},"& a,& a:hover,& a:focus":{color:"#FFFFFF"}},cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),Rl={en:{createButton:"Create Storage Pool",tableTitle:"Storage Pools",name:"Name",type:"Type",host:"Host",target:"Target",operates:"Operates",noResource:"No storage pool available",modify:"Modify",delete:"Delete"},cn:{createButton:"\u521b\u5efa\u5b58\u50a8\u8d44\u6e90\u6c60",tableTitle:"\u5b58\u50a8\u8d44\u6e90\u6c60",name:"\u540d\u79f0",type:"\u7c7b\u578b",host:"\u4e3b\u673a",target:"\u76ee\u6807",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u5b58\u50a8\u8d44\u6e90\u6c60",modify:"\u4fee\u6539",delete:"\u5220\u9664"}};var Tl=t(177),Il=t.n(Tl),Dl=t(178),Al=t.n(Dl),Pl=t(613),Fl={en:{title:"Delete Media Image",content:"Are you sure to delete media image ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u5149\u76d8\u955c\u50cf",content:"\u662f\u5426\u5220\u9664\u5149\u76d8\u955c\u50cf ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Bl(e){var a=e.lang,t=e.imageID,n=e.open,l=e.onSuccess,r=e.onCancel,c=o.a.useState(!0),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(""),d=Object(H.a)(m,2),p=d[0],f=d[1],b=Fl[a],g=b.title,h=b.content+t,E=function(e){u(!0),f(e)},v=function(){u(!0),f(""),l(t)},y=[{color:"transparent",label:b.cancel,onClick:function(){f(""),r()}},{color:"info",label:b.confirm,onClick:function(){u(!1),wa(t,v,E)}}];return o.a.createElement(Xe,{size:"xs",open:n,prompt:p,title:g,buttons:y,content:h,operatable:s})}var Ml=t(592);const zl={en:{title:"Upload New Image",name:"Image Name",description:"Description",tags:"Tags",file:"Image File",choose:"Choose File",cancel:"Cancel",confirm:"Upload"},cn:{title:"\u4e0a\u4f20\u65b0\u955c\u50cf",name:"\u955c\u50cf\u540d\u79f0",description:"\u63cf\u8ff0",tags:"\u6807\u7b7e",file:"\u955c\u50cf\u6587\u4ef6",choose:"\u6d4f\u89c8\u6587\u4ef6",cancel:"\u53d6\u6d88",confirm:"\u4e0a\u4f20"}};function Wl(e){const a={name:"",description:"",tags:new Map,file:null},{lang:t,open:n,onSuccess:l,onCancel:r}=e,[c,i]=o.a.useState(!1),[s,u]=o.a.useState(!1),[m,d]=o.a.useState(0),[p,f]=o.a.useState(!0),[b,g]=o.a.useState(""),[h,E]=o.a.useState(!1),[v,y]=o.a.useState(a),[x,k]=o.a.useState({tags:[]}),C=zl[t],S=C.title,j=o.a.useCallback(e=>{h&&(f(!0),g(e))},[h]),O=()=>{g(""),y(a),i(!1),u(!1),d(0)},w=e=>{h&&(f(!0),O(),l(e))},_=e=>{h&&d(e)},N=()=>{g(""),f(!1);const e=v.name;if(""===e)return void j("must specify image name");const a=v.description;if(""===a)return void j("desciption required");if(!v.tags)return void j("image tags required");var t=[];if(v.tags.forEach((e,a)=>{e&&t.push(a)}),0===t.length)return void j("image tags required");if(!v.file)return void j("must specify upload file");!function(e,a,t,n,o){var l=ta();if(null===l)return void o("session expired");Ha("/media_images/",{name:e,description:a,tags:t,owner:l.user,group:l.group},e=>{n(e.id)},o)}(e,a,t,a=>{if(!h)return;const t=()=>{j("new image "+e+" deleted")},n=a=>{j("delete new image "+e+" fail: "+a)};u(!0),function(e,a,t,n,o){Ya("/media_images/"+e+"/file/","image",a,t,()=>{n(e)},o)}(a,v.file,_,w,()=>{wa(a,t,n)})},j)},R=e=>a=>{if(h){var t=a.target.value;y(a=>({...a,[e]:t}))}},T=e=>a=>{if(h){var t=a.target.checked;y(a=>({...a,tags:a.tags.set(e,t)}))}};o.a.useEffect(()=>{if(!n)return;E(!0);var e=[];return[["linux","Linux"],["windows","Windows"],["centos","Centos"],["ubuntu","Ubuntu"],["64bit","64Bit"],["32bit","32Bit"]].forEach(a=>{e.push({label:a[1],value:a[0]})}),k({tags:e}),i(!0),()=>{E(!1)}},[n]);var I=[{color:"transparent",label:C.cancel,onClick:()=>{O(),r()}}];let D;if(c)if(s)D=o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ne.a,{item:!0,xs:12},o.a.createElement(Ml.a,{variant:"determinate",value:m})),o.a.createElement(Ne.a,{item:!0,xs:12},o.a.createElement(X.a,{align:"center"},m.toFixed(2)+"%")));else{const e=[{type:"text",label:C.name,value:v.name,onChange:R("name"),required:!0,oneRow:!0,xs:8},{type:"textarea",label:C.description,value:v.description,onChange:R("description"),required:!0,oneRow:!0,rows:4,xs:12},{type:"checkbox",label:C.tags,onChange:T,value:v.tags,options:x.tags,required:!0,oneRow:!0,xs:10},{type:"file",label:C.file,onChange:(A="file",e=>{if(h){g("");var a=e.target.files[0];if(e.preventDefault(),a&&a.name&&"iso"===a.name.split(".").pop().toLowerCase())return void y(e=>({...e,[A]:a}));g("en"===t?"invalid file, only iso file supported":"\u65e0\u6548\u6587\u4ef6\uff0c\u4ec5\u652f\u6301iso\u683c\u5f0f")}}),required:!0,oneRow:!0,xs:12}];D=o.a.createElement(Ae,{inputs:e}),I.push({color:"info",label:C.confirm,onClick:N})}else D=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});var A;return o.a.createElement(Xe,{size:"sm",open:n,prompt:b,hideBackdrop:!0,title:S,buttons:I,content:D,operatable:p})}var ql={en:{title:"Modify Media Image",name:"Image Name",description:"Description",tags:"Tags",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u955c\u50cf\u4fe1\u606f",name:"\u955c\u50cf\u540d\u79f0",description:"\u63cf\u8ff0",tags:"\u6807\u7b7e",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function Hl(e){var a={name:"",description:"",tags:new Map},t=e.lang,n=e.imageID,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!1),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(!0),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(""),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(!1),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState(a),O=Object(H.a)(j,2),w=O[0],_=O[1],N=o.a.useState({tags:[]}),R=Object(H.a)(N,2),T=R[0],I=R[1],D=ql[t],A=D.title,P=o.a.useCallback((function(e){C&&(g(!0),y(e))}),[C]),F=function(){y(""),_(a),m(!1)},B=function(e){C&&(g(!0),F(),r(e))},M=function(e){return function(a){if(C){var t=a.target.value;_((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}}};o.a.useEffect((function(){if(l){var e=[["linux","Linux"],["windows","Windows"],["centos","Centos"],["ubuntu","Ubuntu"],["64bit","64Bit"],["32bit","32Bit"]];S(!0);return function(e,a,t){Wa("/media_images/"+e,a,t)}(n,(function(a){if(C){var t=[];e.forEach((function(e){t.push({label:e[1],value:e[0]})})),I({tags:t});var n=new Map;a.tags&&a.tags.forEach((function(e){n.set(e,!0)})),_({name:a.name,description:a.description,tags:n}),m(!0)}}),P),function(){S(!1)}}}),[l,n,C,P]);var z,W=[{color:"transparent",label:D.cancel,onClick:function(){F(),c()}}];if(u){var q=[{type:"text",label:D.name,value:w.name,onChange:M("name"),required:!0,oneRow:!0,xs:8},{type:"textarea",label:D.description,value:w.description,onChange:M("description"),required:!0,oneRow:!0,rows:4,xs:12},{type:"checkbox",label:D.tags,onChange:function(e){return function(a){if(C){var t=a.target.checked;_((function(a){return Object(d.a)(Object(d.a)({},a),{},{tags:a.tags.set(e,t)})}))}}},value:w.tags,options:T.tags,required:!0,oneRow:!0,xs:10}];z=o.a.createElement(Ae,{inputs:q}),W.push({color:"info",label:D.confirm,onClick:function(){g(!1);var e=w.name;if(""!==e){var a=w.description;if(""!==a)if(w.tags){var t=[];w.tags.forEach((function(e,a){e&&t.push(a)})),0!==t.length?function(e,a,t,n,o,l){var r=ta();if(null===r)return void l("session expired");Ga("/media_images/"+e,{name:a,description:t,tags:n,owner:r.user,group:r.group},()=>{o(e)},l)}(n,e,a,t,B,P):P("image tags required")}else P("image tags required");else P("desciption required")}else P("must specify image name")}})}else z=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:l,prompt:v,title:A,buttons:W,content:z,operatable:b})}var Ll={en:{title:"Sync Local Media Images",content:"Are you sure to synchronize local media images",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u540c\u6b65\u672c\u5730\u5149\u76d8\u955c\u50cf",content:"\u662f\u5426\u540c\u6b65\u672c\u5730\u5149\u76d8\u955c\u50cf\u6587\u4ef6",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Ul(e){var a=e.lang,t=e.open,n=e.onSuccess,l=e.onCancel,r=o.a.useState(!0),c=Object(H.a)(r,2),i=c[0],s=c[1],u=o.a.useState(""),m=Object(H.a)(u,2),d=m[0],p=m[1],f=Ll[a],b=f.title,g=f.content,h=function(e){s(!0),p(e)},E=function(){s(!0),p(""),n()},v=[{color:"transparent",label:f.cancel,onClick:function(){p(""),l()}},{color:"info",label:f.confirm,onClick:function(){s(!1),function(e,a){var t=ta();null!==t?$a("/media_images/",{owner:t.user,group:t.group},e,a):a("session expired")}(E,h)}}];return o.a.createElement(Xe,{size:"xs",open:t,prompt:d,title:b,buttons:v,content:g,operatable:i})}var Gl={en:{modify:"Modify Info",delete:"Delete Image",createTime:"Created Time",modifyTime:"Modified Time",uploadButton:"Upload New ISO",syncButton:"Synchronize Local Images",noResource:"No images available"},cn:{modify:"\u4fee\u6539\u955c\u50cf\u4fe1\u606f",delete:"\u5220\u9664\u955c\u50cf",createTime:"\u521b\u5efa\u65f6\u95f4",modifyTime:"\u4fee\u6539\u65f6\u95f4",uploadButton:"\u4e0a\u4f20\u65b0\u5149\u76d8\u955c\u50cf",syncButton:"\u540c\u6b65\u672c\u5730\u955c\u50cf\u6587\u4ef6",noResource:"\u6ca1\u6709\u5149\u76d8\u955c\u50cf"}};var Vl=t(289),$l=t.n(Vl),Zl={en:{title:"Delete Disk Image",content:"Are you sure to delete disk image ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u78c1\u76d8\u955c\u50cf",content:"\u662f\u5426\u5220\u9664\u78c1\u76d8\u955c\u50cf ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Yl(e){var a=e.lang,t=e.imageID,n=e.open,l=e.onSuccess,r=e.onCancel,c=o.a.useState(!0),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(""),d=Object(H.a)(m,2),p=d[0],f=d[1],b=Zl[a],g=b.title,h=b.content+t,E=function(e){u(!0),f(e)},v=function(){u(!0),f(""),l(t)},y=[{color:"transparent",label:b.cancel,onClick:function(){f(""),r()}},{color:"info",label:b.confirm,onClick:function(){u(!1),Ta(t,v,E)}}];return o.a.createElement(Xe,{size:"xs",open:n,prompt:p,title:g,buttons:y,content:h,operatable:s})}const Jl={en:{title:"Upload New Image",name:"Image Name",description:"Description",tags:"Tags",file:"Image File",choose:"Choose File",cancel:"Cancel",confirm:"Upload"},cn:{title:"\u4e0a\u4f20\u65b0\u955c\u50cf",name:"\u955c\u50cf\u540d\u79f0",description:"\u63cf\u8ff0",tags:"\u6807\u7b7e",file:"\u955c\u50cf\u6587\u4ef6",choose:"\u6d4f\u89c8\u6587\u4ef6",cancel:"\u53d6\u6d88",confirm:"\u4e0a\u4f20"}};function Ql(e){const a={name:"",description:"",tags:new Map,file:null},{lang:t,open:n,onSuccess:l,onCancel:r}=e,[c,i]=o.a.useState(!1),[s,u]=o.a.useState(!1),[m,d]=o.a.useState(0),[p,f]=o.a.useState(!0),[b,g]=o.a.useState(""),[h,E]=o.a.useState(!1),[v,y]=o.a.useState(a),[x,k]=o.a.useState({tags:[]}),C=Jl[t],S=C.title,j=e=>{g(e)},O=o.a.useCallback(e=>{h&&(f(!0),g(e))},[h]),w=()=>{g(""),y(a),i(!1),u(!1),d(0)},_=e=>{h&&(f(!0),w(),l(e))},N=e=>{h&&d(e)},R=()=>{g(""),f(!1);const e=v.name;if(""===e)return void O("must specify image name");const a=v.description;if(""===a)return void O("desciption required");if(!v.tags)return void O("image tags required");var t=[];if(v.tags.forEach((e,a)=>{e&&t.push(a)}),0===t.length)return void O("image tags required");if(!v.file)return void O("must specify upload file");Ra(e,null,a,t,a=>{if(!h)return;const t=()=>{O("new image "+e+" deleted")},n=a=>{O("delete new image "+e+" fail: "+a)};u(!0),function(e,a,t,n,o){Ya("/disk_images/"+e+"/file/","image",a,t,()=>{n(e)},o)}(a,v.file,N,_,()=>{Ta(a,t,n)})},O)},T=e=>a=>{if(h){var t=a.target.value;y(a=>({...a,[e]:t}))}},I=e=>a=>{if(h){var t=a.target.checked;y(a=>({...a,tags:a.tags.set(e,t)}))}},D=e=>a=>{if(h){g("");var n=a.target.files[0];if(a.preventDefault(),n&&n.name){if("qcow2"===n.name.split(".").pop().toLowerCase())return void y(a=>({...a,[e]:n}))}j("en"===t?"invalid file, only qcow2 file is allowed":"\u65e0\u6548\u6587\u4ef6\uff0c\u53ea\u5141\u8bb8\u652f\u6301qcow2\u683c\u5f0f")}};o.a.useEffect(()=>{if(!n)return;E(!0);var e=[];return[["linux","Linux"],["windows","Windows"],["centos","Centos"],["ubuntu","Ubuntu"],["64bit","64Bit"],["32bit","32Bit"]].forEach(a=>{e.push({label:a[1],value:a[0]})}),k({tags:e}),i(!0),()=>{E(!1)}},[n]);var A=[{color:"transparent",label:C.cancel,onClick:()=>{w(),r()}}];let P;if(c)if(s)P=o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ne.a,{item:!0,xs:12},o.a.createElement(Ml.a,{variant:"determinate",value:m})),o.a.createElement(Ne.a,{item:!0,xs:12},o.a.createElement(X.a,{align:"center"},m.toFixed(2)+"%")));else{const e=[{type:"text",label:C.name,value:v.name,onChange:T("name"),required:!0,oneRow:!0,xs:8},{type:"textarea",label:C.description,value:v.description,onChange:T("description"),required:!0,oneRow:!0,rows:4,xs:12},{type:"checkbox",label:C.tags,onChange:I,value:v.tags,options:x.tags,required:!0,oneRow:!0,xs:10},{type:"file",label:C.file,onChange:D("file"),required:!0,oneRow:!0,xs:12}];P=o.a.createElement(Ae,{inputs:e}),A.push({color:"info",label:C.confirm,onClick:R})}else P=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:n,prompt:b,hideBackdrop:!0,title:S,buttons:A,content:P,operatable:p})}var Kl={en:{title:"Modify Disk Image",name:"Image Name",description:"Description",tags:"Tags",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u955c\u50cf\u4fe1\u606f",name:"\u955c\u50cf\u540d\u79f0",description:"\u63cf\u8ff0",tags:"\u6807\u7b7e",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function Xl(e){var a={name:"",description:"",tags:new Map},t=e.lang,n=e.imageID,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!1),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(!0),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(""),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(!1),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState(a),O=Object(H.a)(j,2),w=O[0],_=O[1],N=o.a.useState({tags:[]}),R=Object(H.a)(N,2),T=R[0],I=R[1],D=Kl[t],A=D.title,P=o.a.useCallback((function(e){C&&(g(!0),y(e))}),[C]),F=function(){y(""),_(a),m(!1)},B=function(e){C&&(g(!0),F(),r(e))},M=function(e){return function(a){if(C){var t=a.target.value;_((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}}};o.a.useEffect((function(){if(l){var e=[["linux","Linux"],["windows","Windows"],["centos","Centos"],["ubuntu","Ubuntu"],["64bit","64Bit"],["32bit","32Bit"]];S(!0);return Na(n,(function(a){if(C){var t=[];e.forEach((function(e){t.push({label:e[1],value:e[0]})})),I({tags:t});var n=new Map;a.tags&&a.tags.forEach((function(e){n.set(e,!0)})),_({name:a.name,description:a.description,tags:n}),m(!0)}}),P),function(){S(!1)}}}),[C,l,n,P]);var z,W=[{color:"transparent",label:D.cancel,onClick:function(){F(),c()}}];if(u){var q=[{type:"text",label:D.name,value:w.name,onChange:M("name"),required:!0,oneRow:!0,xs:8},{type:"textarea",label:D.description,value:w.description,onChange:M("description"),required:!0,oneRow:!0,rows:4,xs:12},{type:"checkbox",label:D.tags,onChange:function(e){return function(a){if(C){var t=a.target.checked;_((function(a){return Object(d.a)(Object(d.a)({},a),{},{tags:a.tags.set(e,t)})}))}}},value:w.tags,options:T.tags,required:!0,oneRow:!0,xs:10}];z=o.a.createElement(Ae,{inputs:q}),W.push({color:"info",label:D.confirm,onClick:function(){g(!1);var e=w.name;if(""!==e){var a=w.description;if(""!==a)if(w.tags){var t=[];w.tags.forEach((function(e,a){e&&t.push(a)})),0!==t.length?function(e,a,t,n,o,l){var r=ta();if(null===r)return void l("session expired");Ga("/disk_images/"+e,{name:a,description:t,tags:n,owner:r.user,group:r.group},()=>{o(e)},l)}(n,e,a,t,B,P):P("image tags required")}else P("image tags required");else P("desciption required")}else P("must specify image name")}})}else z=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:l,prompt:v,title:A,buttons:W,content:z,operatable:b})}var er={en:{title:"Build New Image",name:"Image Name",description:"Description",tags:"Tags",pool:"Compute Pool",guest:"Source Instance",cancel:"Cancel",confirm:"Build"},cn:{title:"\u6784\u5efa\u65b0\u955c\u50cf",name:"\u955c\u50cf\u540d\u79f0",description:"\u63cf\u8ff0",tags:"\u6807\u7b7e",pool:"\u8d44\u6e90\u6c60",guest:"\u6e90\u4e91\u4e3b\u673a",cancel:"\u53d6\u6d88",confirm:"\u6784\u5efa"}};function ar(e){var a={name:"",description:"",tags:new Map,pool:"",guest:""},t=e.lang,n=e.open,l=e.onSuccess,r=e.onCancel,c=o.a.useState(!1),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(!1),p=Object(H.a)(m,2),f=p[0],b=p[1],g=o.a.useState(0),h=Object(H.a)(g,2),E=h[0],v=h[1],y=o.a.useState(!0),x=Object(H.a)(y,2),k=x[0],C=x[1],S=o.a.useState(""),j=Object(H.a)(S,2),O=j[0],w=j[1],_=o.a.useState(!1),N=Object(H.a)(_,2),R=N[0],T=N[1],I=o.a.useState(a),D=Object(H.a)(I,2),A=D[0],P=D[1],F=o.a.useState({tags:[],pools:[],guests:[]}),B=Object(H.a)(F,2),M=B[0],z=B[1],W=er[t],q=W.title,L=o.a.useCallback((function(e){R&&(C(!0),w(e))}),[R]),U=function(e){return function(a){Ta(e),L(a)}},G=function(){w(""),P(a),u(!1),b(!1),v(0)},V=function e(a,t){return function(n){R&&(n.created?function(e){R&&(C(!0),G(),l(e))}(a):(v(n.progress),setTimeout((function(){Na(a,e(a,t),U(a))}),1e3)))}},$=function(e){return function(a){if(R){var t=a.target.value;P((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}}};o.a.useEffect((function(){if(n){var e=[["linux","Linux"],["windows","Windows"],["centos","Centos"],["ubuntu","Ubuntu"],["64bit","64Bit"],["32bit","32Bit"]];T(!0);return da((function(a){if(R){var t=[];a.forEach((function(e){var a=e.name;t.push({label:a,value:a})}));var n=[];e.forEach((function(e){n.push({label:e[1],value:e[0]})})),z({tags:n,pools:t,guests:[]}),P((function(e){return Object(d.a)(Object(d.a)({},e),{},{pool:"",guest:""})})),u(!0)}}),L),function(){T(!1)}}}),[R,n,L]);var Z,Y=[{color:"transparent",label:W.cancel,onClick:function(){G(),r()}}];if(s)if(f)Z=o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ne.a,{item:!0,xs:12},o.a.createElement(Ml.a,{variant:"determinate",value:E})),o.a.createElement(Ne.a,{item:!0,xs:12},o.a.createElement(X.a,{align:"center"},E.toFixed(2)+"%")));else{var J=[{type:"text",label:W.name,value:A.name,onChange:$("name"),required:!0,oneRow:!0,xs:8},{type:"textarea",label:W.description,value:A.description,onChange:$("description"),required:!0,oneRow:!0,rows:4,xs:12},{type:"checkbox",label:W.tags,onChange:function(e){return function(a){if(R){var t=a.target.checked;P((function(a){return Object(d.a)(Object(d.a)({},a),{},{tags:a.tags.set(e,t)})}))}}},value:A.tags,options:M.tags,required:!0,oneRow:!0,xs:10},{type:"select",label:W.pool,onChange:function(e){if(R){var a=e.target.value;Ea(a,null,(function(e){var t=[];e.forEach((function(e){t.push({value:e.id,label:e.name})})),z((function(e){return Object(d.a)(Object(d.a)({},e),{},{guests:t})})),P((function(e){return Object(d.a)(Object(d.a)({},e),{},{pool:a,guest:""})}))}),L)}},value:A.pool,options:M.pools,required:!0,oneRow:!0,xs:10,sm:8,md:6},{type:"select",label:W.guest,onChange:$("guest"),value:A.guest,options:M.guests,required:!0,oneRow:!0,xs:10,sm:8,md:6}];Z=o.a.createElement(Ae,{inputs:J}),Y.push({color:"info",label:W.confirm,onClick:function(){if(w(""),C(!1),A.name)if(A.description)if(A.tags){var e=[];if(A.tags.forEach((function(a,t){a&&e.push(t)})),0!==e.length)if(A.guest){var a=A.name;Ra(a,A.guest,A.description,e,function(e){return function(a){R&&(b(!0),setTimeout((function(){Na(a,V(a,e),U(a))}),1e3))}}(a),L)}else L("must specify source guest");else L("image tags required")}else L("image tags required");else L("desciption required");else L("must specify image name")}})}else Z=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:n,prompt:O,hideBackdrop:!0,title:q,buttons:Y,content:Z,operatable:k})}var tr={en:{title:"Sync Local Disk Images",content:"Are you sure to synchronize local disk images",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u540c\u6b65\u672c\u5730\u78c1\u76d8\u955c\u50cf",content:"\u662f\u5426\u540c\u6b65\u672c\u5730\u78c1\u76d8\u955c\u50cf\u6587\u4ef6",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function nr(e){var a=e.lang,t=e.open,n=e.onSuccess,l=e.onCancel,r=o.a.useState(!0),c=Object(H.a)(r,2),i=c[0],s=c[1],u=o.a.useState(""),m=Object(H.a)(u,2),d=m[0],p=m[1],f=tr[a],b=f.title,g=f.content,h=function(e){s(!0),p(e)},E=function(){s(!0),p(""),n()},v=[{color:"transparent",label:f.cancel,onClick:function(){p(""),l()}},{color:"info",label:f.confirm,onClick:function(){s(!1),function(e,a){var t=ta();null!==t?$a("/disk_images/",{owner:t.user,group:t.group},e,a):a("session expired")}(E,h)}}];return o.a.createElement(Xe,{size:"xs",open:t,prompt:d,title:b,buttons:v,content:g,operatable:i})}var or={en:{modify:"Modify Info",delete:"Delete Image",download:"Download Image",createTime:"Created Time",modifyTime:"Modified Time",uploadButton:"Upload New Disk Image",buildButton:"Build New Disk Image",syncButton:"Synchronize Local Images",noResource:"No images available"},cn:{modify:"\u4fee\u6539\u955c\u50cf\u4fe1\u606f",delete:"\u5220\u9664\u955c\u50cf",download:"\u4e0b\u8f7d\u955c\u50cf",createTime:"\u521b\u5efa\u65f6\u95f4",modifyTime:"\u4fee\u6539\u65f6\u95f4",uploadButton:"\u4e0a\u4f20\u65b0\u78c1\u76d8\u955c\u50cf",buildButton:"\u6784\u5efa\u65b0\u78c1\u76d8\u955c\u50cf",syncButton:"\u540c\u6b65\u672c\u5730\u955c\u50cf\u6587\u4ef6",noResource:"\u6ca1\u6709\u78c1\u76d8\u955c\u50cf"}};var lr={en:{title:"Delete System Template",content:"Are you sure to delete template ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u7cfb\u7edf\u6a21\u677f",content:"\u662f\u5426\u5220\u9664\u7cfb\u7edf\u6a21\u677f ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function rr(e){var a=e.lang,t=e.templateID,n=e.open,l=e.onSuccess,r=e.onCancel,c=o.a.useState(!0),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(""),d=Object(H.a)(m,2),p=d[0],f=d[1],b=lr[a],g=b.title,h=function(e){u(!0),f(e)},E=function(e){u(!0),f(""),l(t)},v=b.content+t,y=[{color:"transparent",label:b.cancel,onClick:function(){f(""),r()}},{color:"info",label:b.confirm,onClick:function(){u(!1),function(e,a,t){Va("/templates/"+e,()=>{a(e)},t)}(t,E,h)}}];return o.a.createElement(Xe,{size:"xs",open:n,prompt:p,title:g,buttons:y,content:v,operatable:s})}var cr={en:{title:"Create System Template",name:"Name",admin:"Admin Name",operatingSystem:"Operating System",disk:"Disk Driver",network:"Network Interface Model",display:"Display Driver",control:"Remote Control Protocol",usb:"USB Model",tablet:"Tablet Mode",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u521b\u5efa\u7cfb\u7edf\u6a21\u677f",name:"\u6a21\u677f\u540d",admin:"\u7ba1\u7406\u5458\u540d\u79f0",operatingSystem:"\u64cd\u4f5c\u7cfb\u7edf",disk:"\u78c1\u76d8\u9a71\u52a8",network:"\u7f51\u5361\u578b\u53f7",display:"\u663e\u5361\u7c7b\u578b",control:"\u8fdc\u7a0b\u7ba1\u7406\u534f\u8bae",usb:"USB\u63a5\u53e3",tablet:"\u89e6\u6478\u5c4f\u63a5\u53e3",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}},ir=["linux","windows"],sr=["scsi","sata","ide"],ur=["virtio","e1000","rtl8139"],mr=["vga","cirrus"],dr=["vnc","spice"],pr=["","nec-xhci"],fr=["","usb"];function br(e){var a={name:"",admin:"",operating_system:"",disk:"",network:"",display:"",control:"",usb:"",tablet:""},t=e.lang,n=e.open,l=e.onSuccess,r=e.onCancel,c=o.a.useState(!0),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(""),p=Object(H.a)(m,2),f=p[0],b=p[1],g=o.a.useState(a),h=Object(H.a)(g,2),E=h[0],v=h[1],y=cr[t],x=y.title,k=function(e){u(!0),b(e)},C=function(){b(""),v(a)},S=function(e){u(!0),C(),l(e)},j=function(e){return function(a){var t=a.target.value;v((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}},O=function(e){return e.map((function(e){return{value:e,label:e||"none"}}))},w=[{type:"text",label:y.name,onChange:j("name"),value:E.name,required:!0,oneRow:!0,xs:10},{type:"text",label:y.admin,onChange:j("admin"),value:E.admin,required:!0,oneRow:!0,xs:8,sm:6},{type:"select",label:y.operatingSystem,onChange:j("operating_system"),value:E.operating_system,required:!0,oneRow:!0,options:O(ir),xs:12,sm:5},{type:"select",label:y.disk,onChange:j("disk"),value:E.disk,required:!0,oneRow:!0,options:O(sr),xs:6,sm:4},{type:"select",label:y.network,onChange:j("network"),value:E.network,required:!0,oneRow:!0,options:O(ur),xs:12,sm:5},{type:"select",label:y.display,onChange:j("display"),value:E.display,required:!0,oneRow:!0,options:O(mr),xs:6,sm:4},{type:"select",label:y.control,onChange:j("control"),value:E.control,required:!0,oneRow:!0,options:O(dr),xs:6,sm:4},{type:"select",label:y.usb,onChange:j("usb"),value:E.usb,required:!0,oneRow:!0,options:O(pr),xs:8,sm:4},{type:"select",label:y.tablet,onChange:j("tablet"),value:E.tablet,required:!0,oneRow:!0,options:O(fr),xs:8,sm:4}],_=o.a.createElement(Ae,{inputs:w}),N=[{color:"transparent",label:y.cancel,onClick:function(){C(),r()}},{color:"info",label:y.confirm,onClick:function(){E.name?E.admin?(b(""),u(!1),function(e,a,t,n,o,l,r,c,i,s,u){var m={name:e,admin:a,operating_system:t,disk:n,network:o,display:l,control:r};c&&(m.usb=c),i&&(m.tablet=i),Ha("/templates/",m,e=>{let{id:a}=e;s(a)},u)}(E.name,E.admin,E.operating_system,E.disk,E.network,E.display,E.control,E.usb,E.tablet,S,k)):k("must specify admin name"):k("must specify template name")}}];return o.a.createElement(Xe,{size:"xs",open:n,prompt:f,title:x,buttons:N,content:_,operatable:s})}var gr={en:{title:"Modify System Template",name:"Name",admin:"Admin Name",operatingSystem:"Operating System",disk:"Disk Driver",network:"Network Interface Model",display:"Display Driver",control:"Remote Control Protocol",usb:"USB Model",tablet:"Tablet Mode",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u7cfb\u7edf\u6a21\u677f",name:"\u6a21\u677f\u540d",admin:"\u7ba1\u7406\u5458\u540d\u79f0",operatingSystem:"\u64cd\u4f5c\u7cfb\u7edf",disk:"\u78c1\u76d8\u9a71\u52a8",network:"\u7f51\u5361\u578b\u53f7",display:"\u663e\u5361\u7c7b\u578b",control:"\u8fdc\u7a0b\u7ba1\u7406\u534f\u8bae",usb:"USB\u63a5\u53e3",tablet:"\u89e6\u6478\u5c4f\u63a5\u53e3",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}},hr=["linux","windows"],Er=["scsi","sata","ide"],vr=["virtio","e1000","rtl8139"],yr=["vga","cirrus"],xr=["vnc","spice"],kr=["","nec-xhci"],Cr=["","usb"];function Sr(e){var a={name:"",admin:"",operating_system:"",disk:"",network:"",display:"",control:"",usb:"",tablet:""},t=e.lang,n=e.templateID,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!1),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(!0),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(""),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(!1),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState(a),O=Object(H.a)(j,2),w=O[0],_=O[1],N=gr[t],R=N.title,T=o.a.useCallback((function(e){C&&(g(!0),y(e))}),[C]),I=function(){y(""),_(a),m(!1)},D=function(e){C&&(g(!0),I(),r(e))},A=function(e){return function(a){if(C){var t=a.target.value;_((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}}},P=function(e){return e.map((function(e){return{value:e,label:e||"none"}}))};o.a.useEffect((function(){if(n&&l){S(!0);return function(e,a,t){Wa("/templates/"+e,a,t)}(n,(function(e){C&&(_(e),m(!0))}),T),function(){S(!1)}}}),[C,l,n,T]);var F,B=[{color:"transparent",label:N.cancel,onClick:function(){I(),c()}}];if(u){var M=[{type:"text",label:N.name,onChange:A("name"),value:w.name,required:!0,oneRow:!0,xs:10},{type:"text",label:N.admin,onChange:A("admin"),value:w.admin,required:!0,oneRow:!0,xs:8,sm:6},{type:"select",label:N.operatingSystem,onChange:A("operating_system"),value:w.operating_system,required:!0,oneRow:!0,options:P(hr),xs:12,sm:5},{type:"select",label:N.disk,onChange:A("disk"),value:w.disk,required:!0,oneRow:!0,options:P(Er),xs:6,sm:4},{type:"select",label:N.network,onChange:A("network"),value:w.network,required:!0,oneRow:!0,options:P(vr),xs:12,sm:5},{type:"select",label:N.display,onChange:A("display"),value:w.display,required:!0,oneRow:!0,options:P(yr),xs:6,sm:4},{type:"select",label:N.control,onChange:A("control"),value:w.control,required:!0,oneRow:!0,options:P(xr),xs:6,sm:4},{type:"select",label:N.usb,onChange:A("usb"),value:w.usb,required:!0,oneRow:!0,options:P(kr),xs:8,sm:4},{type:"select",label:N.tablet,onChange:A("tablet"),value:w.tablet,required:!0,oneRow:!0,options:P(Cr),xs:8,sm:4}];F=o.a.createElement(Ae,{inputs:M}),B.push({color:"info",label:N.confirm,onClick:function(){if(w.name)if(w.admin){y(""),g(!1);var e=w.name,a=w.admin,t=w.operating_system,o=w.disk,l=w.network,r=w.display,c=w.control,i=w.usb,s=w.tablet;!function(e,a,t,n,o,l,r,c,i,s,u,m){const d="/templates/"+e;var p={name:a,admin:t,operating_system:n,disk:o,network:l,display:r,control:c};i&&(p.usb=i),s&&(p.tablet=s),Ga(d,p,()=>{u(e)},m)}(n,e,a,t,o,l,r,c,i,s,D,T)}else T("must specify admin name");else T("must specify template name")}})}else F=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"xs",open:l,prompt:v,title:R,buttons:B,content:F,operatable:b})}var jr=Object(m.a)({cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),Or={en:{createButton:"Create System Template",tableTitle:"System Templates",name:"Name",os:"Operating System",createdTime:"Created Time",modifiedTime:"Last Modified",operates:"Operates",noResource:"No system templates available",detail:"Detail",delete:"Delete"},cn:{createButton:"\u521b\u5efa\u65b0\u6a21\u677f",tableTitle:"\u7cfb\u7edf\u6a21\u677f",name:"\u540d\u79f0",os:"\u64cd\u4f5c\u7cfb\u7edf",createdTime:"\u521b\u5efa\u65f6\u95f4",modifiedTime:"\u6700\u540e\u4fee\u6539",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u7cfb\u7edf\u6a21\u677f",detail:"\u8be6\u60c5",delete:"\u5220\u9664"}};var wr=t(80),_r=t(91),Nr=t.n(_r),Rr=t(188),Tr=t.n(Rr),Ir=t(114),Dr=t.n(Ir),Ar=Object(m.a)(jn);function Pr(e){var a=Ar(),t=e.color,n=e.headers,l=e.rows;return o.a.createElement("div",{className:a.tableResponsive},o.a.createElement(yn.a,{className:a.table},o.a.createElement(xn.a,{className:a[t+"TableHeader"]},o.a.createElement(kn.a,{className:a.tableHeadRow},n.map((function(e,t){return o.a.createElement(Sn.a,{className:a.tableCell+" "+a.tableHeadCell,key:t},e)})))),o.a.createElement(Cn.a,null,l)))}Pr.defaultProps={color:"gray"};var Fr=t(128),Br=t.n(Fr),Mr=t(185),zr=t.n(Mr),Wr=t(290),qr=t.n(Wr),Hr=t(179),Lr=t.n(Hr),Ur=t(292),Gr=t.n(Ur),Vr=t(293),$r=t.n(Vr),Zr=t(291),Yr=t.n(Zr),Jr=t(180),Qr=t.n(Jr),Kr=t(294),Xr=t.n(Kr),ec=t(181),ac=t.n(ec),tc=t(182),nc=t.n(tc),oc=t(183),lc=t.n(oc),rc=t(184),cc=t.n(rc),ic=t(129),sc=t.n(ic),uc=t(113),mc=t.n(uc),dc={en:{running:"Running",stopped:"Stopped",start:"Start Instance",startWithMedia:"Start Instance With Media",snapshot:"Snapshot",createImage:"Create Disk Image",resetSystem:"Reset System",delete:"Delete Instance",migrate:"Migrate Instance",monitor:"Monitor Resource Usage",detail:"Instance Detail",security:"Security Policies",remoteControl:"Remote Control",stop:"Stop Instance",forceStop:"Force Stop Instance",reboot:"Reboot Instance",reset:"Reset Instance",insertMedia:"Insert Media",ejectMedia:"Eject Media",autoStartup:"Auto Startup",mediaAttached:"Media Attached"},cn:{running:"\u8fd0\u884c\u4e2d",stopped:"\u5df2\u505c\u6b62",start:"\u542f\u52a8\u4e91\u4e3b\u673a",startWithMedia:"\u4ece\u5149\u76d8\u955c\u50cf\u542f\u52a8\u4e91\u4e3b\u673a",snapshot:"\u5feb\u7167",createImage:"\u521b\u5efa\u78c1\u76d8\u955c\u50cf",resetSystem:"\u91cd\u7f6e\u7cfb\u7edf",delete:"\u5220\u9664\u4e91\u4e3b\u673a",migrate:"\u8fc1\u79fb\u4e91\u4e3b\u673a",monitor:"\u76d1\u63a7\u8d44\u6e90\u7528\u91cf",detail:"\u5b9e\u4f8b\u8be6\u60c5",security:"\u5b89\u5168\u7b56\u7565",remoteControl:"\u8fdc\u7a0b\u76d1\u63a7",stop:"\u505c\u6b62\u4e91\u4e3b\u673a",forceStop:"\u5f3a\u5236\u7ec8\u6b62\u4e91\u4e3b\u673a",reboot:"\u91cd\u542f\u4e91\u4e3b\u673a",reset:"\u5f3a\u5236\u91cd\u542f\u4e91\u4e3b\u673a",insertMedia:"\u63d2\u5165\u5149\u76d8\u955c\u50cf",ejectMedia:"\u5f39\u51fa\u5149\u76d8\u955c\u50cf",autoStartup:"\u5f00\u673a\u542f\u52a8",mediaAttached:"\u5a92\u4f53\u5df2\u52a0\u8f7d"}};function pc(e){var a,t=Object(m.a)(jn)(),n=Object(m.a)(z)(),l=e.lang,r=e.instance,c=e.onNotify,i=e.onError,s=e.onDelete,u=e.onStatusChange,d=e.onMediaStart,p=e.onInsertMedia,f=e.onResetSystem,b=e.onBuildImage,g=e.onMigrateInstance,h=e.checked,E=e.checkable,v=e.onCheckStatusChanged,y=dc[l],x={tips:y.start,icon:Br.a,handler:function(e){!function(e,a,t){Ha("/instances/"+e,{},()=>{a(e)},t)}(e,(function(e){c("instance "+e+" started"),u()}),(function(a){i("start instance "+e+" fail: "+a)}))}},k={tips:y.startWithMedia,icon:qr.a,handler:d},C={tips:y.snapshot,icon:Lr.a,href:"/admin/instances/snapshots/"+r.id},S={tips:y.createImage,icon:Yr.a,handler:b},j={tips:y.resetSystem,icon:Gr.a,handler:f},O={tips:y.delete,icon:Ht.a,handler:s},w={tips:y.migrate,icon:Vt.a,handler:function(e){g(e,r.pool,r.cell)}},_={tips:y.monitor,icon:$r.a,href:"/admin/instances/status/"+r.id,target:"_blank"},N={tips:y.detail,icon:Ut.a,href:"/admin/instances/details/"+r.id,target:"_blank"},R={tips:y.security,icon:Qr.a,href:"/admin/instances/policies/"+r.id},T={tips:y.remoteControl,icon:Xr.a,href:"/monitor/"+r.id,target:"_blank"},I={tips:y.stop,icon:Nr.a,handler:function(e){ka(e,(function(e){c("instance "+e+" stopped"),u()}),(function(a){i("stop instance "+e+" fail: "+a)}))}},D={tips:y.forceStop,icon:An.a,handler:function(e){!function(e,a,t){Za("/instances/"+e,{reboot:!1,force:!0},()=>{a(e)},t)}(e,(function(e){c("instance "+e+" force stopped"),u()}),(function(a){i("force stop instance "+e+" fail: "+a)}))}},A={tips:y.reboot,icon:ac.a,handler:function(e){Ca(e,(function(e){c("instance "+e+" reboot"),u()}),(function(a){i("reboot instance "+e+" fail: "+a)}))}},P={tips:y.reset,icon:nc.a,handler:function(e){Sa(e,(function(e){c("instance "+e+" reset"),u()}),(function(a){i("reset instance "+e+" fail: "+a)}))}},F={tips:y.insertMedia,icon:lc.a,handler:p},B={tips:y.ejectMedia,icon:cc.a,handler:function(e){xa(e,(function(e){c("media ejected from instance "+e),u()}),(function(a){i("eject media from instance "+e+" fail: "+a)}))}},M=[];r.running?(a=[o.a.createElement(Q.a,{title:y.running,placement:"top",key:y.running},o.a.createElement(Br.a,{className:n.successText}))],M=[T,I,D,A,P],r.auto_start&&a.push(o.a.createElement(Q.a,{title:y.autoStartup,placement:"top",key:y.autoStartup},o.a.createElement(sc.a,{className:n.infoText}))),r.media_attached?(a.push(o.a.createElement(Q.a,{title:y.mediaAttached,placement:"top",key:y.mediaAttached},o.a.createElement(mc.a,{className:n.infoText}))),M.push(B)):M.push(F),M=M.concat([_,N,R])):(a=[o.a.createElement(Q.a,{title:y.stopped,placement:"top",key:y.stopped},o.a.createElement(zr.a,{className:n.dangerText}))],r.auto_start&&a.push(o.a.createElement(Q.a,{title:y.autoStartup,placement:"top",key:y.autoStartup},o.a.createElement(sc.a,{className:n.infoText}))),M=[x,k,C,S,j,O,w,_,N,R]);var W="";r.internal&&r.internal.network_address&&(W=r.internal.network_address),r.external&&r.external.network_address&&(W+="/"+r.external.network_address);var q;q=r.memory>=1<<30?r.memory/(1<<30)+" GB":r.memory/(1<<20)+" MB";var H=[];r.disks.forEach((function(e){H.push((e/(1<<30)).toFixed(2).toString())}));var L,U=H.join("/")+" GB";return L=E?o.a.createElement($.a,{display:"flex",alignItems:"center"},o.a.createElement($.a,null,o.a.createElement(Te.a,{checked:h,onChange:function(e){return function(e){var a=e.target.checked;v(a,r.id)}(e)},value:r.id})),o.a.createElement($.a,null,r.name)):r.name,o.a.createElement(kn.a,{className:t.tableBodyRow},o.a.createElement(Sn.a,{className:t.tableCell},L),o.a.createElement(Sn.a,{className:t.tableCell},r.host?r.host:r.cell),o.a.createElement(Sn.a,{className:t.tableCell},W),o.a.createElement(Sn.a,{className:t.tableCell},r.cores),o.a.createElement(Sn.a,{className:t.tableCell},q),o.a.createElement(Sn.a,{className:t.tableCell},U),o.a.createElement(Sn.a,{className:t.tableCell},a),o.a.createElement(Sn.a,{className:t.tableCell},M.map((function(e,a){var t,n=o.a.createElement(e.icon);return t=e.href?o.a.createElement(kt.a,{to:e.href,target:e.target},o.a.createElement(K.a,{color:"inherit"},n)):o.a.createElement(K.a,{color:"inherit",onClick:function(){null!==e.handler&&e.handler(r.id)}},n),o.a.createElement(Q.a,{title:e.tips,placement:"top",key:a},t)}))))}var fc={en:{title:"Delete Instance",content:"Are you sure to delete instance ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u4e91\u4e3b\u673a",content:"\u662f\u5426\u5220\u9664\u4e91\u4e3b\u673a ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function bc(e){var a=e.lang,t=e.instanceID,n=e.open,l=e.onSuccess,r=e.onCancel,c=o.a.useState(!0),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(""),d=Object(H.a)(m,2),p=d[0],f=d[1],b=fc[a],g=b.title,h=b.content+t,E=function(e){u(!0),f(e)},v=function(){u(!0),f(""),l(t)},y=[{color:"transparent",label:b.cancel,onClick:function(){f(""),r()}},{color:"info",label:b.confirm,onClick:function(){u(!1),function(e,a,t){Za("/guests/"+e,{force:!1},a,t)}(t,v,E)}}];return o.a.createElement(Xe,{size:"sm",open:n,prompt:p,title:g,buttons:y,content:h,operatable:s})}var gc=t(614),hc=t(593),Ec=t(594),vc=t(187),yc=t.n(vc),xc=["children"];function kc(e){var a=e.children,t=Object(L.a)(e,xc);return o.a.createElement(Ne.a,{item:!0,xs:12},o.a.createElement($.a,{m:1,p:0},o.a.createElement(Ne.a,Object.assign({container:!0},t),a)))}const Cc={en:{title:"Create Instance",name:"Instance Name",resourcePool:"Resource Pool",core:"Core",memory:"Memory",systemDisk:"System Disk Size",dataDisk:"Data Disk Size",autoStartup:"Automatic Startup",systemVersion:"System Version",sourceImage:"Source Image",blankSystem:"Blank System",qos:"QoS Options (Optional)",cpuPriority:"CPU Priority",iops:"IOPS",outbound:"Outband Bandwidth",inbound:"Inbound Bandwidth",noDataDisk:"Don't use data disk",noLimit:"No Limit",cpuPriorityHigh:"High",cpuPriorityMedium:"Medium",cpuPriorityLow:"Low",modules:"Pre-Installed Modules",adminName:"Admin Name",adminPassword:"Admin Password",blankHelper:"Leave blank to generate",dataPath:"Data Path",off:"Off",on:"On",ciOptions:"Cloud Init Options",securityPolicy:"Security Policy",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u521b\u5efa\u4e91\u4e3b\u673a",name:"\u4e91\u4e3b\u673a\u540d\u79f0",resourcePool:"\u8ba1\u7b97\u8d44\u6e90\u6c60",core:"\u6838\u5fc3\u6570",memory:"\u5185\u5b58",systemDisk:"\u7cfb\u7edf\u78c1\u76d8\u5bb9\u91cf",dataDisk:"\u6570\u636e\u78c1\u76d8\u5bb9\u91cf",autoStartup:"\u81ea\u52a8\u542f\u52a8",systemVersion:"\u7cfb\u7edf\u7248\u672c",sourceImage:"\u6765\u6e90\u955c\u50cf",blankSystem:"\u7a7a\u767d\u7cfb\u7edf",qos:"QoS\u9009\u9879 (\u53ef\u9009)",cpuPriority:"CPU\u4f18\u5148\u7ea7",iops:"\u78c1\u76d8\u8bfb\u5199\u9650\u5236",outbound:"\u4e0a\u884c\u5e26\u5bbd",inbound:"\u4e0b\u884c\u5e26\u5bbd",noDataDisk:"\u4e0d\u4f7f\u7528\u6570\u636e\u78c1\u76d8",noLimit:"\u65e0\u9650\u5236",cpuPriorityHigh:"\u9ad8",cpuPriorityMedium:"\u4e2d",cpuPriorityLow:"\u4f4e",modules:"\u9884\u88c5\u6a21\u5757",adminName:"\u7ba1\u7406\u5458\u8d26\u53f7",adminPassword:"\u7ba1\u7406\u5458\u5bc6\u7801",blankHelper:"\u7559\u7a7a\u5219\u81ea\u52a8\u751f\u6210\u65b0\u5bc6\u7801",dataPath:"\u6302\u8f7d\u6570\u636e\u8def\u5f84",off:"\u5173\u95ed",on:"\u5f00\u542f",ciOptions:"Cloud Init\u9009\u9879",securityPolicy:"\u5b89\u5168\u7b56\u7565",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Sc(e){const{lang:a,open:t,onSuccess:n,onCancel:l}=e,r={name:"",pool:"",cores:1..toString(),memory:(1<<30).toString(),system_disk:5,data_disk:0,auto_start:!1,system_template:"",from_image:"__default",security_policy:"",modules:new Map,module_cloud_init_admin_name:"root",module_cloud_init_admin_password:"",module_cloud_init_data_path:"/opt/data",priority:"medium",iops:0,inbound:0,outbound:0},[c,i]=o.a.useState(!1),[s,u]=o.a.useState(!1),[m,d]=o.a.useState(0),[p,f]=o.a.useState(!0),[b,g]=o.a.useState(""),[h,E]=o.a.useState(!1),[v,y]=o.a.useState(r),[x,k]=o.a.useState({pools:[],images:[],versions:[],policies:[]}),[C,S]=o.a.useState(16),[j,O]=o.a.useState(24),[w,_]=o.a.useState(32),[N,R]=o.a.useState(0),[T,I]=o.a.useState(1),D=Cc[a],A=D.title,P=o.a.useCallback(e=>{h&&(f(!0),g(e))},[h]),F=()=>{g(""),y(r),i(!1),u(!1),d(0)},B=e=>{h&&(f(!0),F(),n(e.id))},M=e=>{h&&(u(!0),d(0),setTimeout(()=>{z(e)},1e3))},z=e=>{if(!h)return;va(e,B,P,a=>{h&&(d(a),setTimeout(()=>{z(e)},1e3))})},W=()=>{if(g(""),f(!1),!v.name)return void P("instance name required");if(!v.pool)return void P("must specify target pool");var e=Number(v.cores);if(Number.isNaN(e))return void P("invalid cores: "+v.cores);var a=Number(v.memory);if(Number.isNaN(a))return void P("invalid memory: "+v.memory);var t=[v.system_disk*(1<<30)];0!==v.data_disk&&t.push(v.data_disk*(1<<30));var n=v.system_template;let o;o="__default"===v.from_image?"":v.from_image;var l=[],r=!1;v.modules.forEach((e,a)=>{e&&(l.push(a),"cloud-init"===a&&(r=!0))});var c=null;r&&(c={admin_name:v.module_cloud_init_admin_name,admin_secret:v.module_cloud_init_admin_password,data_path:v.module_cloud_init_data_path});var i={cpu_priority:v.priority,write_iops:v.iops,read_iops:v.iops,receive_speed:v.inbound*(1<<17),send_speed:v.outbound*(1<<17)};!function(e,a,t,n,o,l,r,c,i,s,u,m,d,p,f){var b=ta();if(null===b)return void f("session expired");var g={name:e,owner:b.user,group:b.group,pool:a,cores:t,memory:n,disks:o,auto_start:l,from_image:r,template:c};i&&(g.modules=i),s&&(g.cloud_init=s),u&&(g.qos=u),m&&(g.security_policy_group=m),La("/guests/",g,(e,a)=>{202===e?d(a.id):200===e?p(a.id):f("unexpected status "+e.toString())},f)}(v.name,v.pool,e,a,t,v.auto_start,o,n,l,c,i,v.security_policy,M,B,P)},q=e=>a=>{if(h){var t=a.target.value;y(a=>({...a,[e]:t}))}},H=e=>(a,t)=>{h&&y(a=>({...a,[e]:t}))};o.a.useEffect(()=>{if(!t)return;var e=[],a=[{label:D.blankSystem,value:"__default"}],n=[],o=[];E(!0);const l=t=>{h&&(t.forEach(e=>{let{id:a,name:t}=e;o.push({label:t,value:a})}),k({pools:e,images:a,versions:n,policies:o}),i(!0))},r=e=>{h&&(e.forEach(e=>{let{id:a,name:t}=e;n.push({label:t,value:a})}),Fa("","",!0,!0,l,P))},c=e=>{h&&(e.forEach(e=>{let{name:t,id:n}=e;a.push({label:t,value:n})}),Pa(r,P))},s=a=>{h&&(a.forEach(a=>{let{name:t}=a;e.push({label:t,value:t})}),_a(c,P))};return za(e=>{h&&(e.max_cores&&e.max_cores>0&&S(e.max_cores),e.max_memory&&e.max_memory>0&&O(e.max_memory),e.max_disk&&e.max_disk>0&&_(e.max_disk),da(s,P))},P),()=>{E(!1)}},[h,t,D.blankSystem,P]);var L=[{color:"transparent",label:D.cancel,onClick:()=>{F(),l()}}];let U;if(c)if(s)U=o.a.createElement(Ne.a,{container:!0},o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12},o.a.createElement(Ml.a,{variant:"determinate",value:m}))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12},o.a.createElement(X.a,{align:"center"},m.toFixed(2)+"%"))));else{const e=e=>{let a=2**e;return a>C&&(a=C),a.toString()},t=(e,a)=>{if(!h)return;R(a);let t=2**a;t>C&&(t=C),y(e=>({...e,cores:t}))};let n=Math.ceil(Math.sqrt(C)),l=0,r=[];for(let a=l+1;a<=n;++a){let e=2**a;e>C&&(e=C),r.push({value:a,label:e.toString()})}let c={type:"slider",label:D.core+" - ".concat(v.cores),onChange:t,valueLabelFormat:e,value:N,oneRow:!0,maxStep:n,minStep:l,step:1,marks:r,xs:12,sm:6,md:4};const i=512,s=1<<20,u=1<<30,m=e=>{let a;return a=e>=u?e/u+" GB":e/s+" MB",a},d=(e,a)=>{if(!h)return;I(a);let t=2**a*i*s;t>j*u&&(t=j*u),y(e=>({...e,memory:t}))};let p=Math.ceil(Math.sqrt(j)),f=0,b=[];for(let a=f+1;a<=p;++a){let e=2**a*i*s;e>j*u&&(e=j*u),b.push({value:a,label:m(e)})}let g,E,k,S={type:"slider",label:D.memory+" - ".concat(m(v.memory)),onChange:d,valueLabelDisplay:"off",value:T,oneRow:!0,maxStep:p,minStep:f,step:1,marks:b,xs:12,sm:8,md:6};{const e=1,a=5,t=w;var G=[];[a,t>>1,t].forEach(e=>{G.push({value:e,label:e+" GB"})}),g={type:"slider",label:D.systemDisk+" - ".concat(v.system_disk," GB"),onChange:H("system_disk"),value:v.system_disk,oneRow:!0,maxStep:t,minStep:a,step:e,marks:G,xs:12,sm:6,md:4}}{const e=0,a=2,t=w;let n,o=[];[e,t>>1,t].forEach(e=>{o.push({value:e,label:e+" GB"})}),n=0===v.data_disk?D.dataDisk+" - "+D.noDataDisk:D.dataDisk+" - ".concat(v.data_disk," GB"),E={type:"slider",label:n,onChange:H("data_disk"),value:v.data_disk,oneRow:!0,maxStep:t,minStep:e,step:a,marks:o,xs:12,sm:6,md:4}}if(v.system_template&&"__default"!==v.from_image){var V=[{value:"qemu",label:"QEMU-Guest-Agent"},{value:"cloud-init",label:"CloudInit"}];let e;if(v.modules.get("cloud-init")){const a=[{type:"text",label:D.adminName,onChange:q("module_cloud_init_admin_name"),value:v.module_cloud_init_admin_name,oneRow:!0,xs:12},{type:"text",label:D.adminPassword,onChange:q("module_cloud_init_admin_password"),helper:D.blankHelper,oneRow:!0,xs:12},{type:"text",label:D.dataPath,onChange:q("module_cloud_init_data_path"),value:v.module_cloud_init_data_path,disabled:!0,oneRow:!0,xs:12}];e=o.a.createElement(Ve,{xs:12,sm:8,md:6},o.a.createElement(_e.a,{component:"legend"},D.ciOptions),o.a.createElement(Ae,{inputs:a}))}else e=o.a.createElement(Ve,null);k=o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(we.a,{component:"fieldset",fullWidth:!0},o.a.createElement(_e.a,{component:"legend"},D.modules),o.a.createElement(Re.a,null,o.a.createElement(Ne.a,{container:!0},V.map(e=>{let a;return a=!!v.modules.has(e.value)&&v.modules.get(e.value),o.a.createElement(Ve,{xs:12,sm:6,md:4,key:e.value},o.a.createElement(Oe.a,{control:o.a.createElement(Te.a,{checked:a,onChange:(t="modules",n=e.value,e=>{if(h){var a=e.target.checked;y(e=>({...e,[t]:e[t].set(n,a)}))}}),value:e.value}),label:e.label}));var t,n})))))),e)}else k=o.a.createElement(Ve,null);var Z=[{type:"text",label:D.name,onChange:q("name"),value:v.name,oneRow:!0,required:!0,xs:12,sm:6,md:4},{type:"select",label:D.resourcePool,onChange:q("pool"),value:v.pool,oneRow:!0,options:x.pools,required:!0,xs:10,sm:4,md:3},c,S,g,E,{type:"select",label:D.sourceImage,onChange:q("from_image"),value:v.from_image,oneRow:!0,options:x.images,xs:10,sm:6,md:5},{type:"select",label:D.systemVersion,onChange:q("system_template"),value:v.system_template,oneRow:!0,options:x.versions,xs:10,sm:5,md:4},{type:"switch",label:D.autoStartup,onChange:(Y="auto_start",e=>{if(h){var a=e.target.checked;y(e=>({...e,[Y]:a}))}}),value:v.auto_start,on:D.on,off:D.off,oneRow:!0,xs:8,sm:6,md:4}];x.policies&&0!==x.policies.length&&Z.push({type:"select",label:D.securityPolicy,onChange:q("security_policy"),value:v.security_policy,oneRow:!0,options:x.policies,xs:10,sm:5,md:4});const O=[{value:"high",label:D.cpuPriorityHigh},{value:"medium",label:D.cpuPriorityMedium},{value:"low",label:D.cpuPriorityLow}],_=2e3;let A=[];[0,_/2,_].forEach(e=>{A.push({value:e,label:e.toString()})});const P=20;let F,B,M,z=[];[0,P/2,P].forEach(e=>{z.push({value:e,label:0===e?D.noLimit:e.toString()+" Mbit/s"})}),F=0===v.iops?D.iops+" - "+D.noLimit:"cn"===a?D.iops+" - "+v.iops+"\u6b21\u8bf7\u6c42/\u79d2":D.iops+" - "+v.iops+" Operates/Second",B=0===v.inbound?D.inbound+" - "+D.noLimit:D.inbound+" - "+v.inbound+" Mbit/s",M=0===v.outbound?D.outbound+" - "+D.noLimit:D.outbound+" - "+v.outbound+" Mbit/s";const J=[{type:"radio",label:D.cpuPriority,onChange:q("priority"),value:v.priority,oneRow:!0,options:O,xs:12},{type:"slider",label:F,onChange:H("iops"),value:v.iops,oneRow:!0,maxStep:_,minStep:0,step:10,marks:A,xs:12},{type:"slider",label:B,onChange:H("inbound"),value:v.inbound,oneRow:!0,maxStep:P,minStep:0,step:2,marks:z,xs:12},{type:"slider",label:M,onChange:H("outbound"),value:v.outbound,oneRow:!0,maxStep:P,minStep:0,step:2,marks:z,xs:12}];U=o.a.createElement(Ne.a,{container:!0},o.a.createElement($.a,{m:1,pt:2},o.a.createElement(Ae,{inputs:Z})),k,o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:9,md:7},o.a.createElement($.a,{m:0,pb:2},o.a.createElement(gc.a,null,o.a.createElement(hc.a,{expandIcon:o.a.createElement(yc.a,null)},D.qos),o.a.createElement(Ec.a,null,o.a.createElement($.a,{m:1,pt:2},o.a.createElement(Ae,{inputs:J})))))))),L.push({color:"info",label:D.confirm,onClick:W})}else U=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});var Y;return o.a.createElement(Xe,{size:"md",open:t,prompt:b,promptPosition:"top",hideBackdrop:!0,title:A,buttons:L,content:U,operatable:p})}var jc={en:{title:"Start Instance With Media",name:"Media Name",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4ece\u5f15\u5bfc\u5149\u76d8\u542f\u52a8\u4e91\u4e3b\u673a",name:"\u955c\u50cf\u540d\u79f0",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function Oc(e){var a={image:""},t=e.lang,n=e.instanceID,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!1),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(!0),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(""),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(!1),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState(a),O=Object(H.a)(j,2),w=O[0],_=O[1],N=o.a.useState({images:[]}),R=Object(H.a)(N,2),T=R[0],I=R[1],D=jc[t],A=D.title,P=o.a.useCallback((function(e){C&&(g(!0),y(e))}),[C]),F=function(){y(""),_(a),m(!1)},B=function(e){C&&(g(!0),F(),r(e))};o.a.useEffect((function(){if(l){S(!0);return Oa((function(e){if(C){var a=[];e.forEach((function(e){a.push({value:e.id,label:e.name})})),I({images:a}),m(!0)}}),P),function(){return S(!1)}}}),[C,l,P]);var M,z,W=[{color:"transparent",label:D.cancel,onClick:function(){F(),c()}}];if(u){var q=[{type:"select",label:D.name,onChange:(z="image",function(e){if(C){var a=e.target.value;_((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},z,a))}))}}),value:w.image,options:T.images,required:!0,oneRow:!0,xs:12,sm:10}];M=o.a.createElement(Ae,{inputs:q}),W.push({color:"info",label:D.confirm,onClick:function(){y(""),g(!1);var e=w.image;""!==e?function(e,a,t,n){Ha("/instances/"+e,{from_media:!0,source:a},()=>{t(e)},n)}(n,e,B,P):P("select a media image")}})}else M=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"xs",open:l,prompt:v,title:A,buttons:W,content:M,operatable:b})}var wc={en:{title:"Insert Media Into Instance",name:"Media Name",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5411\u4e91\u4e3b\u673a\u63d2\u5165\u5149\u76d8\u955c\u50cf",name:"\u955c\u50cf\u540d\u79f0",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function _c(e){var a={image:""},t=e.lang,n=e.instanceID,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!1),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(!0),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(""),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(!1),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState(a),O=Object(H.a)(j,2),w=O[0],_=O[1],N=o.a.useState({images:[]}),R=Object(H.a)(N,2),T=R[0],I=R[1],D=wc[t],A=D.title,P=o.a.useCallback((function(e){C&&(g(!0),y(e))}),[C]),F=function(){y(""),_(a),m(!1)},B=function(e){C&&(g(!0),F(),r(e))};o.a.useEffect((function(){if(l){S(!0);return Oa((function(e){if(C){var a=[];e.forEach((function(e){a.push({value:e.id,label:e.name})})),I({images:a}),m(!0)}}),P),function(){return S(!1)}}}),[C,l,P]);var M,z,W=[{color:"transparent",label:D.cancel,onClick:function(){F(),c()}}];if(u){var q=[{type:"select",label:D.name,onChange:(z="image",function(e){if(C){var a=e.target.value;_((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},z,a))}))}}),value:w.image,options:T.images,required:!0,oneRow:!0,xs:12,sm:10}];M=o.a.createElement(Ae,{inputs:q}),W.push({color:"info",label:D.confirm,onClick:function(){y(""),g(!1);var e=w.image;""!==e?function(e,a,t,n){Ha("/instances/"+e+"/media",{source:a},()=>{t(e)},n)}(n,e,B,P):P("select a media image")}})}else M=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"xs",open:l,prompt:v,title:A,buttons:W,content:M,operatable:b})}var Nc={en:{title:"Build New Image",name:"Image Name",description:"Description",tags:"Tags",cancel:"Cancel",confirm:"Build"},cn:{title:"\u6784\u5efa\u65b0\u955c\u50cf",name:"\u955c\u50cf\u540d\u79f0",description:"\u63cf\u8ff0",tags:"\u6807\u7b7e",cancel:"\u53d6\u6d88",confirm:"\u6784\u5efa"}};function Rc(e){var a={name:"",description:"",tags:new Map},t=e.lang,n=e.instanceID,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!1),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(!1),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(0),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(!0),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState(""),O=Object(H.a)(j,2),w=O[0],_=O[1],N=o.a.useState(!1),R=Object(H.a)(N,2),T=R[0],I=R[1],D=o.a.useState(a),A=Object(H.a)(D,2),P=A[0],F=A[1],B=o.a.useState({tags:[]}),M=Object(H.a)(B,2),z=M[0],W=M[1],q=Nc[t],L=q.title,U=o.a.useCallback((function(e){T&&(S(!0),_(e))}),[T]),G=function(e){return function(a){Ta(e),U(a)}},V=function(){_(""),F(a),m(!1),g(!1),y(0)},$=function e(a,t){return function(n){T&&(n.created?function(e){T&&(S(!0),V(),r(e))}(t):(y(n.progress),setTimeout((function(){Na(a,e(a,t),G(a))}),1e3)))}},Z=function(e){return function(a){if(T){var t=a.target.value;F((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}}};o.a.useEffect((function(){if(l){I(!0);var e=[];return[["linux","Linux"],["windows","Windows"],["centos","Centos"],["ubuntu","Ubuntu"],["64bit","64Bit"],["32bit","32Bit"]].forEach((function(a){e.push({label:a[1],value:a[0]})})),W({tags:e}),m(!0),function(){I(!1)}}}),[l]);var Y,J=[{color:"transparent",label:q.cancel,onClick:function(){V(),c()}}];if(u)if(b)Y=o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ne.a,{item:!0,xs:12},o.a.createElement(Ml.a,{variant:"determinate",value:v})),o.a.createElement(Ne.a,{item:!0,xs:12},o.a.createElement(X.a,{align:"center"},v.toFixed(2)+"%")));else{var Q=[{type:"text",label:q.name,value:P.name,onChange:Z("name"),required:!0,oneRow:!0,xs:8},{type:"textarea",label:q.description,value:P.description,onChange:Z("description"),required:!0,oneRow:!0,rows:4,xs:12},{type:"checkbox",label:q.tags,onChange:function(e){return function(a){if(T){var t=a.target.checked;F((function(a){return Object(d.a)(Object(d.a)({},a),{},{tags:a.tags.set(e,t)})}))}}},value:P.tags,options:z.tags,required:!0,oneRow:!0,xs:10}];Y=o.a.createElement(Ae,{inputs:Q}),J.push({color:"info",label:q.confirm,onClick:function(){if(_(""),S(!1),P.name)if(P.description)if(P.tags){var e=[];if(P.tags.forEach((function(a,t){a&&e.push(t)})),0!==e.length){var a=P.name;Ra(a,n,P.description,e,function(e){return function(a){T&&(g(!0),setTimeout((function(){Na(a,$(a,e),G(a))}),1e3))}}(a),U)}else U("image tags required")}else U("image tags required");else U("desciption required");else U("must specify image name")}})}else Y=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:l,prompt:w,hideBackdrop:!0,title:L,buttons:J,content:Y,operatable:C})}var Tc={en:{title:"Reset Instance System",name:"Target System",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u91cd\u7f6e\u4e91\u4e3b\u673a\u7cfb\u7edf",name:"\u76ee\u6807\u7cfb\u7edf",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function Ic(e){var a={image:""},t=e.lang,n=e.instanceID,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!1),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(!1),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(0),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(!0),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState(""),O=Object(H.a)(j,2),w=O[0],_=O[1],N=o.a.useState(!1),R=Object(H.a)(N,2),T=R[0],I=R[1],D=o.a.useState(a),A=Object(H.a)(D,2),P=A[0],F=A[1],B=o.a.useState({images:[]}),M=Object(H.a)(B,2),z=M[0],W=M[1],q=Tc[t],L=q.title,U=o.a.useCallback((function(e){T&&(S(!0),_(e))}),[T]),G=function(){_(""),F(a),m(!1)},V=function(e){T&&(S(!0),G(),r(e.id))},$=function(e){T&&(g(!0),y(0),setTimeout((function(){Z(e)}),1e3))},Z=function e(a){if(T){va(a,V,U,(function(t){T&&(y(t),setTimeout((function(){e(a)}),1e3))}))}};o.a.useEffect((function(){if(l){I(!0);return _a((function(e){if(T){var a=[];e.forEach((function(e){var t=e.id,n=e.name;a.push({value:t,label:n})})),W({images:a}),m(!0)}}),U),function(){I(!1)}}}),[l,T,U]);var Y,J,Q=[{color:"transparent",label:q.cancel,onClick:function(){G(),c()}}];if(u)if(b)Y=o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ne.a,{item:!0,xs:12},o.a.createElement(Ml.a,{variant:"determinate",value:v})),o.a.createElement(Ne.a,{item:!0,xs:12},o.a.createElement(X.a,{align:"center"},v.toFixed(2)+"%")));else{var K=[{type:"select",label:q.name,onChange:(J="image",function(e){if(T){var a=e.target.value;F((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},J,a))}))}}),value:P.image,options:z.images,required:!0,oneRow:!0,xs:12,sm:10}];Y=o.a.createElement(Ae,{inputs:K}),Q.push({color:"info",label:q.confirm,onClick:function(){_(""),S(!1);var e=P.image;""!==e?function(e,a,t,n){Ga("/guests/"+e+"/system/",{from_image:a},()=>{t(e)},n)}(n,e,$,U):U("select a target system")}})}else Y=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"xs",open:l,prompt:w,hideBackdrop:!0,title:L,buttons:Q,content:Y,operatable:C})}var Dc={en:{title:"Migrate Single Instance",sourcePool:"Source Pool",sourceCell:"Source Cell",targetCell:"Target Cell",offline:"Offline",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u8fc1\u79fb\u4e91\u4e3b\u673a\u5b9e\u4f8b",sourcePool:"\u6e90\u8d44\u6e90\u6c60",sourceCell:"\u6e90\u8282\u70b9",targetCell:"\u76ee\u6807\u8282\u70b9",offline:"\u79bb\u7ebf",cancel:"\u53d6\u6d88",confirm:"\u786e\u8ba4"}};function Ac(e){var a={targetCell:""},t=e.lang,n=e.instanceID,l=e.open,r=e.sourcePool,c=e.sourceCell,i=e.onSuccess,s=e.onCancel,u=o.a.useState(!1),m=Object(H.a)(u,2),p=m[0],f=m[1],b=o.a.useState(!0),g=Object(H.a)(b,2),h=g[0],E=g[1],v=o.a.useState(""),y=Object(H.a)(v,2),x=y[0],k=y[1],C=o.a.useState(!1),S=Object(H.a)(C,2),j=S[0],O=S[1],w=o.a.useState(a),_=Object(H.a)(w,2),N=_[0],R=_[1],T=o.a.useState({cells:[]}),I=Object(H.a)(T,2),D=I[0],A=I[1],P=Dc[t],F=P.title,B=o.a.useCallback((function(e){j&&(E(!0),k(e))}),[j]),M=function(){k(""),R(a),f(!1)},z=function(e){j&&(E(!0),M(),i(e))};o.a.useEffect((function(){if(l){O(!0);return pa(r,(function(e){if(j){var a=[];e.forEach((function(e){var t;e.name!==c&&(t=e.alive?e.name+"("+e.address+")":e.name+"("+P.offline+")",a.push({label:t,value:e.name,disabled:!e.alive}))})),0!==a.length?(A({cells:a}),f(!0)):B("no target cell available")}}),B),function(){O(!1)}}}),[j,l,r,c,B,P.offline]);var W,q,L=[{color:"transparent",label:P.cancel,onClick:function(){M(),s()}}];if(p){var U=[{type:"text",label:P.sourcePool,value:r,disabled:!0,oneRow:!0,xs:12,sm:8},{type:"text",label:P.sourceCell,value:c,disabled:!0,oneRow:!0,xs:12,sm:8},{type:"select",label:P.targetCell,onChange:(q="targetCell",function(e){if(j){var a=e.target.value;R((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},q,a))}))}}),value:N.targetCell,options:D.cells,required:!0,oneRow:!0,xs:12,sm:10}];W=o.a.createElement(Ae,{inputs:U}),L.push({color:"info",label:P.confirm,onClick:function(){var e=N.targetCell;""!==e?(k(""),E(!1),function(e,a,t,n,o,l){Ha("/migrations/",{source_pool:e,source_cell:a,target_cell:t,instances:[n]},()=>{o(n)},l)}(r,c,e,n,z,B)):B("select a target cell")}})}else W=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"xs",open:l,prompt:x,title:F,buttons:L,content:W,operatable:h})}var Pc=t(595),Fc={en:{title:"Batch Stopping Instance",content1:"Are you sure to stop ",content2:" instance(s)",cancel:"Cancel",confirm:"Confirm",finish:"Finish",fail:"Fail",complete:"Stopped",processing:"Stopping",instance:"Instance",result:"Result"},cn:{title:"\u6279\u91cf\u505c\u6b62\u4e91\u4e3b\u673a",content1:"\u662f\u5426\u505c\u6b62 ",content2:" \u4e2a\u4e91\u4e3b\u673a",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a",finish:"\u5b8c\u6210",fail:"\u5931\u8d25",complete:"\u5df2\u505c\u6b62",processing:"\u505c\u6b62\u4e2d",instance:"\u4e91\u4e3b\u673a",result:"\u5904\u7406\u7ed3\u679c"}};function Bc(e){var a,t,n=0,l=1,r=2,c=e.lang,i=e.targets,s=e.open,u=e.onSuccess,m=e.onCancel,d=o.a.useState(n),p=Object(H.a)(d,2),f=p[0],b=p[1],g=o.a.useState(null),h=Object(H.a)(g,2),E=h[0],v=h[1],y=o.a.useState(!0),x=Object(H.a)(y,2),k=x[0],C=x[1],S=o.a.useState(""),j=Object(H.a)(S,2),O=j[0],w=j[1],_=o.a.useState(!1),N=Object(H.a)(_,2),R=N[0],T=N[1],I=Fc[c],D=I.title,A=o.a.useCallback((function(e){R&&(C(!0),w(e))}),[R]),P={color:"transparent",label:I.cancel,onClick:function(){w(""),b(n),m()}},F={color:"info",label:I.confirm,onClick:function(){if(w(""),C(!1),i&&0!==i.length){var e,a=function a(n){R&&(v(n),setTimeout((function(){Aa(e,a,t,A)}),2e3))},t=function(e){R&&(v(e),r!==f&&(C(!0),b(r)))};!function(e,a,t){if(!e||0===e.length)return void t("target is empty");La("/batch/stop_guest/",{guest:e},(e,n)=>{202===e?a(n.id):t("unexpected status "+e.toString())},t)}(i,(function(o){R&&(n===f&&b(l),Aa(e=o,a,t,A))}),A)}else A("no target selecetd")}},B={color:"info",label:I.finish,onClick:function(){R&&(b(n),w(""),u())}},M=function(e){var a=[];return e?(e.forEach((function(e,t){"fail"===e.status?a.push({id:e.id,text:I.fail+":"+e.error}):"stopped"===e.status?a.push({id:e.id,text:I.complete}):a.push({id:e.id,text:I.processing})})),o.a.createElement(Pc.a,{component:ge.a},o.a.createElement(yn.a,null,o.a.createElement(xn.a,null,o.a.createElement(kn.a,null,o.a.createElement(Sn.a,null,I.instance),o.a.createElement(Sn.a,null,I.result))),o.a.createElement(Cn.a,null,a.map((function(e){return o.a.createElement(kn.a,{key:e.id},o.a.createElement(Sn.a,{component:"th",scope:"row"},e.id),o.a.createElement(Sn.a,null,e.text))})))))):o.a.createElement("div",null)};switch(o.a.useEffect((function(){return T(!0),function(){return T(!1)}}),[]),f){case l:a=M(E),t=O?[P]:[];break;case r:a=M(E),t=[B];break;default:a=I.content1+i.length.toString()+I.content2,t=[P,F]}return o.a.createElement(Xe,{size:"sm",open:s,prompt:O,promptPosition:"top",hideBackdrop:!0,title:D,buttons:t,content:a,operatable:k})}var Mc={en:{title:"Batch Deleting Instance",content1:"Are you sure to delete ",content2:" instance(s)",cancel:"Cancel",confirm:"Confirm",finish:"Finish",fail:"Fail",complete:"Deleted",processing:"Deleting",instance:"Instance",result:"Result"},cn:{title:"\u6279\u91cf\u5220\u9664\u4e91\u4e3b\u673a",content1:"\u662f\u5426\u5220\u9664 ",content2:" \u4e2a\u4e91\u4e3b\u673a",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a",finish:"\u5b8c\u6210",fail:"\u5931\u8d25",complete:"\u5df2\u5220\u9664",processing:"\u5220\u9664\u4e2d",instance:"\u4e91\u4e3b\u673a",result:"\u5904\u7406\u7ed3\u679c"}};function zc(e){var a,t,n=0,l=1,r=2,c=e.lang,i=e.targets,s=e.open,u=e.onSuccess,m=e.onCancel,d=o.a.useState(n),p=Object(H.a)(d,2),f=p[0],b=p[1],g=o.a.useState(null),h=Object(H.a)(g,2),E=h[0],v=h[1],y=o.a.useState(!0),x=Object(H.a)(y,2),k=x[0],C=x[1],S=o.a.useState(""),j=Object(H.a)(S,2),O=j[0],w=j[1],_=o.a.useState(!1),N=Object(H.a)(_,2),R=N[0],T=N[1],I=Mc[c],D=I.title,A=o.a.useCallback((function(e){R&&(C(!0),w(e))}),[R]),P={color:"transparent",label:I.cancel,onClick:function(){w(""),b(n),m()}},F={color:"info",label:I.confirm,onClick:function(){if(w(""),C(!1),i&&0!==i.length){var e,a=function a(n){R&&(v(n),setTimeout((function(){Da(e,a,t,A)}),2e3))},t=function(e){R&&(v(e),r!==f&&(C(!0),b(r)))};!function(e,a,t){if(!e||0===e.length)return void t("target is empty");La("/batch/delete_guest/",{guest:e},(e,n)=>{202===e?a(n.id):t("unexpected status "+e.toString())},t)}(i,(function(o){R&&(n===f&&b(l),Da(e=o,a,t,A))}),A)}else A("no target selecetd")}},B={color:"info",label:I.finish,onClick:function(){R&&(b(n),w(""),u())}},M=function(e){var a=[];return e?(e.forEach((function(e,t){"fail"===e.status?a.push({id:e.id,text:I.fail+":"+e.error}):"deleted"===e.status?a.push({id:e.id,text:I.complete}):a.push({id:e.id,text:I.processing})})),o.a.createElement(Pc.a,{component:ge.a},o.a.createElement(yn.a,null,o.a.createElement(xn.a,null,o.a.createElement(kn.a,null,o.a.createElement(Sn.a,null,I.instance),o.a.createElement(Sn.a,null,I.result))),o.a.createElement(Cn.a,null,a.map((function(e){return o.a.createElement(kn.a,{key:e.id},o.a.createElement(Sn.a,{component:"th",scope:"row"},e.id),o.a.createElement(Sn.a,null,e.text))})))))):o.a.createElement("div",null)};switch(o.a.useEffect((function(){return T(!0),function(){return T(!1)}}),[]),f){case l:a=M(E),t=O?[P]:[];break;case r:a=M(E),t=[B];break;default:a=I.content1+i.length.toString()+I.content2,t=[P,F]}return o.a.createElement(Xe,{size:"sm",open:s,prompt:O,promptPosition:"top",hideBackdrop:!0,title:D,buttons:t,content:a,operatable:k})}const Wc={en:{title:"Batch Creating Instances",rule:"Name Rule",ruleOrder:"By Order",ruleMAC:"By MAC",ruleAddress:"By Guest Address",prefix:"Prefix",count:"Create Quantity",resourcePool:"Resource Pool",core:"Core",memory:"Memory",systemDisk:"System Disk Size",dataDisk:"Data Disk Size",autoStartup:"Automatic Startup",systemVersion:"System Version",sourceImage:"Source Image",blankSystem:"Blank System",qos:"QoS Options (Optional)",cpuPriority:"CPU Priority",iops:"IOPS",outbound:"Outband Bandwidth",inbound:"Inbound Bandwidth",noDataDisk:"Don't use data disk",noLimit:"No Limit",cpuPriorityHigh:"High",cpuPriorityMedium:"Medium",cpuPriorityLow:"Low",modules:"Pre-Installed Modules",adminName:"Admin Name",adminPassword:"Admin Password",blankHelper:"Leave blank to generate",dataPath:"Data Path",off:"Off",on:"On",cancel:"Cancel",confirm:"Confirm",finish:"Finish",fail:"Fail",complete:"Created",processing:"Creating",instance:"Instance",result:"Result"},cn:{title:"\u6279\u91cf\u521b\u5efa\u4e91\u4e3b\u673a",rule:"\u5b9e\u4f8b\u547d\u540d\u89c4\u5219",ruleOrder:"\u987a\u5e8f\u9012\u589e",ruleMAC:"\u6309MAC\u5730\u5740",ruleAddress:"\u6309\u5b9e\u4f8b\u5730\u5740",prefix:"\u5b9e\u4f8b\u540d\u524d\u7f00",count:"\u521b\u5efa\u6570\u91cf",resourcePool:"\u8ba1\u7b97\u8d44\u6e90\u6c60",core:"\u6838\u5fc3\u6570",memory:"\u5185\u5b58",systemDisk:"\u7cfb\u7edf\u78c1\u76d8\u5bb9\u91cf",dataDisk:"\u6570\u636e\u78c1\u76d8\u5bb9\u91cf",autoStartup:"\u81ea\u52a8\u542f\u52a8",systemVersion:"\u7cfb\u7edf\u7248\u672c",sourceImage:"\u6765\u6e90\u955c\u50cf",blankSystem:"\u7a7a\u767d\u7cfb\u7edf",qos:"QoS\u9009\u9879 (\u53ef\u9009)",cpuPriority:"CPU\u4f18\u5148\u7ea7",iops:"\u78c1\u76d8\u8bfb\u5199\u9650\u5236",outbound:"\u4e0a\u884c\u5e26\u5bbd",inbound:"\u4e0b\u884c\u5e26\u5bbd",noDataDisk:"\u4e0d\u4f7f\u7528\u6570\u636e\u78c1\u76d8",noLimit:"\u65e0\u9650\u5236",cpuPriorityHigh:"\u9ad8",cpuPriorityMedium:"\u4e2d",cpuPriorityLow:"\u4f4e",modules:"\u9884\u88c5\u6a21\u5757",adminName:"\u7ba1\u7406\u5458\u8d26\u53f7",adminPassword:"\u7ba1\u7406\u5458\u5bc6\u7801",blankHelper:"\u7559\u7a7a\u5219\u81ea\u52a8\u751f\u6210\u65b0\u5bc6\u7801",dataPath:"\u6302\u8f7d\u6570\u636e\u8def\u5f84",off:"\u5173\u95ed",on:"\u5f00\u542f",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a",finish:"\u5b8c\u6210",fail:"\u5931\u8d25",complete:"\u521b\u5efa\u5b8c\u6210",processing:"\u521b\u5efa\u4e2d",instance:"\u4e91\u4e3b\u673a",result:"\u5904\u7406\u7ed3\u679c"}};function qc(e){const a=0,t=1,n=2,l="order",r="MAC",c="address",{lang:i,open:s,onSuccess:u,onCancel:m}=e,d={rule:l,prefix:"",count:1,name:"",pool:"",cores:1..toString(),memory:(1<<30).toString(),system_disk:5,data_disk:0,auto_start:!1,system_template:"",from_image:"__default",modules:new Map,module_cloud_init_admin_name:"root",module_cloud_init_admin_password:"",module_cloud_init_data_path:"/opt/data",priority:"medium",iops:0,inbound:0,outbound:0},[p,f]=o.a.useState(!1),[b,g]=o.a.useState(a),[h,E]=o.a.useState(null),[v,y]=o.a.useState(!0),[x,k]=o.a.useState(""),[C,S]=o.a.useState(!1),[j,O]=o.a.useState(d),[w,_]=o.a.useState({pools:[],images:[],versions:[]}),[N,R]=o.a.useState(16),[T,I]=o.a.useState(24),[D,A]=o.a.useState(32),[P,F]=o.a.useState(0),[B,M]=o.a.useState(1),z=Wc[i],W=z.title,q=o.a.useCallback(e=>{C&&(y(!0),k(e))},[C]),H=()=>{k(""),O(d),f(!1),g(a)},L=e=>{C&&(k(""),a===b&&g(t),Ia(e,U(e),G(e),q))},U=e=>a=>{C&&(E(a),setTimeout(()=>{Ia(e,U(e),G(e),q)},1e3))},G=e=>e=>{C&&(E(e),n!==b&&(y(!0),g(n)))},V=e=>a=>{if(C){var t=a.target.value;O(a=>({...a,[e]:t}))}},Z=e=>(a,t)=>{C&&O(a=>({...a,[e]:t}))};o.a.useEffect(()=>{if(!s)return;var e=[],a=[{label:z.blankSystem,value:"__default"}],t=[];S(!0);const n=n=>{C&&(n.forEach(e=>{let{id:a,name:n}=e;t.push({label:n,value:a})}),_({pools:e,images:a,versions:t}),f(!0))},o=e=>{C&&(e.forEach(e=>{let{name:t,id:n}=e;a.push({label:t,value:n})}),Pa(n,q))},l=a=>{C&&(a.forEach(a=>{let{name:t}=a;e.push({label:t,value:t})}),_a(o,q))};return za(e=>{C&&(e.max_cores&&e.max_cores>0&&R(e.max_cores),e.max_memory&&e.max_memory>0&&I(e.max_memory),e.max_disk&&e.max_disk>0&&A(e.max_disk),da(l,q))},q),()=>{S(!1)}},[C,s,z.blankSystem,q]);const Y=e=>{if(C){var a=[];return e?(e.forEach((e,t)=>{let n;if("fail"===e.status)n=z.fail+":"+e.error;else if("created"===e.status)n=z.complete;else{const a=e.progress;n=o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:8,sm:9,md:10},o.a.createElement(Ml.a,{variant:"determinate",value:a})),o.a.createElement(Ve,{xs:4,sm:3,md:2},o.a.createElement(X.a,{align:"center"},a.toFixed(2)+"%")))}a.push({name:e.name,content:n})}),o.a.createElement(Pc.a,{component:ge.a},o.a.createElement(yn.a,null,o.a.createElement(xn.a,null,o.a.createElement(kn.a,null,o.a.createElement(Sn.a,null,z.instance),o.a.createElement(Sn.a,null,z.result))),o.a.createElement(Cn.a,null,a.map(e=>o.a.createElement(kn.a,{key:e.name},o.a.createElement(Sn.a,{component:"th",scope:"row"},e.name),o.a.createElement(Sn.a,null,e.content))))))):o.a.createElement("div",null)}},J={color:"transparent",label:z.cancel,onClick:()=>{H(),m()}},Q={color:"info",label:z.confirm,onClick:()=>{if(k(""),y(!1),!j.prefix)return void q("prefix required");var e=Number.parseInt(j.count);if(Number.isNaN(e))return void q("invalid count: "+j.count);if(!j.pool)return void q("must specify target pool");var a=Number.parseInt(j.cores);if(Number.isNaN(a))return void q("invalid cores: "+j.cores);var t=Number.parseInt(j.memory);if(Number.isNaN(t))return void q("invalid memory: "+j.memory);var n=[j.system_disk*(1<<30)];0!==j.data_disk&&n.push(j.data_disk*(1<<30));var o=j.system_template;let l;l="__default"===j.from_image?"":j.from_image;var r=[],c=!1;j.modules.forEach((e,a)=>{e&&(r.push(a),"cloud-init"===a&&(c=!0))});var i=null;c&&(i={admin_name:j.module_cloud_init_admin_name,admin_secret:j.module_cloud_init_admin_password,data_path:j.module_cloud_init_data_path});var s={cpu_priority:j.priority,write_iops:j.iops,read_iops:j.iops,receive_speed:j.inbound*(1<<17),send_speed:j.outbound*(1<<17)};!function(e,a,t,n,o,l,r,c,i,s,u,m,d,p,f){var b=ta();if(null===b)return void f("session expired");var g={name_rule:e,count:t,owner:b.user,group:b.group,pool:n,cores:o,memory:l,disks:r,auto_start:c,from_image:i,template:s};a&&(g.name_prefix=a),u&&(g.modules=u),m&&(g.cloud_init=m),d&&(g.qos=d),La("/batch/create_guest/",g,(e,a)=>{202===e?p(a.id):f("unexpected status "+e.toString())},f)}(j.rule,j.prefix,e,j.pool,a,t,n,j.auto_start,l,o,r,i,s,L,q)}},K={color:"info",label:z.finish,onClick:()=>{C&&(H(),u())}};let ee,ae;if(p)switch(b){case t:ee=Y(h),ae=x?[J]:[];break;case n:ee=Y(h),ae=[K];break;default:ae=[J,Q];const e=e=>{let a=2**e;return a>N&&(a=N),a.toString()},a=(e,a)=>{if(!C)return;F(a);let t=2**a;t>N&&(t=N),O(e=>({...e,cores:t}))};let s=Math.ceil(Math.sqrt(N)),u=0,m=[];for(let t=u+1;t<=s;++t){let e=2**t;e>N&&(e=N),m.push({value:t,label:e.toString()})}let d={type:"slider",label:z.core+" - ".concat(j.cores),onChange:a,valueLabelFormat:e,value:P,oneRow:!0,maxStep:s,minStep:u,step:1,marks:m,xs:12,sm:6,md:4};const p=512,f=1<<20,b=1<<30,g=e=>{let a;return a=e>=b?e/b+" GB":e/f+" MB",a},E=(e,a)=>{if(!C)return;M(a);let t=2**a*p*f;t>T*b&&(t=T*b),O(e=>({...e,memory:t}))};let v=Math.ceil(Math.sqrt(T)),y=0,k=[];for(let t=y+1;t<=v;++t){let e=2**t*p*f;e>T*b&&(e=T*b),k.push({value:t,label:g(e)})}let S,_,R,I,A={type:"slider",label:z.memory+" - ".concat(g(j.memory)),onChange:E,valueLabelDisplay:"off",value:B,oneRow:!0,maxStep:v,minStep:y,step:1,marks:k,xs:12,sm:8,md:6},W=z.systemDisk+" - ".concat(j.system_disk," GB");{const e=1,a=5,t=D;var te=[];[a,t>>1,t].forEach(e=>{te.push({value:e,label:e+" GB"})}),S=o.a.createElement(Ie.a,{color:"secondary",value:j.system_disk,max:t,min:a,step:e,valueLabelDisplay:"auto",marks:te,onChange:Z("system_disk")})}{const e=0,a=2,t=D;let n=[];[e,t>>1,t].forEach(e=>{n.push({value:e,label:e+" GB"})}),R=0===j.data_disk?z.dataDisk+" - "+z.noDataDisk:z.dataDisk+" - ".concat(j.data_disk," GB"),_=o.a.createElement(Ie.a,{color:"secondary",value:j.data_disk,max:t,min:e,step:a,valueLabelDisplay:"auto",marks:n,onChange:Z("data_disk")})}if(j.system_template&&"__default"!==j.from_image){var ne=[{value:"qemu",label:"QEMU-Guest-Agent"},{value:"cloud-init",label:"CloudInit"}];let e;e=j.modules.get("cloud-init")?o.a.createElement(Ve,{xs:12,sm:8,md:6},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(we.a,{component:"fieldset",fullWidth:!0},o.a.createElement(_e.a,{component:"legend"},"Cloud Init Options"),o.a.createElement(kc,null,o.a.createElement(Ce.a,{label:z.adminName,onChange:V("module_cloud_init_admin_name"),value:j.module_cloud_init_admin_name,margin:"normal",fullWidth:!0})),o.a.createElement(kc,null,o.a.createElement(Ce.a,{label:z.adminPassword,onChange:V("module_cloud_init_admin_password"),helperText:z.blankHelper,margin:"normal",fullWidth:!0})),o.a.createElement(kc,null,o.a.createElement(Ce.a,{label:z.dataPath,onChange:V("module_cloud_init_data_path"),value:j.module_cloud_init_data_path,margin:"normal",disabled:!0,fullWidth:!0}))))):o.a.createElement(Ve,null),I=o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(we.a,{component:"fieldset",fullWidth:!0},o.a.createElement(_e.a,{component:"legend"},z.modules),o.a.createElement(Re.a,null,o.a.createElement(Ne.a,{container:!0},ne.map(e=>{let a;return a=!!j.modules.has(e.value)&&j.modules.get(e.value),o.a.createElement(Ve,{xs:12,sm:6,md:4,key:e.value},o.a.createElement(Oe.a,{control:o.a.createElement(Te.a,{checked:a,onChange:(t="modules",n=e.value,e=>{if(C){var a=e.target.checked;O(e=>({...e,[t]:e[t].set(n,a)}))}}),value:e.value}),label:e.label}));var t,n})))))),e)}else I=o.a.createElement(Ve,null);const q=[{value:"high",label:z.cpuPriorityHigh},{value:"medium",label:z.cpuPriorityMedium},{value:"low",label:z.cpuPriorityLow}],H=2e3;let L=[];[0,H/2,H].forEach(e=>{L.push({value:e,label:e.toString()})});const U=20;let G,X,le,re=[];[0,U/2,U].forEach(e=>{re.push({value:e,label:0===e?z.noLimit:e.toString()+" Mbit/s"})}),G=0===j.iops?z.iops+" - "+z.noLimit:"cn"===i?z.iops+" - "+j.iops+"\u6b21\u8bf7\u6c42/\u79d2":z.iops+" - "+j.iops+" Operates/Second",X=0===j.inbound?z.inbound+" - "+z.noLimit:z.inbound+" - "+j.inbound+" Mbit/s",le=0===j.outbound?z.outbound+" - "+z.noLimit:z.outbound+" - "+j.outbound+" Mbit/s";const ce=[{type:"radio",label:z.cpuPriority,onChange:V("priority"),value:j.priority,oneRow:!0,options:q,xs:12},{type:"slider",label:G,onChange:Z("iops"),value:j.iops,oneRow:!0,maxStep:H,minStep:0,step:10,marks:L,xs:12},{type:"slider",label:X,onChange:Z("inbound"),value:j.inbound,oneRow:!0,maxStep:U,minStep:0,step:2,marks:re,xs:12},{type:"slider",label:le,onChange:Z("outbound"),value:j.outbound,oneRow:!0,maxStep:U,minStep:0,step:2,marks:re,xs:12}];ee=o.a.createElement(Ne.a,{container:!0},o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(we.a,{component:"fieldset",fullWidth:!0},o.a.createElement(_e.a,{component:"legend"},z.rule),o.a.createElement(je.a,{"aria-label":z.rule,name:"rule",value:j.rule,onChange:V("rule"),row:!0},o.a.createElement($.a,{display:"flex"},o.a.createElement($.a,null,o.a.createElement(Oe.a,{value:l,control:o.a.createElement(Se.a,null),label:z.ruleOrder})),o.a.createElement($.a,null,o.a.createElement(Oe.a,{value:r,control:o.a.createElement(Se.a,{disabled:!0}),label:z.ruleMAC})),o.a.createElement($.a,null,o.a.createElement(Oe.a,{value:c,control:o.a.createElement(Se.a,{disabled:!0}),label:z.ruleAddress})))))))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:6},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(_e.a,{component:"legend"},z.count),o.a.createElement(Ie.a,{color:"secondary",value:j.count,max:20,min:1,step:1,valueLabelDisplay:"auto",marks:[{value:1,label:"1"},{value:10,label:"10"},{value:20,label:"20"}],onChange:Z("count")})))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:6,md:4},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:z.prefix,onChange:V("prefix"),value:j.prefix,margin:"normal",required:!0,fullWidth:!0})))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:10,sm:4,md:3},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(ke.a,{htmlFor:"pool"},z.resourcePool),o.a.createElement(ye.a,{value:j.pool,onChange:V("pool"),inputProps:{name:"pool",id:"pool"},required:!0,fullWidth:!0},w.pools.map((e,a)=>o.a.createElement(pe.a,{value:e.value,key:a},e.label)))))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(we.a,{component:"fieldset",fullWidth:!0},o.a.createElement(De,d)))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(we.a,{component:"fieldset",fullWidth:!0},o.a.createElement(De,A))))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:6,md:4},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(_e.a,{component:"legend"},W),S))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:6,md:4},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(_e.a,{component:"legend"},R),_))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:10,sm:6,md:5},o.a.createElement($.a,{m:0,pb:2},o.a.createElement(ke.a,{htmlFor:"image"},z.sourceImage),o.a.createElement(ye.a,{value:j.from_image,onChange:V("from_image"),inputProps:{name:"image",id:"image"},fullWidth:!0},w.images.map((e,a)=>o.a.createElement(pe.a,{value:e.value,key:a},e.label)))))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:10,sm:5,md:4},o.a.createElement($.a,{m:0,pb:2},o.a.createElement(ke.a,{htmlFor:"version"},z.systemVersion),o.a.createElement(ye.a,{value:j.system_template,onChange:V("system_template"),inputProps:{name:"version",id:"version"},fullWidth:!0},w.versions.map((e,a)=>o.a.createElement(pe.a,{value:e.value,key:a},e.label)))))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:8,sm:6,md:4},o.a.createElement($.a,{m:0,pb:2},o.a.createElement(ke.a,{htmlFor:"auto_start"},z.autoStartup),z.off,o.a.createElement(xe.a,{checked:j.failover,onChange:(oe="auto_start",e=>{if(C){var a=e.target.checked;O(e=>({...e,[oe]:a}))}}),color:"primary",inputProps:{name:"auto_start",id:"auto_start"}}),z.on))),I,o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:9,md:7},o.a.createElement($.a,{m:0,pb:2},o.a.createElement(gc.a,null,o.a.createElement(hc.a,{expandIcon:o.a.createElement(yc.a,null)},z.qos),o.a.createElement(Ec.a,null,o.a.createElement($.a,{m:1,pt:2},o.a.createElement(Ae,{inputs:ce}))))))))}else ee=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}}),ae=[J];var oe;return o.a.createElement(Xe,{size:"md",open:s,prompt:x,promptPosition:"top",hideBackdrop:!0,title:W,buttons:ae,content:ee,operatable:v})}var Hc=Object(m.a)({cardCategoryWhite:{"&,& a,& a:hover,& a:focus":{color:"rgba(255,255,255,.62)",margin:"0",fontSize:"14px",marginTop:"0",marginBottom:"0"},"& a,& a:hover,& a:focus":{color:"#FFFFFF"}},cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),Lc={en:{createButton:"Create New Instance",batchCreate:"Batch Create",batchDelete:"Batch Delete",batchStop:"Batch Stop",enterBatch:"Enter Batch Mode",exitBatch:"Exit Batch Mode",tableTitle:"Cloud Instances",name:"Name",cell:"Host Cell",address:"Address",core:"Core",memory:"Memory",disk:"Disk",status:"Status",operates:"Operates",noResource:"No instances available",computePools:"Compute Pools"},cn:{createButton:"\u521b\u5efa\u4e91\u4e3b\u673a",batchCreate:"\u6279\u91cf\u521b\u5efa",batchDelete:"\u6279\u91cf\u5220\u9664",batchStop:"\u6279\u91cf\u505c\u6b62",enterBatch:"\u8fdb\u5165\u6279\u91cf\u6a21\u5f0f",exitBatch:"\u9000\u51fa\u6279\u91cf\u6a21\u5f0f",tableTitle:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",name:"\u540d\u79f0",cell:"\u627f\u8f7d\u8282\u70b9",address:"\u5730\u5740",core:"\u6838\u5fc3",memory:"\u5185\u5b58",disk:"\u78c1\u76d8",status:"\u72b6\u6001",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u4e91\u4e3b\u673a\u5b9e\u4f8b",computePools:"\u8ba1\u7b97\u8d44\u6e90\u6c60"}};function Uc(e){var a=Hc(),t=o.a.useState(null),n=Object(H.a)(t,2),l=n[0],r=n[1],c=o.a.useState(new Map),i=Object(H.a)(c,2),s=i[0],m=i[1],d=o.a.useState(!1),p=Object(H.a)(d,2),f=p[0],b=p[1],g=Object(u.g)(),h=new URLSearchParams(g.search),E=h.get("pool"),v=h.get("cell"),y=o.a.useState(!1),x=Object(H.a)(y,2),k=x[0],C=x[1],S=o.a.useState(!1),j=Object(H.a)(S,2),O=j[0],w=j[1],_=o.a.useState(!1),N=Object(H.a)(_,2),R=N[0],T=N[1],I=o.a.useState(!1),D=Object(H.a)(I,2),A=D[0],P=D[1],F=o.a.useState(!1),B=Object(H.a)(F,2),M=B[0],z=B[1],W=o.a.useState(!1),q=Object(H.a)(W,2),L=q[0],U=q[1],G=o.a.useState(!1),V=Object(H.a)(G,2),Z=V[0],Y=V[1],J=o.a.useState(!1),Q=Object(H.a)(J,2),K=Q[0],ee=Q[1],ae=o.a.useState(!1),te=Object(H.a)(ae,2),ne=te[0],oe=te[1],le=o.a.useState(!1),re=Object(H.a)(le,2),ce=re[0],ie=re[1],se=o.a.useState(""),me=Object(H.a)(se,2),de=me[0],pe=me[1],fe=o.a.useState(""),be=Object(H.a)(fe,2),ge=be[0],he=be[1],Ee=o.a.useState(""),ye=Object(H.a)(Ee,2),xe=ye[0],ke=ye[1],Ce=o.a.useState("warning"),Se=Object(H.a)(Ce,2),je=Se[0],Oe=Se[1],we=o.a.useState(""),_e=Object(H.a)(we,2),Ne=_e[0],Re=_e[1],Ie=function(){Re("")},De=o.a.useCallback((function(e){Oe("warning"),Re(e),setTimeout(Ie,3e3)}),[Oe,Re]),Ae=function(e){Oe("info"),Re(e),Ma(e),setTimeout(Ie,3e3)},Pe=o.a.useCallback((function(){Ea(E,v,(function(e){var a=s,t=!1;if(e){r(e);var n=[];a.forEach((function(a,t){e.some((function(e){return e.id===t}))||n.push(t)})),e.forEach((function(e){var n=e.id;a.has(n)||(a.set(n,!1),t||(t=!0))})),0!==n.length&&n.forEach((function(e){a.delete(e),t||(t=!0)}))}else r([]),0!==a.size&&(a.clear(),t=!0);t&&m(new Map(a))}),(function(e){De(e),na()}))}),[E,v,s,De]),Fe=function(e){w(!0),pe(e)},Be=function(){w(!1)},Me=function(e){T(!0),pe(e)},ze=function(){T(!1)},We=function(e){P(!0),pe(e)},qe=function(){P(!1)},He=function(e){z(!0),pe(e)},Le=function(){z(!1)},Ue=function(e){U(!0),pe(e)},Ge=function(){U(!1)},$e=function(e,a,t){Y(!0),pe(e),he(a),ke(t)},Ze=function(){Y(!1)},Ye=function(){C(!1)},Je=function(){ee(!1)},Qe=function(){oe(!1)},Ke=function(){ie(!1)},Xe=function(){Pe()},ea=function(e,a){var t=new Map(s);t.set(a,e),m(t)};if(o.a.useEffect((function(){var e=!0;Pe();var a=setInterval((function(){e&&Pe()}),5e3);return function(){e=!1,clearInterval(a)}}),[Pe]),!E)return console.log("pool name omit"),oa();if(null===ta())return oa();var aa,la=e.lang,ra=Lc[la];if(null===l)aa=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});else if(0===l.length)aa=o.a.createElement($.a,{textAlign:"center"},o.a.createElement(gn,null,ra.noResource));else{var ca;ca=f?o.a.createElement($.a,{display:"flex",alignItems:"center"},o.a.createElement($.a,null,o.a.createElement(Te.a,{onChange:function(e){var a,t=e.target.checked,n=new Map,o=Object(wr.a)(s.keys());try{for(o.s();!(a=o.n()).done;){var l=a.value;n.set(l,t)}}catch(r){o.e(r)}finally{o.f()}m(n)}})),o.a.createElement($.a,null,ra.name)):ra.name,aa=o.a.createElement(Pr,{color:"primary",headers:[ca,ra.cell,ra.address,ra.core,ra.memory,ra.disk,ra.status,ra.operates],rows:l.map((function(e){var a=e.id;return o.a.createElement(pc,{key:e.id,instance:e,lang:la,checked:!(!s||!s.has(a))&&s.get(a),checkable:f,onCheckStatusChanged:ea,onNotify:Ae,onError:De,onDelete:Fe,onMediaStart:Me,onInsertMedia:We,onResetSystem:He,onBuildImage:Ue,onStatusChange:Xe,onMigrateInstance:$e})}))})}var ia=[o.a.createElement(kt.a,{to:"/admin/compute_pools/",key:ra.computePools},ra.computePools)];v?(ia.push(o.a.createElement(kt.a,{to:"/admin/instances/range/?pool="+E,key:E},E)),ia.push(o.a.createElement(X.a,{color:"textPrimary",key:v},v))):ia.push(o.a.createElement(X.a,{color:"textPrimary",key:E},E));var sa=[{label:ra.createButton,icon:Dt.a,color:"info",onClick:function(){C(!0)}},{label:ra.batchCreate,icon:Tr.a,color:"info",onClick:function(){ie(!0)}}];f?sa.push({label:ra.batchDelete,icon:Ht.a,color:"danger",onClick:function(){oe(!0)}},{label:ra.batchStop,icon:Nr.a,color:"info",onClick:function(){ee(!0)}},{label:ra.exitBatch,icon:Dr.a,color:"success",onClick:function(){b(!1)}}):sa.push({label:ra.enterBatch,icon:Xo.a,color:"info",onClick:function(){var e,a=new Map,t=Object(wr.a)(s.keys());try{for(t.s();!(e=t.n()).done;){var n=e.value;a.set(n,!1)}}catch(o){t.e(o)}finally{t.f()}m(a),b(!0)}});var ua=[];return s&&(s.forEach((function(e,a){e&&ua.push(a)})),ua.sort()),o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement(At.a,{separator:"\u203a","aria-label":"breadcrumb"},ia)),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{display:"flex"},sa.map((function(e,a){var t=e.label,n=e.color,l=e.icon,r=e.onClick;return o.a.createElement($.a,{key:a,m:1},o.a.createElement(ue,{size:"sm",color:n,round:!0,onClick:r},o.a.createElement(l),t))})))),o.a.createElement(Ve,{xs:12,sm:12,md:12},o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",{className:a.cardTitleWhite},ra.tableTitle)),o.a.createElement(fn,null,aa))),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:je,message:Ne,open:""!==Ne,closeNotification:Ie,close:!0})),o.a.createElement(Ve,null,o.a.createElement(Sc,{lang:la,open:k,onSuccess:function(e){Ye(),Ae("new instance "+e+" created"),Pe()},onCancel:Ye})),o.a.createElement(Ve,null,o.a.createElement(bc,{lang:la,instanceID:de,open:O,onSuccess:function(e){Be(),Ae("instance "+e+" deleted"),Pe()},onCancel:Be})),o.a.createElement(Ve,null,o.a.createElement(Oc,{lang:la,instanceID:de,open:R,onSuccess:function(e){ze(),Ae("instance "+e+" started with media"),Pe()},onCancel:ze})),o.a.createElement(Ve,null,o.a.createElement(_c,{lang:la,instanceID:de,open:A,onSuccess:function(e){qe(),Ae("instance "+e+" started with media"),Pe()},onCancel:qe})),o.a.createElement(Ve,null,o.a.createElement(Ic,{lang:la,instanceID:de,open:M,onSuccess:function(e){Le(),Ae("guest system of "+e+" reset")},onCancel:Le})),o.a.createElement(Ve,null,o.a.createElement(Rc,{lang:la,instanceID:de,open:L,onSuccess:function(e){Ge(),Ae("new image "+e+" created from "+de)},onCancel:Ge})),o.a.createElement(Ve,null,o.a.createElement(Ac,{lang:la,instanceID:de,sourcePool:ge,sourceCell:xe,open:Z,onSuccess:function(e){Ze(),Ae("instance "+e+" migrated"),Pe()},onCancel:Ze})),o.a.createElement(Ve,null,o.a.createElement(Bc,{lang:la,open:K,targets:K?ua:[],onSuccess:function(){Je(),Pe()},onCancel:Je})),o.a.createElement(Ve,null,o.a.createElement(zc,{lang:la,open:ne,targets:ne?ua:[],onSuccess:function(){Qe(),Pe()},onCancel:Qe})),o.a.createElement(Ve,null,o.a.createElement(qc,{lang:la,open:ce,onSuccess:function(){Ke(),Pe()},onCancel:Ke})))}var Gc={en:{title:"Instance",cores:"Cores",memory:"Memory",disks:"Disk",autoStartup:"Auto Startup",internalAddress:"Internal Address",externalAddress:"External Address",ioUsage:"IO Usage",stopped:"Stopped ",running:"Running ",used:"Used ",available:"Available ",coresUsed:"Cores Used",throughput:"Throughput",receive:"Receive ",send:"Send ",write:"Write ",read:"Read ",flags:"Running Flags",mediaAttached:"Media Attached"},cn:{title:"\u4e91\u4e3b\u673a",cores:"\u6838\u5fc3\u6570",memory:"\u5185\u5b58",disks:"\u78c1\u76d8",autoStartup:"\u5f00\u673a\u542f\u52a8",internalAddress:"\u5185\u90e8\u5730\u5740",externalAddress:"\u5916\u90e8\u5730\u5740",ioUsage:"IO\u541e\u5410\u91cf",stopped:"\u505c\u6b62",running:"\u8fd0\u884c",used:"\u5df2\u7528",available:"\u53ef\u7528",coresUsed:"\u6838\u5fc3\u5df2\u5360\u7528",throughput:"\u541e\u5410\u91cf",receive:"\u63a5\u6536",send:"\u53d1\u9001",write:"\u5199\u5165",read:"\u8bfb\u53d6",flags:"\u8fd0\u884c\u6807\u5fd7",mediaAttached:"\u5df2\u6302\u8f7d\u5a92\u4f53"}},Vc={borderRadius:"3px",marginTop:"-20px",padding:"15px"},$c=Object(d.a)(Object(d.a)(Object(d.a)({},Qn),z),{},{cardWithDivider:{borderTop:"1px solid "+C[10]},coresChart:Object(d.a)(Object(d.a)({},Vc),{},{background:y[0]}),memoryChart:Object(d.a)(Object(d.a)({},Vc),{},{background:x[0]}),networkChart:Object(d.a)(Object(d.a)({},Vc),{},{background:E[0]}),diskChart:Object(d.a)(Object(d.a)({},Vc),{},{background:k[0]}),disableChart:Object(d.a)(Object(d.a)({},Vc),{},{background:C[5]})}),Zc=Object(m.a)($c),Yc=function(e){var a,t,n,l=e.lang,r=e.status,c=Zc(),i=Gc[l];if(a=r.running?o.a.createElement(X.a,{component:"span",className:c.cardTitle},o.a.createElement(Br.a,{className:c.successText}),i.title+": "+r.name+" ( "+i.running+" )"):o.a.createElement(X.a,{component:"span",className:c.cardTitle},o.a.createElement(zr.a,{className:c.mutedText}),i.title+": "+r.name+" ( "+i.stopped+" )"),r.running){var s={label:i.coresUsed,color:"#FFF",data:[]},u=0;r.coreRecords.forEach((function(e){s.data.push(e.current),u=Math.max(u,e.max)}));var m=o.a.createElement(Ve,{xs:8,sm:6,md:3,key:"cores-usage"},o.a.createElement($.a,{m:0,p:0,className:c.coresChart,boxShadow:2},o.a.createElement(Xn,{series:[s],minTickStep:1,maxValue:100,maxTicks:5,displayValue:function(e){return e.toString()+"%"}}))),d={label:i.used+i.memory,color:C[4],data:[]},p={label:i.available+i.memory,color:y[1],data:[]};r.memoryRecords.forEach((function(e){d.data.push(e.used),p.data.push(e.available)}));var f=o.a.createElement(Ve,{xs:8,sm:6,md:3,key:"memory-usage"},o.a.createElement($.a,{m:0,p:0,className:c.memoryChart,boxShadow:2},o.a.createElement(ao,{series:[d,p],minTickStep:1024,displayValue:function(e){return 0===e?"0":e>=1024?0===e%1024?(e/1024).toString()+" GB":(e/1024).toFixed(2)+" GB":e.toString()+" MB"}}))),b={label:i.receive+i.throughput,color:x[3],data:[]},g={label:i.send+i.throughput,color:h[1],data:[]};r.networkSpeed.forEach((function(e){b.data.push(ca(e.receive/(1<<20),2)),g.data.push(ca(e.send/(1<<20),2))}));var E=function(e){return e>=1024?0===e%1024?(e/1024).toString()+" GB/s":(e/1024).toFixed(2)+" GB/s":Number.isInteger(e)?e.toString()+" MB/s":e.toFixed(2)+" MB/s"},v=[b,g],k=o.a.createElement(Ve,{xs:8,sm:6,md:3,key:"network-usage"},o.a.createElement($.a,{m:0,p:0,className:c.networkChart,boxShadow:2},o.a.createElement(no,{series:v,displayValue:E,minTickStep:1}))),S={label:i.write+i.throughput,color:y[1],data:[]},j={label:i.read+i.throughput,color:x[3],data:[]};r.diskSpeed.forEach((function(e){S.data.push(ca(e.write/(1<<20),2)),j.data.push(ca(e.read/(1<<20),2))}));var O=[S,j];t=[m,f,k,o.a.createElement(Ve,{xs:8,sm:6,md:3,key:"io-usage"},o.a.createElement($.a,{m:0,p:0,className:c.diskChart,boxShadow:2},o.a.createElement(no,{series:O,displayValue:E,minTickStep:1})))];var w=o.a.createElement($.a,{m:1,p:2,key:"core-label"},o.a.createElement(X.a,{component:"span",className:c.cardTitle},i.cores+": "),o.a.createElement(X.a,{component:"span"},r.cores)),_=o.a.createElement($.a,{m:1,p:2,key:"memory-label"},o.a.createElement(X.a,{component:"span",className:c.cardTitle},i.memory+": "),o.a.createElement(X.a,{component:"span"},la(r.memory))),N=[];r.disks.forEach((function(e){N.push(la(e))}));var R=o.a.createElement($.a,{m:1,p:2,key:"disk-label"},o.a.createElement(X.a,{component:"span",className:c.cardTitle},i.disks+": "),o.a.createElement(X.a,{component:"span"},N.join(" / "))),T=o.a.createElement($.a,{m:1,p:2,key:"internal-label"},o.a.createElement(X.a,{component:"span",className:c.cardTitle},i.internalAddress+": "),o.a.createElement(X.a,{component:"span"},r.internal&&r.internal.network_address?r.internal.network_address:"")),I=o.a.createElement($.a,{m:1,p:2,key:"external-label"},o.a.createElement(X.a,{component:"span",className:c.cardTitle},i.externalAddress+": "),o.a.createElement(X.a,{component:"span"},r.external&&r.external.network_address?r.external.network_address:"")),D=[];r.auto_start&&D.push(o.a.createElement(Q.a,{title:i.autoStartup,placement:"top",key:i.autoStartup},o.a.createElement(sc.a,{className:c.infoText}))),r.media_attached&&D.push(o.a.createElement(Q.a,{title:i.mediaAttached,placement:"top",key:i.mediaAttached},o.a.createElement(mc.a,{className:c.infoText}))),n=[w,_,R,T,I,o.a.createElement($.a,{m:1,p:2,key:"flag-label"},o.a.createElement(X.a,{component:"span",className:c.cardTitle},i.flags+": "),D)]}else t=new Array(4).fill(o.a.createElement(Ve,{xs:8,sm:6,md:3},o.a.createElement($.a,{m:0,p:0,className:c.disableChart,boxShadow:2}))),n=[];return o.a.createElement(ln,{chart:!0},o.a.createElement(un,null,o.a.createElement(an,null,t)),o.a.createElement(fn,null,a,o.a.createElement($.a,{m:0,p:0,className:c.cardWithDivider,display:"flex",alignItems:"center"},n)))};function Jc(e){var a,t,n=e.match.params.id,l=o.a.useState(!1),r=Object(H.a)(l,2),c=r[0],i=r[1],s=o.a.useState(!1),u=Object(H.a)(s,2),m=u[0],p=u[1],f=o.a.useState(null),b=Object(H.a)(f,2),g=b[0],h=b[1],E=o.a.useState(""),v=Object(H.a)(E,2),y=v[0],x=v[1],k=o.a.useCallback((function(){x("")}),[x]),C=o.a.useCallback((function(e){if(m){x(e),setTimeout(k,3e3)}}),[x,k,m]);if(o.a.useEffect((function(){p(!0);var e,a,t,o=new Array(5).fill({current:0,max:0}),l=new Array(5).fill({available:0,used:0}),r=new Array(5).fill({receive:0,send:0}),c=new Array(5).fill({write:0,read:0}),s=new Array(5).fill({receive:0,send:0}),u=new Array(5).fill({write:0,read:0}),f=!1,b=function(e){if(m){var n,p;if(o.shift(),o.push({current:ca(e.cpu_usage,2),max:e.cores}),e.memory_available>e.memory?(C("abnormal available memory, "+e.memory_available+" > allocated "+e.memory),p=e.memory,n=0):(p=e.memory_available,n=e.memory-e.memory_available),l.shift(),l.push({available:ca(p/(1<<20),2),used:ca(n/(1<<20),2)}),r.shift(),r.push({receive:e.bytes_received,send:e.bytes_sent}),c.shift(),c.push({write:e.bytes_written,read:e.bytes_read}),f){var b=(r[r.length-1].receive-r[r.length-2].receive)/2,g=(r[r.length-1].send-r[r.length-2].send)/2,E=(c[c.length-1].write-c[c.length-2].write)/2,v=(c[c.length-1].read-c[c.length-2].read)/2;s.shift(),s.push({receive:b,send:g}),u.shift(),u.push({write:E,read:v})}else f=!0;var y=Object(d.a)(Object(d.a)({},e),{},{pool:a,cell:t,coreRecords:o,memoryRecords:l,networkRecords:r,diskRecords:c,networkSpeed:s,diskSpeed:u});h(y),i(!0)}};return va(n,(function(o){if(m){a=o.pool,t=o.cell,ya(n,b,C);e=setInterval((function(){m&&ya(n,b,C)}),2e3)}}),C),function(){p(!1),e&&clearInterval(e)}}),[n,C,m,c]),c){a=o.a.createElement(Ve,{xs:12},o.a.createElement(Yc,{status:g,lang:e.lang}));var S=[o.a.createElement(kt.a,{to:"/admin/instances/range/?pool="+g.pool,key:g.pool},g.pool),o.a.createElement(kt.a,{to:"/admin/instances/range/?pool="+g.pool+"&cell="+g.cell,key:g.cell},g.cell),o.a.createElement(X.a,{color:"textPrimary",key:g.name},g.name)];t=o.a.createElement(At.a,{separator:"\u203a","aria-label":"breadcrumb"},S)}else a=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}}),t=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},t),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),a,o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:"warning",message:y,open:""!==y,closeNotification:k,close:!0})))}var Qc=t(597),Kc=t(596),Xc=t(298),ei=t.n(Xc),ai=t(297),ti=t.n(ai),ni=t(295),oi=t.n(ni),li=t(296),ri=t.n(li),ci=t(299),ii=t.n(ci),si=t(220),ui={en:{title:"Create Snapshot",name:"Name",description:"Description",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u521b\u5efa\u4e91\u4e3b\u673a\u5feb\u7167",name:"\u540d\u79f0",description:"\u63cf\u8ff0",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function mi(e){var a={name:"",description:""},t=e.lang,n=e.open,l=e.instanceID,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!0),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(""),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(a),E=Object(H.a)(h,2),v=E[0],y=E[1],x=ui[t],k=x.title,C=function(e){m(!0),g(e)},S=function(){g(""),y(a)},j=function(e){m(!0),S(),r(e)},O=function(e){return function(a){var t=a.target.value;y((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}},w=[{type:"text",label:x.name,onChange:O("name"),value:v.name,required:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"text",label:x.description,onChange:O("description"),value:v.description,required:!0,oneRow:!0,xs:12,sm:10,md:8}],_=o.a.createElement(Ae,{inputs:w}),N=[{color:"transparent",label:x.cancel,onClick:function(){S(),c()}},{color:"info",label:x.confirm,onClick:function(){g(""),m(!1),v.name?v.description?function(e,a,t,n,o){Ha("/instances/"+e+"/snapshots/",{name:a,description:t},()=>{n(a,e)},o)}(l,v.name,v.description,j,C):C("must specify description"):C("must specify snapshot name")}}];return o.a.createElement(Xe,{size:"sm",open:n,prompt:b,title:k,buttons:N,content:_,operatable:u})}var di={en:{title:"Delete Snapshot",content:"Are you sure to delete snapshot ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u4e91\u4e3b\u673a\u5feb\u7167",content:"\u662f\u5426\u5220\u9664\u4e91\u4e3b\u673a\u5feb\u7167 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function pi(e){var a=e.lang,t=e.instanceID,n=e.snapshotName,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!0),s=Object(H.a)(i,2),u=s[0],m=s[1],d=o.a.useState(""),p=Object(H.a)(d,2),f=p[0],b=p[1],g=di[a],h=g.title,E=g.content+n,v=function(e){m(!0),b(e)},y=function(){m(!0),b(""),r(n)},x=[{color:"transparent",label:g.cancel,onClick:function(){b(""),c()}},{color:"info",label:g.confirm,onClick:function(){m(!1),function(e,a,t,n){Va("/instances/"+e+"/snapshots/"+a,()=>{t(a,e)},n)}(t,n,y,v)}}];return o.a.createElement(Xe,{size:"sm",open:l,prompt:f,title:h,buttons:x,content:E,operatable:u})}var fi={en:{title:"Revert Snapshot",content:"Are you sure to revert snapshot ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6062\u590d\u4e91\u4e3b\u673a\u5feb\u7167",content:"\u662f\u5426\u6062\u590d\u4e91\u4e3b\u673a\u5feb\u7167 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function bi(e){var a=e.lang,t=e.instanceID,n=e.snapshotName,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!0),s=Object(H.a)(i,2),u=s[0],m=s[1],d=o.a.useState(""),p=Object(H.a)(d,2),f=p[0],b=p[1],g=fi[a],h=g.title,E=g.content+n,v=function(e){m(!0),b(e)},y=function(){m(!0),b(""),r(n)},x=[{color:"transparent",label:g.cancel,onClick:function(){b(""),c()}},{color:"info",label:g.confirm,onClick:function(){m(!1),function(e,a,t,n){Ga("/instances/"+e+"/snapshots/",{target:a},()=>{t(a,e)},n)}(t,n,y,v)}}];return o.a.createElement(Xe,{size:"sm",open:l,prompt:f,title:h,buttons:x,content:E,operatable:u})}var gi={panel:{background:t.n(si).a[100]}},hi={en:{title:"Snapshots of ",noResource:"No snapshots created",back:"Back",create:"Create New Snapshot",delete:"Delete",revert:"Revert",current:"Current",name:"Name",description:"Description",createdTime:"Created Time",type:"Type",offline:"Offline Snapshot",realtime:"Realtime Snapshot"},cn:{title:"\u4e91\u4e3b\u673a\u5feb\u7167:",noResource:"\u5c1a\u672a\u521b\u5efa\u4e91\u4e3b\u673a\u5feb\u7167",back:"\u8fd4\u56de",create:"\u521b\u5efa\u65b0\u5feb\u7167",delete:"\u5220\u9664",revert:"\u6062\u590d",current:"\u5f53\u524d",name:"\u5feb\u7167\u540d\u79f0",description:"\u63cf\u8ff0",createdTime:"\u521b\u5efa\u65f6\u95f4",type:"\u7c7b\u578b",offline:"\u79bb\u7ebf\u5feb\u7167",realtime:"\u5b9e\u65f6\u5feb\u7167"}};function Ei(e){var a=Object(m.a)(gi)(),t=e.match.params.id,n=o.a.useState(null),l=Object(H.a)(n,2),r=l[0],c=l[1],i=o.a.useState(null),s=Object(H.a)(i,2),u=s[0],p=s[1],f=o.a.useState(!1),b=Object(H.a)(f,2),g=b[0],h=b[1],E=o.a.useState(!1),v=Object(H.a)(E,2),y=v[0],x=v[1],k=o.a.useState(!1),C=Object(H.a)(k,2),S=C[0],j=C[1],O=o.a.useState("warning"),w=Object(H.a)(O,2),_=w[0],N=w[1],R=o.a.useState(""),T=Object(H.a)(R,2),I=T[0],D=T[1],A=function(){D("")},P=o.a.useCallback((function(e){N("warning"),D(e),setTimeout(A,3e3)}),[N,D]),F=function(e){N("info"),D(e),Ma(e),setTimeout(A,3e3)},B=o.a.useCallback((function(e){P(e)}),[P]),M=o.a.useCallback((function(e,a,t){var n=[];return t.get(e).forEach((function(e){var o={name:e};a===e&&(o.isCurrent=!0),t.has(e)&&(o.children=M(e,a,t)),n.push(o)})),n}),[]),z=o.a.useCallback((function(e){!function(e,a,t){Wa("/instances/"+e+"/snapshots/",a,t)}(t,(function(a){var t="",n="",o={},l=new Map;if(Object.keys(a).forEach((function(e){l.set(e,a[e])})),0!==l.length){var r=new Map;l.forEach((function(e,a){if(e.is_root&&(t=a),e.is_current&&(n=a),e.backing){var o=e.backing;r.has(o)?r.get(o).push(a):r.set(o,[a])}})),""!==t&&(o.name=t,t===n&&(o.isCurrent=!0),r.has(t)&&(o.children=M(t,n,r)))}c(e?{name:e,rootName:t,current:n,rootNode:o}:function(e){return Object(d.a)(Object(d.a)({},e),{},{rootName:t,current:n,rootNode:o})})}),B)}),[t,B,M]),W=function(){h(!1)},q=function(){x(!1)},L=function(){j(!1)},U=function(e){!function(e,a,t,n){Wa("/instances/"+e+"/snapshots/"+a,t,n)}(t,e,(function(a){p(Object(d.a)(Object(d.a)({},a),{},{name:e}))}),B)};if(o.a.useEffect((function(){va(t,(function(e){z(e.name)}),B)}),[t,B,z]),null===ta())return oa();var G,V,Z=e.lang,Y=hi[Z],J=[];if(null===r)G=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}}),V="";else{if(""===r.rootName)G=o.a.createElement($.a,{display:"flex",justifyContent:"center"},Y.noResource);else{var Q,K=[],ee=function e(a){var t;t=a.isCurrent?a.name+"( "+Y.current+" )":a.name,K.push(a.name);var n={nodeId:a.name,label:t,key:a.name,onClick:function(e){e.preventDefault(),U(a.name)}};if(a.name===r.rootName?n.icon=o.a.createElement(oi.a,null):a.name===r.current&&(n.icon=o.a.createElement(ri.a,null)),a.children){var l=[];a.children.forEach((function(a){l.push(e(a))})),n.children=l}return o.a.createElement(Kc.a,n)}(r.rootNode),ae=o.a.createElement(Qc.a,{defaultCollapseIcon:o.a.createElement(ti.a,null),defaultExpandIcon:o.a.createElement(ei.a,null),defaultEndIcon:o.a.createElement(Lr.a,null),defaultExpanded:K},ee);if(u){var te=[{title:Y.name,value:u.name},{title:Y.description,value:u.description},{title:Y.createdTime,value:u.create_time},{title:Y.type,value:u.running?Y.realtime:Y.offline}],ne=[o.a.createElement(ue,{size:"sm",color:"info",onClick:function(){j(!0)}},o.a.createElement(ii.a,null),Y.revert),o.a.createElement(ue,{size:"sm",color:"info",onClick:function(){x(!0)}},o.a.createElement(Ht.a,null),Y.delete)];Q=o.a.createElement(ge.a,{className:a.panel},o.a.createElement($.a,{p:2,m:1},o.a.createElement(yn.a,{size:"small"},o.a.createElement(Cn.a,null,te.map((function(e){return o.a.createElement(kn.a,{key:e.title},o.a.createElement(Sn.a,{component:"th"},o.a.createElement(X.a,{component:"span",variant:"subtitle1"},e.title)),o.a.createElement(Sn.a,null,o.a.createElement(X.a,{component:"span"},e.value)))}))))),o.a.createElement($.a,{display:"flex",m:2},ne.map((function(e,a){return o.a.createElement($.a,{key:a,m:2,p:1},e)}))))}else Q=o.a.createElement("div",null);G=o.a.createElement(an,null,o.a.createElement(Ve,{xs:12,sm:6},ae),o.a.createElement(Ve,{xs:12,sm:6},Q))}V=Y.title+r.name,J=[o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){e.history.goBack()}},o.a.createElement(ul.a,null),Y.back),o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){h(!0)}},o.a.createElement(Dt.a,null),Y.create)]}return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{display:"flex"},J.map((function(e,a){return o.a.createElement($.a,{key:a,pl:2,pr:2},e)})))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12,sm:12,md:12},o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},V),o.a.createElement(fn,null,G))),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:_,message:I,open:""!==I,closeNotification:A,close:!0})),o.a.createElement(Ve,null,o.a.createElement(mi,{lang:Z,open:g,instanceID:t,onSuccess:function(e){W(),F("new snapshot "+e+" created for "+r.name),z()},onCancel:W})),o.a.createElement(Ve,null,o.a.createElement(pi,{lang:Z,open:y,instanceID:t,snapshotName:u?u.name:"",onSuccess:function(e){q(),F("snapshot "+e+" deleted"),z()},onCancel:q})),o.a.createElement(Ve,null,o.a.createElement(bi,{lang:Z,open:S,instanceID:t,snapshotName:u?u.name:"",onSuccess:function(e){L(),F("restored to snapshot "+e),z()},onCancel:L})))}var vi=t(598),yi=t(222),xi=t.n(yi),ki=t(223),Ci=t.n(ki),Si=t(225),ji=t.n(Si),Oi=t(226),wi=t.n(Oi),_i=t(224),Ni=t.n(_i),Ri={en:{title:"Modify Instance Name",current:"Current Name",new:"New Name",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u4e91\u4e3b\u673a\u540d\u79f0",current:"\u5f53\u524d\u4e91\u4e3b\u673a\u540d",new:"\u65b0\u4e3b\u673a\u540d",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Ti(e){var a={name:""},t=e.lang,n=e.open,l=e.instanceID,r=e.current,c=e.onSuccess,i=e.onCancel,s=r?r.name.slice(r.group.length+1):"",u=o.a.useState(!0),m=Object(H.a)(u,2),p=m[0],f=m[1],b=o.a.useState(""),g=Object(H.a)(b,2),h=g[0],E=g[1],v=o.a.useState(!1),y=Object(H.a)(v,2),x=y[0],k=y[1],C=o.a.useState(a),S=Object(H.a)(C,2),j=S[0],O=S[1],w=Ri[t],_=w.title,N=o.a.useCallback((function(e){x&&(f(!0),E(e))}),[x]),R=function(){E(""),O(a)},T=function(e){x&&(f(!0),R(),c(e,l))};o.a.useEffect((function(){if(n)return k(!0),function(){return k(!1)}}),[n]);var I,D=[{type:"text",label:w.current,value:s,disabled:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"text",label:w.new,onChange:(I="name",function(e){if(x){var a=e.target.value;O((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},I,a))}))}}),value:j.name,required:!0,oneRow:!0,xs:12,sm:10,md:8}],A=[{color:"transparent",label:w.cancel,onClick:function(){R(),i()}},{color:"info",label:w.confirm,onClick:function(){if(E(""),f(!1),j.name){var e=[r.group,j.name].join(".");s!==e?function(e,a,t,n){Ga("/guests/"+e+"/name/",{name:a},()=>{t(a,e)},n)}(l,e,T,N):N("no need to modify")}else N("must specify new instance name")}}],P=o.a.createElement(Ae,{inputs:D});return o.a.createElement(Xe,{size:"sm",open:n,prompt:h,title:_,buttons:A,content:P,operatable:p})}var Ii={en:{title:"Modify Instance Cores",current:"Current Cores",new:"New Cores",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u6838\u5fc3\u6570",current:"\u5f53\u524d\u6838\u5fc3\u6570",new:"\u65b0\u6838\u5fc3\u6570",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Di(e){var a={cores:""},t=e.lang,n=e.open,l=e.instanceID,r=e.current,c=e.onSuccess,i=e.onCancel,s=r?r.cores:0,u=o.a.useState(!0),m=Object(H.a)(u,2),p=m[0],f=m[1],b=o.a.useState(""),g=Object(H.a)(b,2),h=g[0],E=g[1],v=o.a.useState(!1),y=Object(H.a)(v,2),x=y[0],k=y[1],C=o.a.useState(a),S=Object(H.a)(C,2),j=S[0],O=S[1],w=Ii[t],_=w.title,N=o.a.useCallback((function(e){x&&(f(!0),E(e))}),[x]),R=function(){E(""),O(a)},T=function(e){x&&(f(!0),R(),c(e,l))};o.a.useEffect((function(){if(n)return k(!0),function(){return k(!1)}}),[n]);var I,D=[{type:"text",label:w.current,value:s.toString(),disabled:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"text",label:w.new,onChange:(I="cores",function(e){if(x){var a=e.target.value;O((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},I,a))}))}}),value:j.cores,required:!0,oneRow:!0,xs:12,sm:10,md:8}],A=[{color:"transparent",label:w.cancel,onClick:function(){R(),i()}},{color:"info",label:w.confirm,onClick:function(){if(j.cores){var e=Number.parseInt(j.cores);Number.isNaN(e)?N("invalid cores number: "+j.cores):s!==e?(E(""),f(!1),function(e,a,t,n){Ga("/guests/"+e+"/cores",{cores:a},()=>{t(a,e)},n)}(l,e,T,N)):N("no need to modify")}else N("must specify new instance cores")}}],P=o.a.createElement(Ae,{inputs:D});return o.a.createElement(Xe,{size:"sm",open:n,prompt:h,title:_,buttons:A,content:P,operatable:p})}var Ai={en:{title:"Modify Memory of Instance",current:"Current Memory Size",new:"New Memory Size (MB)",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u5185\u5b58\u5927\u5c0f",current:"\u5f53\u524d\u5185\u5b58\u5bb9\u91cf",new:"\u65b0\u5185\u5b58\u5bb9\u91cf(MB)",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Pi(e){var a={memory:""},t=e.lang,n=e.open,l=e.instanceID,r=e.current,c=e.onSuccess,i=e.onCancel,s=r?r.memory:0,u=la(s),m=o.a.useState(!0),p=Object(H.a)(m,2),f=p[0],b=p[1],g=o.a.useState(""),h=Object(H.a)(g,2),E=h[0],v=h[1],y=o.a.useState(!1),x=Object(H.a)(y,2),k=x[0],C=x[1],S=o.a.useState(a),j=Object(H.a)(S,2),O=j[0],w=j[1],_=Ai[t],N=_.title,R=o.a.useCallback((function(e){k&&(b(!0),v(e))}),[k]),T=function(){v(""),w(a)},I=function(e){k&&(b(!0),T(),c(e,l))};o.a.useEffect((function(){if(n)return C(!0),function(){return C(!1)}}),[n]);var D,A=[{type:"text",label:_.current,value:u,disabled:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"text",label:_.new,onChange:(D="memory",function(e){if(k){var a=e.target.value;w((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},D,a))}))}}),value:O.memory,required:!0,oneRow:!0,xs:12,sm:10,md:8}],P=[{color:"transparent",label:_.cancel,onClick:function(){T(),i()}},{color:"info",label:_.confirm,onClick:function(){if(O.memory){var e=Number.parseInt(O.memory);if(Number.isNaN(e))R("invalid memory size: "+O.memory);else{var a=e*(1<<20);s!==a?(v(""),b(!1),function(e,a,t,n){Ga("/guests/"+e+"/memory",{memory:a},()=>{t(a,e)},n)}(l,a,I,R)):R("no need to modify")}}else R("must specify new memory size")}}],F=o.a.createElement(Ae,{inputs:A});return o.a.createElement(Xe,{size:"sm",open:n,prompt:E,title:N,buttons:P,content:F,operatable:f})}var Fi={en:{title:"Modify Admin Password",name:"Admin Name",new:"New Password (generate a new one when blank)",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u7ba1\u7406\u5458\u5bc6\u7801",name:"\u5f53\u524d\u7ba1\u7406\u5458",new:"\u65b0\u5bc6\u7801(\u7559\u7a7a\u5219\u81ea\u52a8\u751f\u6210)",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Bi(e){var a={user:"",password:""},t=e.lang,n=e.open,l=e.instanceID,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!1),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(!0),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(""),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(!1),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState(a),O=Object(H.a)(j,2),w=O[0],_=O[1],N=Fi[t],R=N.title,T=o.a.useCallback((function(e){C&&(g(!0),y(e))}),[C]),I=function(){y(""),_(a),m(!1)},D=function(e){C&&(g(!0),I(),r(e,l))};o.a.useEffect((function(){if(n&&l){S(!0);return ja(l,(function(e,a){C&&(_({user:e,password:a}),m(!0))}),T),function(){S(!1)}}}),[C,n,l,T]);var A,P,F=[{color:"transparent",label:N.cancel,onClick:function(){I(),c()}}];if(u){var B=[{type:"text",label:N.name,value:w.user,disabled:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"password",label:N.new,value:w.password,onChange:(P="password",function(e){if(C){var a=e.target.value;_((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},P,a))}))}}),required:!0,oneRow:!0,xs:12,sm:10,md:8}];A=o.a.createElement(Ae,{inputs:B}),F.push({color:"info",label:N.confirm,onClick:function(){y(""),g(!1),function(e,a,t,n,o){var l={};a&&(l.user=a),t&&(l.password=t),Ga("/guests/"+e+"/auth",l,a=>{let{user:t,password:o}=a;n(t,o,e)},o)}(l,w.user,w.password,D,T)}})}else A=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:n,prompt:v,title:R,buttons:F,content:A,operatable:b})}var Mi={en:{title:"Extend Disk Size",current:"Current Disk Size",new:"New Disk Size (GB)",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6269\u5c55\u78c1\u76d8\u5bb9\u91cf",current:"\u5f53\u524d\u78c1\u76d8\u5bb9\u91cf",new:"\u65b0\u78c1\u76d8\u5bb9\u91cf(GB)",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function zi(e){var a={size:""},t=e.lang,n=e.open,l=e.instanceID,r=e.current,c=e.index,i=e.onSuccess,s=e.onCancel,u=r?r.disks[c]:0,m=la(u),p=o.a.useState(!0),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(""),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(!1),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState(a),O=Object(H.a)(j,2),w=O[0],_=O[1],N=Mi[t],R=N.title,T=o.a.useCallback((function(e){C&&(g(!0),y(e))}),[C]),I=function(){y(""),_(a)},D=function(e,a){C&&(g(!0),I(),i(e,a,l))};o.a.useEffect((function(){if(n)return S(!0),function(){return S(!1)}}),[n]);var A,P=[{type:"text",label:N.current,value:m,disabled:!0,oneRow:!0,xs:12,sm:6,md:4},{type:"text",label:N.new,onChange:(A="size",function(e){if(C){var a=e.target.value;_((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},A,a))}))}}),value:w.size,required:!0,oneRow:!0,xs:12,sm:10,md:8}],F=[{color:"transparent",label:N.cancel,onClick:function(){I(),s()}},{color:"info",label:N.confirm,onClick:function(){if(w.size){var e=Number.parseInt(w.size);if(Number.isNaN(e))T("invalid disk size: "+w.size);else{var a=e*(1<<30);u!==a?(y(""),g(!1),function(e,a,t,n,o){const l={size:t};Ga("/guests/"+e+"/disks/resize/"+a.toString(),l,()=>{n(a,t,e)},o)}(l,c,a,D,T)):T("no need to modify")}}else T("must specify new disk size")}}],B=o.a.createElement(Ae,{inputs:P});return o.a.createElement(Xe,{size:"sm",open:n,prompt:v,title:R,buttons:F,content:B,operatable:b})}var Wi={en:{title:"Shrink Disk Size",content1:"Are you sure to shrink size of ",content2:" ? it will take a long time, please be patient and ignore the timeout warning.",cancel:"Cancel",confirm:"Confirm",systemDisk:"System Disk",dataDisk:"Data Disk"},cn:{title:"\u538b\u7f29\u78c1\u76d8\u5bb9\u91cf",content1:"\u662f\u5426\u538b\u7f29 ",content2:" \u7684\u78c1\u76d8\u7a7a\u95f4\uff0c\u8fd9\u4f1a\u5360\u7528\u5f88\u957f\u65f6\u95f4\uff0c\u8bf7\u5ffd\u7565\u8d85\u65f6\u63d0\u793a\u5e76\u8010\u5fc3\u7b49\u5f85",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a",systemDisk:"\u7cfb\u7edf\u78c1\u76d8",dataDisk:"\u6570\u636e\u78c1\u76d8"}};function qi(e){var a,t=e.lang,n=e.instanceID,l=e.index,r=e.open,c=e.onSuccess,i=e.onCancel,s=o.a.useState(!0),u=Object(H.a)(s,2),m=u[0],d=u[1],p=o.a.useState(""),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(!1),E=Object(H.a)(h,2),v=E[0],y=E[1],x=Wi[t],k=x.title,C=o.a.useCallback((function(e){v&&(d(!0),g(e))}),[v]),S=function(e){v&&(d(!0),g(""),c(e,n))};o.a.useEffect((function(){if(r)return y(!0),function(){return y(!1)}}),[r]),a=0===l?x.content1+x.systemDisk+x.content2:x.content1+x.dataDisk+l.toString()+x.content2;var j=[{color:"transparent",label:x.cancel,onClick:function(){g(""),i()}},{color:"info",label:x.confirm,onClick:function(){g(""),d(!1),function(e,a,t,n){Ga("/guests/"+e+"/disks/shrink/"+a.toString(),{immediate:!1},()=>{t(a,e)},n)}(n,l,S,C)}}];return o.a.createElement(Xe,{size:"sm",open:r,prompt:b,title:k,buttons:j,content:a,operatable:m})}var Hi={en:{title:"Modify CPU Priority",label:"CPU Priority",cpuPriorityHigh:"High",cpuPriorityMedium:"Medium",cpuPriorityLow:"Low",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539CPU\u4f18\u5148\u7ea7",label:"CPU\u4f18\u5148\u7ea7",cpuPriorityHigh:"\u9ad8",cpuPriorityMedium:"\u4e2d",cpuPriorityLow:"\u4f4e",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Li(e){var a=e.lang,t=e.open,n=e.instanceID,l=e.onSuccess,r=e.onCancel,c={priority:"medium"},i=o.a.useState(!0),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(!1),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(""),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(!1),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState(c),O=Object(H.a)(j,2),w=O[0],_=O[1],N=Hi[a],R=N.title,T=o.a.useCallback((function(e){C&&(m(!0),y(e))}),[C]),I=function(){y(""),_(c),g(!1)},D=function(e){C&&(m(!0),I(),l(e,n))};o.a.useEffect((function(){if(t&&n){S(!0);return va(n,(function(e){if(C){var a="medium";e.qos&&(a=e.qos.cpu_priority),_({priority:a}),g(!0)}}),T),function(){return S(!1)}}}),[t,n,C,T]);var A,P,F=[{color:"transparent",label:N.cancel,onClick:function(){I(),r()}}];if(b){var B=[{label:N.cpuPriorityHigh,value:"high"},{label:N.cpuPriorityMedium,value:"medium"},{label:N.cpuPriorityLow,value:"low"}],M=[{type:"radio",label:N.label,onChange:(P="priority",function(e){if(C){var a=e.target.value;_((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},P,a))}))}}),value:w.priority,options:B,required:!0,xs:12}];A=o.a.createElement(Ae,{inputs:M}),F.push({color:"info",label:N.confirm,onClick:function(){w.priority?(y(""),m(!1),function(e,a,t,n){Ga("/guests/"+e+"/qos/cpu",{priority:a},()=>{t(a,e)},n)}(n,w.priority,D,T)):T("invalid priority value")}})}else A=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:t,prompt:v,title:R,buttons:F,content:A,operatable:u})}var Ui={en:{title:"Modify Disk IOPS",label:"IOPS",noLimit:"No Limit",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u78c1\u76d8\u8bfb\u5199\u9650\u5236",label:"\u78c1\u76d8\u8bfb\u5199\u9650\u5236",noLimit:"\u65e0\u9650\u5236",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Gi(e){var a={iops:0},t=e.lang,n=e.open,l=e.instanceID,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!0),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(!1),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(""),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(!1),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState(a),O=Object(H.a)(j,2),w=O[0],_=O[1],N=Ui[t],R=N.title,T=o.a.useCallback((function(e){C&&(m(!0),y(e))}),[C]),I=function(){y(""),_(a),g(!1)},D=function(e){C&&(m(!0),I(),r(e,l))};o.a.useEffect((function(){if(n&&l){S(!0);return va(l,(function(e){if(C){var a=0;e.qos&&(a=e.qos.write_iops),_({iops:a}),g(!0)}}),T),function(){return S(!1)}}}),[n,l,C,T]);var A,P,F=[{color:"transparent",label:N.cancel,onClick:function(){I(),c()}}];if(b){var B=[{value:0,label:N.noLimit},{value:2e3,label:2e3}],M=[{type:"slider",label:N.label,onChange:(P="iops",function(e,a){C&&_((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},P,a))}))}),value:w.iops,marks:B,step:10,maxStep:2e3,minStep:0,required:!0,xs:12}];A=o.a.createElement(Ae,{inputs:M}),F.push({color:"info",label:N.confirm,onClick:function(){y(""),m(!1),function(e,a,t,n){Ga("/guests/"+e+"/qos/disk",{write_iops:a,read_iops:a},()=>{t(a,e)},n)}(l,w.iops,D,T)}})}else A=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:n,prompt:v,title:R,buttons:F,content:A,operatable:u})}var Vi={en:{title:"Modify Network Bandwidth",outbound:"Outband Bandwidth",inbound:"Inbound Bandwidth",noLimit:"No Limit",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u7f51\u7edc\u5e26\u5bbd\u9650\u5236",outbound:"\u4e0a\u884c\u5e26\u5bbd",inbound:"\u4e0b\u884c\u5e26\u5bbd",noLimit:"\u65e0\u9650\u5236",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function $i(e){var a={inbound:0,outbound:0},t=e.lang,n=e.open,l=e.instanceID,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!0),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(!1),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(""),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(!1),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState(a),O=Object(H.a)(j,2),w=O[0],_=O[1],N=Vi[t],R=N.title,T=o.a.useCallback((function(e){C&&(m(!0),y(e))}),[C]),I=function(){y(""),_(a),g(!1)},D=function(e,a){C&&(m(!0),I(),r(e,a,l))},A=function(e){return function(a,t){C&&_((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}};o.a.useEffect((function(){if(n&&l){S(!0);return va(l,(function(e){if(C){var t=a;e.qos&&e.qos.receive_speed&&(t.inbound=e.qos.receive_speed/(1<<17)),e.qos&&e.qos.send_speed&&(t.outbound=e.qos.send_speed/(1<<17)),_(t),g(!0)}}),T),function(){return S(!1)}}}),[n,l,C,T,1<<17,a]);var P,F=[{color:"transparent",label:N.cancel,onClick:function(){I(),c()}}];if(b){var B=[{value:0,label:N.noLimit},{value:20,label:"20 Mbit/s"}],M=[{type:"slider",label:N.inbound,onChange:A("inbound"),value:w.inbound,marks:B,step:2,maxStep:20,minStep:0,required:!0,xs:12},{type:"slider",label:N.outbound,onChange:A("outbound"),value:w.outbound,marks:B,step:2,maxStep:20,minStep:0,required:!0,xs:12}];P=o.a.createElement(Ae,{inputs:M}),F.push({color:"info",label:N.confirm,onClick:function(){y(""),m(!1),function(e,a,t,n,o){Ga("/guests/"+e+"/qos/network",{receive_speed:a,send_speed:t},()=>{n(a,t,e)},o)}(l,w.inbound*(1<<17),w.outbound*(1<<17),D,T)}})}else P=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:n,prompt:v,title:R,buttons:F,content:P,operatable:u})}var Zi={en:{title:"Reset Monitor Secret",content:"Are you sure to reset monitor secret of instance ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u91cd\u7f6e\u76d1\u63a7\u5bc6\u7801",content:"\u662f\u5426\u91cd\u7f6e\u4e91\u4e3b\u673a\u76d1\u63a7\u5bc6\u7801 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Yi(e){var a=e.lang,t=e.guestID,n=e.guestName,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!0),s=Object(H.a)(i,2),u=s[0],m=s[1],d=o.a.useState(""),p=Object(H.a)(d,2),f=p[0],b=p[1],g=Zi[a],h=g.title,E=g.content+n,v=function(e){m(!0),b(e)},y=function(){m(!0),b(""),r(t)},x=[{color:"transparent",label:g.cancel,onClick:function(){b(""),c()}},{color:"info",label:g.confirm,onClick:function(){m(!1),function(e,a,t){Ga("/guests/"+e+"/monitor/secret","",()=>{a(e)},t)}(t,y,v)}}];return o.a.createElement(Xe,{size:"sm",open:l,prompt:f,title:h,buttons:x,content:E,operatable:u})}var Ji={en:{title:"Details of Instance ",back:"Back",name:"Name",id:"ID",cores:"Cores",memory:"Memory",adminPassword:"Admin Password",monitorAddress:"Monitor Address",monitorSecret:"Monitor Secret",systemDisk:"System Disk",dataDisk:"Data Disk",ethernetAddress:"Ethernet Address",internalAddress:"Internal Address",allocatedAddress:"Allocated Address",externalAddress:"External Address",operatingSystem:"Operating System",autoStartup:"Auto Startup",enable:"Enable",disable:"Disable",pool:"Host Pool",cell:"Host Cell",owner:"Owner",group:"Group",cpuPriority:"CPU Priority",iops:"Disk IOPS",bandwidth:"Inbound/Outbound Bandwidth",noLimit:"No Limit",cpuPriorityHigh:"High",cpuPriorityMedium:"Medium",cpuPriorityLow:"Low",createdTime:"Created Time",disabledWhenRunning:"Disabled When Running",disabledWhenStopped:"Disabled When Stopped",status:"Status",running:"Running",stopped:"Stopped",display:"Display",hide:"Hide",modify:"Modify",extendDisk:"Extend Disk Size",shrinkDisk:"Shrink Disk Size",resetSecret:"Reset Monitor Secret"},cn:{title:"\u4e91\u4e3b\u673a\u8be6\u60c5 ",back:"\u8fd4\u56de",name:"\u4e3b\u673a\u540d",id:"ID",cores:"\u6838\u5fc3\u6570",memory:"\u5185\u5b58",adminPassword:"\u7ba1\u7406\u5458\u5bc6\u7801",monitorAddress:"\u76d1\u63a7\u5730\u5740",monitorSecret:"\u76d1\u63a7\u5bc6\u7801",systemDisk:"\u7cfb\u7edf\u78c1\u76d8",dataDisk:"\u6570\u636e\u78c1\u76d8",ethernetAddress:"MAC\u5730\u5740",internalAddress:"\u5185\u90e8\u5730\u5740",allocatedAddress:"\u5df2\u5206\u914d\u5730\u5740",externalAddress:"\u5916\u90e8\u5730\u5740",operatingSystem:"\u64cd\u4f5c\u7cfb\u7edf",autoStartup:"\u5f00\u673a\u542f\u52a8",enable:"\u542f\u7528",disable:"\u672a\u542f\u7528",pool:"\u6240\u5c5e\u8d44\u6e90\u6c60",cell:"\u627f\u8f7d\u8d44\u6e90\u8282\u70b9",owner:"\u6240\u5c5e\u7528\u6237",group:"\u6240\u5c5e\u7528\u6237\u7ec4",cpuPriority:"CPU\u4f18\u5148\u7ea7",iops:"\u78c1\u76d8 IOPS",bandwidth:"\u4e0b/\u4e0a\u884c\u5e26\u5bbd",noLimit:"\u65e0\u9650\u5236",cpuPriorityHigh:"\u9ad8",cpuPriorityMedium:"\u4e2d",cpuPriorityLow:"\u4f4e",createdTime:"\u521b\u5efa\u65f6\u95f4",disabledWhenRunning:"\u8fd0\u884c\u65f6\u7981\u7528",disabledWhenStopped:"\u505c\u673a\u65f6\u7981\u7528",status:"\u72b6\u6001",running:"\u8fd0\u884c\u4e2d",stopped:"\u5df2\u505c\u673a",display:"\u663e\u793a",hide:"\u9690\u85cf",modify:"\u4fee\u6539",extendDisk:"\u6269\u5c55\u78c1\u76d8\u5bb9\u91cf",shrinkDisk:"\u7f29\u51cf\u78c1\u76d8\u7a7a\u95f4",resetSecret:"\u91cd\u7f6e\u76d1\u63a7\u5bc6\u7801"}};function Qi(e){var a=e.match.params.id,t=o.a.useState(null),n=Object(H.a)(t,2),l=n[0],r=n[1],c=o.a.useState(null),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(!1),d=Object(H.a)(m,2),p=d[0],f=d[1],b=o.a.useState(!1),g=Object(H.a)(b,2),h=g[0],E=g[1],v=o.a.useState(!1),y=Object(H.a)(v,2),x=y[0],k=y[1],C=o.a.useState(!1),S=Object(H.a)(C,2),j=S[0],O=S[1],w=o.a.useState(!1),_=Object(H.a)(w,2),N=_[0],R=_[1],T=o.a.useState(!1),I=Object(H.a)(T,2),D=I[0],A=I[1],P=o.a.useState(!1),F=Object(H.a)(P,2),B=F[0],M=F[1],z=o.a.useState(!1),W=Object(H.a)(z,2),q=W[0],L=W[1],U=o.a.useState(!1),G=Object(H.a)(U,2),V=G[0],Z=G[1],Y=o.a.useState(!1),J=Object(H.a)(Y,2),Q=J[0],K=J[1],X=o.a.useState(!1),ee=Object(H.a)(X,2),ae=ee[0],te=ee[1],ne=o.a.useState(0),oe=Object(H.a)(ne,2),le=oe[0],re=oe[1],ce=o.a.useState("warning"),ie=Object(H.a)(ce,2),se=ie[0],me=ie[1],de=o.a.useState(""),pe=Object(H.a)(de,2),fe=pe[0],be=pe[1],he=function(){be("")},Ee=o.a.useCallback((function(e){me("warning"),be(e),setTimeout(he,3e3)}),[me,be]),ye=function(e){me("info"),be(e),Ma(e),setTimeout(he,3e3)},ke=o.a.useCallback((function(e){Ee(e)}),[Ee]),Ce=o.a.useCallback((function(){va(a,(function(e){r(e)}),ke)}),[a,ke]),Se=function(){E(!1)},je=function(){k(!1)},Oe=function(){O(!1)},we=function(){R(!1)},_e=function(e){A(!0),re(e)},Ne=function(){A(!1)},Re=function(e){M(!0),re(e)},Te=function(){M(!1)},Ie=function(){L(!1)},De=function(){Z(!1)},Ae=function(){K(!1)},Pe=function(){te(!0)},Fe=function(){te(!1)},Be=function(){Ce()},Me=function(e){Ee("set auto start fail: "+e)};o.a.useEffect((function(){Ce()}),[Ce]);var ze,We,qe=e.lang,He=Ji[qe],Le=[];if(null===l)ze=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}}),We="";else{var Ue,Ge={label:He.disabledWhenRunning,icon:xi.a},$e={label:He.disabledWhenStopped,icon:xi.a};Ue=l.display_protocol?l.display_protocol+"://"+l.internal.display_address:"vnc://"+l.internal.display_address;var Ze=[l.qos&&l.qos.send_speed&&0!==l.qos.receive_speed?ra(l.qos.receive_speed):He.noLimit,l.qos&&l.qos.receive_speed&&0!==l.qos.send_speed?ra(l.qos.send_speed):He.noLimit].join(" / "),Ye=[];s?Ye.push({label:He.hide,icon:Ci.a,onClick:function(){return u(null)}}):Ye.push({label:He.display,icon:dl.a,onClick:function(){ja(a,(function(e,a){u(a||'no password configured for user "'+e+'"')}),ke)}}),l.running?Ye.push({label:He.modify,icon:Qo.a,onClick:function(){R(!0)}}):Ye.push($e);var Je,Qe=[{title:He.name,value:l.name,operators:l.running?[Ge]:[{label:He.modify,icon:Qo.a,onClick:function(){E(!0)}}]},{title:He.id,value:a},{title:He.cores,value:l.cores,operators:l.running?[Ge]:[{label:He.modify,icon:Qo.a,onClick:function(){k(!0)}}]},{title:He.memory,value:la(l.memory),operators:l.running?[Ge]:[{label:He.modify,icon:Qo.a,onClick:function(){O(!0)}}]},{title:He.status,value:l.running?He.running:He.stopped},{title:He.ethernetAddress,value:l.ethernet_address},{title:He.createdTime,value:l.create_time},{title:He.adminPassword,value:s||"****************",operators:Ye},{title:He.monitorAddress,value:l.internal?Ue:""},{title:He.monitorSecret,value:p?l.monitor_secret:new Array(l.monitor_secret.length).fill("*"),operators:p?[{label:He.hide,icon:Ci.a,onClick:function(){return f(!1)}},{label:He.resetSecret,icon:Ni.a,onClick:Pe}]:[{label:He.display,icon:dl.a,onClick:function(){return f(!0)}},{label:He.resetSecret,icon:Ni.a,onClick:Pe}]},{title:He.systemDisk,value:la(l.disks[0]),operators:l.running?[Ge]:[{label:He.extendDisk,icon:ji.a,onClick:function(){return _e(0)}},{label:He.shrinkDisk,icon:wi.a,onClick:function(){return Re(0)}}]}];if(l.disks.length>1)for(var Ke=function(){var e=Xe;Qe.push({title:He.dataDisk+Xe.toString(),value:la(l.disks[Xe]),operators:l.running?[Ge]:[{label:He.extendDisk,icon:ji.a,onClick:function(){return _e(e)}},{label:He.shrinkDisk,icon:wi.a,onClick:function(){return Re(e)}}]})},Xe=1;Xe<l.disks.length;Xe++)Ke();if(l.qos&&l.qos.cpu_priority)switch(l.qos.cpu_priority){case"high":Je=He.cpuPriorityHigh;break;case"medium":Je=He.cpuPriorityMedium;break;case"low":Je=He.cpuPriorityLow;break;default:Je="invalid priority "+l.qos.cpu_priority}else Je=He.noLimit;Qe=Qe.concat([{title:He.internalAddress,value:l.internal&&l.internal.network_address?l.internal.network_address:""},{title:He.allocatedAddress,value:l.internal&&l.internal.allocated_address?l.internal.allocated_address:""},{title:He.externalAddress,value:l.external&&l.external.network_address?l.external.network_address:""},{title:He.operatingSystem,value:l.system},{title:He.autoStartup,value:o.a.createElement("div",null,He.disable,o.a.createElement(xe.a,{checked:l.auto_start,onChange:function(e){var t=e.target.checked;e.preventDefault(),function(e,a,t,n){Ga("/guests/"+e+"/auto_start",{enable:a},()=>{t(e)},n)}(a,t,Be,Me)},color:"primary"}),He.enable)},{title:He.pool,value:l.pool},{title:He.cell,value:l.cell},{title:He.cpuPriority,value:Je,operators:[{label:He.modify,icon:Qo.a,onClick:function(){L(!0)}}]},{title:He.iops,value:l.qos&&l.qos.write_iops?l.qos.write_iops:He.noLimit,operators:l.running?[Ge]:[{label:He.modify,icon:Qo.a,onClick:function(){Z(!0)}}]},{title:He.bandwidth,value:Ze,operators:[{label:He.modify,icon:Qo.a,onClick:function(){K(!0)}}]}]),ze=o.a.createElement(vi.a,{maxWidth:"md"},o.a.createElement(Pc.a,{component:ge.a},o.a.createElement(yn.a,null,o.a.createElement(Cn.a,null,Qe.map((function(e,a){return o.a.createElement(kn.a,{key:a},o.a.createElement(Sn.a,{component:"th",scope:"row"},e.title),o.a.createElement(Sn.a,null,e.value),o.a.createElement(Sn.a,null,e.operators?e.operators.map((function(e,a){var t=e.label,n=e.icon,l=e.onClick;return o.a.createElement(_n,{key:a,label:t,icon:n,onClick:l})})):""))})))))),We=He.title+l.name,Le=[o.a.createElement(ue,{size:"sm",color:"info",round:!0,href:"/admin/instances/"},o.a.createElement(ul.a,null),He.back)]}return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{display:"flex"},Le.map((function(e,a){return o.a.createElement($.a,{key:a,pl:2,pr:2},e)})))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12,sm:12,md:12},o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},We),o.a.createElement(fn,null,ze))),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:se,message:fe,open:""!==fe,closeNotification:he,close:!0})),o.a.createElement(Ve,null,o.a.createElement(Ti,{lang:qe,open:h,instanceID:a,current:l,onSuccess:function(e){Se(),ye("name of "+a+" changed to "+e),Ce()},onCancel:Se})),o.a.createElement(Ve,null,o.a.createElement(Di,{lang:qe,open:x,instanceID:a,current:l,onSuccess:function(e){je(),ye("cores of "+a+" changed to "+e),Ce()},onCancel:je})),o.a.createElement(Ve,null,o.a.createElement(Pi,{lang:qe,open:j,instanceID:a,current:l,onSuccess:function(e){Oe(),ye("memory of "+a+" changed to "+la(e)),Ce()},onCancel:Oe})),o.a.createElement(Ve,null,o.a.createElement(Bi,{lang:qe,open:N,instanceID:a,onSuccess:function(e){we(),ye("password of "+e+" modified"),Ce()},onCancel:we})),o.a.createElement(Ve,null,o.a.createElement(zi,{lang:qe,open:D,instanceID:a,current:l,index:le,onSuccess:function(e,a){Ne(),ye("size of disk "+e+" changed to "+la(a)),Ce()},onCancel:Ne})),o.a.createElement(Ve,null,o.a.createElement(qi,{lang:qe,open:B,instanceID:a,current:l,index:le,onSuccess:function(e){Te(),ye("size of disk "+e+" shrunk"),Ce()},onCancel:Te})),o.a.createElement(Ve,null,o.a.createElement(Li,{lang:qe,open:q,instanceID:a,current:l,onSuccess:function(e){Ie(),ye("CPU priority changed to "+e),Ce()},onCancel:Ie})),o.a.createElement(Ve,null,o.a.createElement(Gi,{lang:qe,open:V,instanceID:a,current:l,onSuccess:function(e){De(),ye("Disk IOPS changed to "+e),Ce()},onCancel:De})),o.a.createElement(Ve,null,o.a.createElement($i,{lang:qe,open:Q,instanceID:a,current:l,onSuccess:function(e,a){Ae();var t=[ra(e),ra(a)].join("/");ye("network bandwidth changed to "+t),Ce()},onCancel:Ae})),o.a.createElement(Yi,{lang:qe,open:ae,guestID:a,guestName:l?l.name:"",onSuccess:function(){Fe(),ye("monitor secret reset"),Ce()},onCancel:Fe}))}var Ki=t(602),Xi=Object(d.a)(Object(d.a)({},Qn),{},{cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),es=Object(m.a)(Xi),as={en:{createButton:"Create New Instance",batchCreate:"Batch Create",batchDelete:"Batch Delete",batchStop:"Batch Stop",enterBatch:"Enter Batch Mode",exitBatch:"Exit Batch Mode",tableTitle:"Cloud Instances",name:"Name",cell:"Host Cell",address:"Address",core:"Core",memory:"Memory",disk:"Disk",status:"Status",operates:"Operates",noResource:"No instances available",pool:"Compute Pool",disabled:"Disabled",offline:"Offline",allCells:"All Cells",allPools:"All Pools",keyword:"Key Word",search:"Search",notMatch:"No instance match keyword: "},cn:{createButton:"\u521b\u5efa\u4e91\u4e3b\u673a",batchCreate:"\u6279\u91cf\u521b\u5efa",batchDelete:"\u6279\u91cf\u5220\u9664",batchStop:"\u6279\u91cf\u505c\u6b62",enterBatch:"\u8fdb\u5165\u6279\u91cf\u6a21\u5f0f",exitBatch:"\u9000\u51fa\u6279\u91cf\u6a21\u5f0f",tableTitle:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",name:"\u540d\u79f0",cell:"\u627f\u8f7d\u8282\u70b9",address:"\u5730\u5740",core:"\u6838\u5fc3",memory:"\u5185\u5b58",disk:"\u78c1\u76d8",status:"\u72b6\u6001",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u4e91\u4e3b\u673a\u5b9e\u4f8b",pool:"\u8ba1\u7b97\u8d44\u6e90\u6c60",disabled:"\u5df2\u7981\u7528",offline:"\u79bb\u7ebf",allCells:"\u6240\u6709\u8282\u70b9",allPools:"\u6240\u6709\u8d44\u6e90\u6c60",keyword:"\u5173\u952e\u8bcd",search:"\u641c\u7d22",notMatch:"\u6ca1\u6709\u4e91\u4e3b\u673a\u5339\u914d\u5173\u952e\u8bcd: "}};function ts(e){var a=es(),t=Object(u.g)(),n=new URLSearchParams(t.search),l=n.get("pool"),r=n.get("cell"),c=o.a.useState(!1),i=Object(H.a)(c,2),s=i[0],m=i[1],p=o.a.useState(null),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(new Map),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(!1),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState({pool:"",cell:""}),O=Object(H.a)(j,2),w=O[0],_=O[1],N=o.a.useState([]),R=Object(H.a)(N,2),T=R[0],I=R[1],D=o.a.useState([]),A=Object(H.a)(D,2),P=A[0],F=A[1],B=o.a.useState(0),M=Object(H.a)(B,2),z=M[0],W=M[1],q=o.a.useState(""),L=Object(H.a)(q,2),U=L[0],G=L[1],V=o.a.useState(""),Z=Object(H.a)(V,2),Y=Z[0],J=Z[1],Q=o.a.useState(0),K=Object(H.a)(Q,2),X=K[0],ee=K[1],ae=o.a.useState(!1),te=Object(H.a)(ae,2),ne=te[0],oe=te[1],le=o.a.useState(!1),re=Object(H.a)(le,2),ce=re[0],ie=re[1],se=o.a.useState(!1),me=Object(H.a)(se,2),de=me[0],fe=me[1],be=o.a.useState(!1),ge=Object(H.a)(be,2),he=ge[0],Ee=ge[1],xe=o.a.useState(!1),Se=Object(H.a)(xe,2),je=Se[0],Oe=Se[1],we=o.a.useState(!1),_e=Object(H.a)(we,2),Ne=_e[0],Re=_e[1],Ie=o.a.useState(!1),De=Object(H.a)(Ie,2),Ae=De[0],Pe=De[1],Fe=o.a.useState(!1),Be=Object(H.a)(Fe,2),Me=Be[0],ze=Be[1],We=o.a.useState(!1),qe=Object(H.a)(We,2),He=qe[0],Le=qe[1],Ue=o.a.useState(!1),Ge=Object(H.a)(Ue,2),$e=Ge[0],Ze=Ge[1],Ye=o.a.useState(""),Je=Object(H.a)(Ye,2),Qe=Je[0],Ke=Je[1],Xe=o.a.useState(""),ea=Object(H.a)(Xe,2),aa=ea[0],la=ea[1],ra=o.a.useState(""),ca=Object(H.a)(ra,2),ia=ca[0],sa=ca[1],ua=o.a.useState("warning"),ma=Object(H.a)(ua,2),fa=ma[0],ba=ma[1],ga=o.a.useState(""),ha=Object(H.a)(ga,2),Ea=ha[0],va=ha[1],ya=function(){va("")},xa=o.a.useCallback((function(e){ba("warning"),va(e),setTimeout(ya,3e3)}),[ba,va]),ka=function(e){ba("info"),va(e),Ma(e),setTimeout(ya,3e3)},Ca=function(){G(""),J(""),W(0)},Sa=o.a.useCallback((function(){!function(e,a,t,n,o,l,r){var c={};c.limit=e||20,a&&(c.offset=a),t&&(c.pool=t),n&&(c.cell=n),o&&(c.keyword=o),Ha("/search/guests/",c,l,r)}(10,z,w.pool,w.cell,U,(function(e){var a=e.result,t=v,n=!1;if(ee(e.total),a){g(a);var o=[];t.forEach((function(e,t){a.some((function(e){return e.id===t}))||o.push(t)})),a.forEach((function(e){var a=e.id;t.has(a)||(t.set(a,!1),n||(n=!0))})),0!==o.length&&o.forEach((function(e){t.delete(e),n||(n=!0)}))}else g([]),0!==t.size&&(t.clear(),n=!0);n&&y(new Map(t))}),(function(e){xa(e),na()}))}),[w,v,xa,z,U]),ja=function(e){ie(!0),Ke(e)},Oa=function(){ie(!1)},wa=function(e){fe(!0),Ke(e)},_a=function(){fe(!1)},Na=function(e){Ee(!0),Ke(e)},Ra=function(){Ee(!1)},Ta=function(e){Oe(!0),Ke(e)},Ia=function(){Oe(!1)},Da=function(e){Re(!0),Ke(e)},Aa=function(){Re(!1)},Pa=function(e,a,t){Pe(!0),Ke(e),la(a),sa(t)},Fa=function(){Pe(!1)},Ba=function(){oe(!1)},za=function(){ze(!1)},Wa=function(){Le(!1)},qa=function(){Ze(!1)},La=function(){Sa()},Ua=function(e,a){var t=new Map(v);t.set(a,e),y(t)};if(o.a.useEffect((function(){if(s){var e=!0;Sa();var a=setInterval((function(){e&&Sa()}),5e3);return function(){e=!1,clearInterval(a)}}da((function(e){var a=[];e.forEach((function(e){a.push(e.name)})),0!==a.length?(I(a),F([]),_({pool:l||"",cell:r||""}),m(!0)):xa("no compute pools available")}),xa)}),[Sa,s,xa,l,r]),null===ta())return oa();var Ga,Va=e.lang,$a=as[Va];if(s&&b)if(0===b.length)Ga=""!==U?o.a.createElement($.a,{textAlign:"center"},o.a.createElement(gn,null,$a.notMatch+U)):o.a.createElement($.a,{textAlign:"center"},o.a.createElement(gn,null,$a.noResource));else{var Za,Ya,Ja,Qa;if(Za=C?o.a.createElement($.a,{display:"flex",alignItems:"center"},o.a.createElement($.a,null,o.a.createElement(Te.a,{onChange:function(e){var a,t=e.target.checked,n=new Map,o=Object(wr.a)(v.keys());try{for(o.s();!(a=o.n()).done;){var l=a.value;n.set(l,t)}}catch(r){o.e(r)}finally{o.f()}y(n)}})),o.a.createElement($.a,null,$a.name)):$a.name,X>10)Ja=0===X%10?X/10:(X-X%10)/10+1,Qa=0===z%10?z/10+1:(z-z%10)/10+1,Ya=o.a.createElement($.a,{justifyContent:"center",display:"flex"},o.a.createElement($.a,{m:1},o.a.createElement(Ki.a,{count:Ja,page:Qa,onChange:function(e,a){W(10*(a-1))},color:"primary",boundaryCount:3,showFirstButton:!0,showLastButton:!0})));else Ya=o.a.createElement("div",null);Ga=o.a.createElement("div",null,o.a.createElement(Pr,{color:"primary",headers:[Za,$a.cell,$a.address,$a.core,$a.memory,$a.disk,$a.status,$a.operates],rows:b.map((function(e){var a=e.id;return o.a.createElement(pc,{key:e.id,instance:e,lang:Va,checked:!(!v||!v.has(a))&&v.get(a),checkable:C,onCheckStatusChanged:Ua,onNotify:ka,onError:xa,onDelete:ja,onMediaStart:wa,onInsertMedia:Na,onResetSystem:Ta,onBuildImage:Da,onStatusChange:La,onMigrateInstance:Pa})}))}),Ya)}else Ga=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});var Ka=[{label:$a.createButton,icon:Dt.a,color:"info",onClick:function(){oe(!0)}},{label:$a.batchCreate,icon:Tr.a,color:"info",onClick:function(){Ze(!0)}}];C?Ka.push({label:$a.batchDelete,icon:Ht.a,color:"danger",onClick:function(){Le(!0)}},{label:$a.batchStop,icon:Nr.a,color:"info",onClick:function(){ze(!0)}},{label:$a.exitBatch,icon:Dr.a,color:"success",onClick:function(){S(!1)}}):Ka.push({label:$a.enterBatch,icon:Xo.a,color:"info",onClick:function(){var e,a=new Map,t=Object(wr.a)(v.keys());try{for(t.s();!(e=t.n()).done;){var n=e.value;a.set(n,!1)}}catch(o){t.e(o)}finally{t.f()}y(a),S(!0)}});var Xa=[{label:$a.allPools,value:""}];T.forEach((function(e){Xa.push({label:e,value:e})}));var et=[{label:$a.allCells,value:""}];P.forEach((function(e){var a=e.name;e.address&&(a+="("+e.address+")"),e.alive||(a+="-"+$a.offline),e.enabled||(a+="-"+$a.disabled),et.push({label:a,value:e.name})}));var at=o.a.createElement($.a,{m:0,pb:2},o.a.createElement(ke.a,{htmlFor:"pool",className:a.cardCategory},$a.pool),o.a.createElement(ye.a,{value:w.pool,onChange:function(e){var a=e.target.value,t=[];pa(a,(function(e){e.forEach((function(e){t.push(e)})),F(t),_({pool:a,cell:""}),Ca()}),xa)},inputProps:{name:"pool",id:"pool"},fullWidth:!0},Xa.map((function(e,a){return o.a.createElement(pe.a,{value:e.value,key:a},e.label)})))),tt=o.a.createElement($.a,{m:0,pb:2},o.a.createElement(ke.a,{htmlFor:"cell",className:a.cardCategory},$a.cell),o.a.createElement(ye.a,{value:w.cell,onChange:function(e){var a=e.target.value;_((function(e){return Object(d.a)(Object(d.a)({},e),{},{cell:a})})),Ca()},inputProps:{name:"cell",id:"cell"},fullWidth:!0},et.map((function(e,a){return o.a.createElement(pe.a,{value:e.value,key:a},e.label)})))),nt=[];return v&&(v.forEach((function(e,a){e&&nt.push(a)})),nt.sort()),o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{display:"flex"},Ka.map((function(e,a){var t=e.label,n=e.color,l=e.icon,r=e.onClick;return o.a.createElement($.a,{key:a,m:1},o.a.createElement(ue,{size:"sm",color:n,round:!0,onClick:r},o.a.createElement(l),t))})))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{ml:1},o.a.createElement(an,null,o.a.createElement(Ve,{xs:5,sm:3,md:2},o.a.createElement($.a,{pt:1},at)),o.a.createElement(Ve,{xs:6,sm:4,md:2},o.a.createElement($.a,{pt:1},tt)),o.a.createElement(Ve,{xs:10,sm:6,md:4},o.a.createElement(Ce.a,{value:Y,label:$a.keyword,onChange:function(e){var a=e.target.value;J(a)},margin:"normal",fullWidth:!0})),o.a.createElement(Ve,{xs:2,sm:1},o.a.createElement($.a,{pt:4},o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){J(""),G(Y),W(0),Sa()}},$a.search)))))),o.a.createElement(Ve,{xs:12},o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",{className:a.cardTitleWhite},$a.tableTitle)),o.a.createElement(fn,null,Ga))),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:fa,message:Ea,open:""!==Ea,closeNotification:ya,close:!0})),o.a.createElement(Ve,null,o.a.createElement(Sc,{lang:Va,open:ne,onSuccess:function(e){Ba(),ka("new instance "+e+" created"),Sa()},onCancel:Ba})),o.a.createElement(Ve,null,o.a.createElement(bc,{lang:Va,instanceID:Qe,open:ce,onSuccess:function(e){Oa(),ka("instance "+e+" deleted"),Sa()},onCancel:Oa})),o.a.createElement(Ve,null,o.a.createElement(Oc,{lang:Va,instanceID:Qe,open:de,onSuccess:function(e){_a(),ka("instance "+e+" started with media"),Sa()},onCancel:_a})),o.a.createElement(Ve,null,o.a.createElement(_c,{lang:Va,instanceID:Qe,open:he,onSuccess:function(e){Ra(),ka("instance "+e+" started with media"),Sa()},onCancel:Ra})),o.a.createElement(Ve,null,o.a.createElement(Ic,{lang:Va,instanceID:Qe,open:je,onSuccess:function(e){Ia(),ka("guest system of "+e+" reset")},onCancel:Ia})),o.a.createElement(Ve,null,o.a.createElement(Rc,{lang:Va,instanceID:Qe,open:Ne,onSuccess:function(e){Aa(),ka("new image "+e+" created from "+Qe)},onCancel:Aa})),o.a.createElement(Ve,null,o.a.createElement(Ac,{lang:Va,instanceID:Qe,sourcePool:aa,sourceCell:ia,open:Ae,onSuccess:function(e){Fa(),ka("instance "+e+" migrated"),Sa()},onCancel:Fa})),o.a.createElement(Ve,null,o.a.createElement(Bc,{lang:Va,open:Me,targets:Me?nt:[],onSuccess:function(){za(),Sa()},onCancel:za})),o.a.createElement(Ve,null,o.a.createElement(zc,{lang:Va,open:He,targets:He?nt:[],onSuccess:function(){Wa(),Sa()},onCancel:Wa})),o.a.createElement(Ve,null,o.a.createElement(qc,{lang:Va,open:$e,onSuccess:function(){qa(),Sa()},onCancel:qa})))}var ns=t(190),os=t.n(ns),ls=t(191),rs=t.n(ls),cs={en:{title:"Remove Security Policy Rule",content:"Are you sure to remove ",content2:"th rule",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u5b89\u5168\u89c4\u5219",content:"\u662f\u5426\u5220\u9664\u7b2c ",content2:"\u4e2a\u5b89\u5168\u89c4\u5219",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function is(e){var a=e.lang,t=e.open,n=e.guestID,l=e.index,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!0),s=Object(H.a)(i,2),u=s[0],m=s[1],d=o.a.useState(""),p=Object(H.a)(d,2),f=p[0],b=p[1],g=cs[a],h=g.title,E=function(e){m(!0),b(e)},v=function(){m(!0),b(""),r(n,l)},y=g.content+(l+1)+g.content2,x=[{color:"transparent",label:g.cancel,onClick:function(){b(""),c()}},{color:"info",label:g.confirm,onClick:function(){m(!1),function(e,a,t,n){Va("/guests/"+e+"/security_policy/rules/"+a,()=>{t(e,a)},n)}(n,l,v,E)}}];return o.a.createElement(Xe,{size:"xs",open:t,prompt:f,title:h,buttons:x,content:y,operatable:u})}var ss={en:{title:"Add Security Policy Rule",action:"Action",accept:"Accept",reject:"Reject",protocol:"Protocol",sourceAddress:"Source Address",targetPort:"Target Port",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6dfb\u52a0\u5b89\u5168\u89c4\u5219",action:"\u5904\u7406",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",protocol:"\u534f\u8bae",sourceAddress:"\u6765\u6e90\u5730\u5740",targetPort:"\u76ee\u6807\u7aef\u53e3",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function us(e){var a={action:"accept",protocol:"",port:""},t=e.lang,n=e.guestID,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!0),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(""),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(a),E=Object(H.a)(h,2),v=E[0],y=E[1],x=ss[t],k=x.title,C=[{label:x.accept,value:"accept"},{label:x.reject,value:"reject"}],S=function(e){m(!0),g(e)},j=function(){g(""),y(a)},O=function(e){m(!0),j(),r(e)},w=function(e){return function(a){var t=a.target.value;y((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}},_=[{type:"radio",label:x.action,onChange:w("action"),value:v.action,options:C,required:!0,oneRow:!0,xs:10,sm:8,md:6},{type:"select",label:x.protocol,onChange:w("protocol"),value:v.protocol,options:[{label:"TCP",value:"tcp"},{label:"UDP",value:"udp"}],required:!0,oneRow:!0,xs:8,sm:6,md:4},{type:"text",label:x.targetPort,onChange:w("port"),value:v.port,required:!0,oneRow:!0,xs:8,sm:6,md:4}],N=o.a.createElement(Ae,{inputs:_}),R=[{color:"transparent",label:x.cancel,onClick:function(){j(),c()}},{color:"info",label:x.confirm,onClick:function(){if(m(!1),v.action)if(v.protocol)if(v.port){var e=Number.parseInt(v.port);Number.isNaN(e)?S("invalid target port: "+v.port):function(e,a,t,n,o,l){Ha("/guests/"+e+"/security_policy/rules/",{action:a,protocol:t,to_port:n},()=>{o(e)},l)}(n,v.action,v.protocol,e,O,S)}else S("must specify target port");else S("must specify protocol");else S("must specify action")}}];return o.a.createElement(Xe,{size:"sm",open:l,prompt:b,title:k,buttons:R,content:N,operatable:u})}var ms={en:{title:"Modify Security Policy Rule",action:"Action",accept:"Accept",reject:"Reject",protocol:"Protocol",sourceModifyress:"Source Address",targetPort:"Target Port",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u5b89\u5168\u89c4\u5219",action:"\u5904\u7406",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",protocol:"\u534f\u8bae",sourceModifyress:"\u6765\u6e90\u5730\u5740",targetPort:"\u76ee\u6807\u7aef\u53e3",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function ds(e){var a=e.lang,t=e.guestID,n=e.rule,l=e.open,r=e.onSuccess,c=e.onCancel,i={action:n.action,protocol:n.protocol,port:n.to_port},s=o.a.useState(!0),u=Object(H.a)(s,2),m=u[0],p=u[1],f=o.a.useState(""),b=Object(H.a)(f,2),g=b[0],h=b[1],E=o.a.useState(!1),v=Object(H.a)(E,2),y=v[0],x=v[1],k=o.a.useState(!1),C=Object(H.a)(k,2),S=C[0],j=C[1],O=o.a.useState(i),w=Object(H.a)(O,2),_=w[0],N=w[1],R=ms[a],T=R.title,I=[{label:R.accept,value:"accept"},{label:R.reject,value:"reject"}],D=o.a.useCallback((function(e){y&&(p(!0),h(e))}),[y]),A=function(){h(""),N(i),j(!1)},P=function(e){p(!0),A(),r(e)},F=function(e){return function(a){var t=a.target.value;N((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}};o.a.useEffect((function(){if(t&&n)return x(!0),N({action:n.action,protocol:n.protocol,port:n.to_port}),j(!0),function(){x(!1)}}),[y,l,t,n,D]);var B,M=[{color:"transparent",label:R.cancel,onClick:function(){A(),c()}}];if(S){var z=[{type:"radio",label:R.action,onChange:F("action"),value:_.action,options:I,required:!0,oneRow:!0,xs:10,sm:8,md:6},{type:"select",label:R.protocol,onChange:F("protocol"),value:_.protocol,options:[{label:"TCP",value:"tcp"},{label:"UDP",value:"udp"}],required:!0,oneRow:!0,xs:8,sm:6,md:4},{type:"text",label:R.targetPort,onChange:F("port"),value:_.port,required:!0,oneRow:!0,xs:8,sm:6,md:4}];B=o.a.createElement(Ae,{inputs:z}),M.push({color:"info",label:R.confirm,onClick:function(){if(p(!1),_.action)if(_.protocol)if(_.port){var e=Number.parseInt(_.port);Number.isNaN(e)?D("invalid target port: "+_.port):function(e,a,t,n,o,l,r){Ga("/guests/"+e+"/security_policy/rules/"+a,{action:t,protocol:n,to_port:o},()=>{l(e,a)},r)}(t,n.index,_.action,_.protocol,e,P,D)}else D("must specify target port");else D("must specify protocol");else D("must specify action")}})}else B=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:l,prompt:g,title:T,buttons:M,content:B,operatable:m})}var ps=Object(d.a)(Object(d.a)({},z),{},{cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),fs=Object(m.a)(ps),bs={en:{createButton:"Add New Rule",tableTitle:"Security Policy Rules",rule:"Rule",action:"Action",protocol:"Protocol",sourceAddress:"Source Address",targetPort:"Target Port",default:"Default Action",accept:"Accept",reject:"Reject",operates:"Operates",noResource:"No security policy available",modify:"Modify",remove:"Remove",moveUp:"Move Up",moveDown:"Move Down",back:"Back"},cn:{createButton:"\u6dfb\u52a0\u65b0\u89c4\u5219",tableTitle:"\u5b89\u5168\u89c4\u5219",rule:"\u89c4\u5219",action:"\u5904\u7406",protocol:"\u534f\u8bae",sourceAddress:"\u6765\u6e90\u5730\u5740",targetPort:"\u76ee\u6807\u7aef\u53e3",default:"\u9ed8\u8ba4\u5904\u7406",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u5b89\u5168\u7b56\u7565\u7ec4",modify:"\u4fee\u6539",remove:"\u79fb\u9664",moveUp:"\u4e0a\u79fb",moveDown:"\u4e0b\u79fb",back:"\u8fd4\u56de"}};function gs(e){var a,t=e.match.params.id,n=e.lang,l=bs[n],r=fs(),c=o.a.useState(!1),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(null),d=Object(H.a)(m,2),p=d[0],f=d[1],b=o.a.useState(!1),g=Object(H.a)(b,2),h=g[0],E=g[1],v=o.a.useState(!1),y=Object(H.a)(v,2),x=y[0],k=y[1],C=o.a.useState(!1),S=Object(H.a)(C,2),j=S[0],O=S[1],w=o.a.useState(""),_=Object(H.a)(w,2),N=_[0],R=_[1],T=o.a.useState("warning"),I=Object(H.a)(T,2),D=I[0],A=I[1],P=o.a.useState(""),F=Object(H.a)(P,2),B=F[0],M=F[1],z=function(){M("")},W=o.a.useCallback((function(e){if(s){A("warning"),M(e),setTimeout(z,3e3)}}),[A,M,s]),q=o.a.useCallback((function(){s&&function(e,a,t){Wa("/guests/"+e+"/security_policy/",a,t)}(t,f,W)}),[t,W,s]),L=function(e){if(s){A("info"),M(e),Ma(e),setTimeout(z,3e3)}},U=function(){k(!1)},G=function(){O(!1)},V=function(){E(!1)},Z=function(e){!function(e,a,t,n){Ga("/guests/"+e+"/security_policy/rules/"+a+"/order",{direction:"up"},()=>{t(e,a)},n)}(t,e.index,(function(e,a){L(a+"th rule moved up"),q()}),W)},Y=function(e){!function(e,a,t,n){Ga("/guests/"+e+"/security_policy/rules/"+a+"/order",{direction:"down"},()=>{t(e,a)},n)}(t,e.index,(function(e,a){L(a+"th rule moved down"),q()}),W)};if(o.a.useEffect((function(){return u(!0),q(),function(){u(!1)}}),[q]),null===p)a=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});else{var J=o.a.createElement(we.a,{component:"fieldset",fullWidth:!0,disabled:!0},o.a.createElement(je.a,{name:"type",value:p.default_action,onChange:function(e){var a=e.target.value;!function(e,a,t,n){Ga("/guests/"+e+"/security_policy/default_action",{action:a},()=>{t(e)},n)}(t,a,(function(){L("default action changed to "+a),q()}),W)},row:!0},o.a.createElement($.a,{display:"flex",alignItems:"center"},o.a.createElement($.a,null,o.a.createElement(Oe.a,{value:"accept",control:o.a.createElement(Se.a,null),label:l.accept})),o.a.createElement($.a,null,o.a.createElement(Oe.a,{value:"reject",control:o.a.createElement(Se.a,null),label:l.reject}))))),K=[[l.default,J]];p.rules&&0!==p.rules.length?p.rules.forEach((function(e,a){var t={index:a,action:e.action,protocol:e.protocol,to_port:e.to_port},n=[{onClick:function(e){return function(e){k(!0),R(e)}(t)},icon:Qo.a,label:l.modify},{onClick:function(e){return function(e){O(!0),R(e)}(t)},icon:Ht.a,label:l.remove}];p.rules.length-1!==a&&n.push({onClick:function(e){return Y(t)},icon:os.a,label:l.moveDown}),0!==a&&n.push({onClick:function(e){return Z(t)},icon:rs.a,label:l.moveUp}),K.push(function(e,a,t){var n=t.map((function(e,a){return o.a.createElement(_n,{label:e.label,icon:e.icon,onClick:e.onClick,key:a})})),c=a.action,i=a.protocol,s=a.from_address,u=a.to_port;return[e,"accept"===c?o.a.createElement(Q.a,{title:l.accept,placement:"top"},o.a.createElement(Ft.a,{className:r.successText})):o.a.createElement(Q.a,{title:l.reject,placement:"top"},o.a.createElement(Mt.a,{className:r.dangerText})),i,s,u,n]}(a,e,n))})):K.push([o.a.createElement($.a,{display:"flex",justifyContent:"center"},o.a.createElement(gn,null,l.noResource))]),a=o.a.createElement(wn,{color:"primary",headers:[l.rule,l.action,l.protocol,l.sourceAddress,l.targetPort,l.operates],rows:K})}var X=[{href:"/admin/instances/",icon:ul.a,label:l.back},{onClick:function(){E(!0)},icon:Dt.a,label:l.createButton}];return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12,sm:12,md:12},o.a.createElement(an,null,o.a.createElement(Ve,{xs:12,sm:6,md:4},o.a.createElement($.a,{display:"flex"},X.map((function(e,a){return e.href?o.a.createElement($.a,{p:1,key:a},o.a.createElement(ue,{size:"sm",color:"info",round:!0,href:e.href},o.a.createElement(e.icon),e.label)):o.a.createElement($.a,{p:1,key:a},o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:e.onClick},o.a.createElement(e.icon),e.label))})))))),o.a.createElement(Ve,{xs:12,sm:12,md:12},o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",{className:r.cardTitleWhite},l.tableTitle)),o.a.createElement(fn,null,a))),o.a.createElement(vn,{place:"tr",color:D,message:B,open:""!==B,closeNotification:z,close:!0}),o.a.createElement(us,{lang:n,open:h,guestID:t,onSuccess:function(){V(),L("new security policy rule added"),q()},onCancel:V}),o.a.createElement(ds,{lang:n,open:x,guestID:t,rule:N,onSuccess:function(e){U(),L(e+"th rule modified"),q()},onCancel:U}),o.a.createElement(is,{lang:n,open:j,guestID:t,index:N.index,onSuccess:function(e,a){G(),L(a+"the rule removed"),q()},onCancel:G}))}var hs=t(192),Es=t.n(hs),vs=t(301),ys=t.n(vs),xs=t(601),ks=t(599),Cs={cardTitle:{float:"left",padding:"10px 10px 10px 0px",lineHeight:"24px"},cardTitleRTL:{float:"right",padding:"10px 0px 10px 10px !important"},displayNone:{display:"none !important"},tabsRoot:{minHeight:"unset !important",overflowX:"visible","& $tabRootButton":{fontSize:"0.875rem"}},tabRootButton:{minHeight:"unset !important",minWidth:"unset !important",width:"unset !important",height:"unset !important",maxWidth:"unset !important",maxHeight:"unset !important",padding:"10px 15px",borderRadius:"3px",lineHeight:"24px",border:"0 !important",color:"#FFF !important",marginLeft:"4px","&:last-child":{marginLeft:"0px"}},tabSelected:{backgroundColor:"rgba("+p("#FFF")+", 0.2)",transition:"0.2s background-color 0.1s"},tabWrapper:{display:"inline-block",minHeight:"unset !important",minWidth:"unset !important",width:"unset !important",height:"unset !important",maxWidth:"unset !important",maxHeight:"unset !important",fontWeight:"500",fontSize:"12px",marginTop:"1px","& > svg,& > .material-icons":{verticalAlign:"middle",margin:"-1px 5px 0 0 !important"}}},Ss=Object(m.a)(Cs);function js(e){var a,t=o.a.useState(0),n=Object(H.a)(t,2),l=n[0],r=n[1],c=Ss(),i=e.headerColor,s=e.plainTabs,u=e.tabs,m=e.title,d=e.rtlActive,p=V()((a={},Object(le.a)(a,c.cardTitle,!0),Object(le.a)(a,c.cardTitleRTL,d),a));return o.a.createElement(ln,{plain:s},o.a.createElement(un,{color:i,plain:s},void 0!==m?o.a.createElement("div",{className:p},m):null,o.a.createElement(xs.a,{value:l,onChange:function(e,a){r(a)},classes:{root:c.tabsRoot,indicator:c.displayNone,scrollButtons:c.displayNone},variant:"scrollable",scrollButtons:"auto"},u.map((function(e,a){var t={};return e.tabIcon&&(t={icon:o.a.createElement(e.tabIcon,null)}),o.a.createElement(ks.a,Object.assign({classes:{root:c.tabRootButton,selected:c.tabSelected,wrapper:c.tabWrapper},key:a,label:e.tabName},t))})))),o.a.createElement(fn,null,u.map((function(e,a){return a===l?o.a.createElement("div",{key:a},e.tabContent):null}))))}var Os=t(300),ws=t.n(Os),_s=t(115),Ns=t.n(_s),Rs={en:{title:"Create User",user:"Username",password:"Password",password2:"Confirm Password",nick:"Nickname",mail:"Mail",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u521b\u5efa\u7528\u6237",user:"\u7528\u6237\u540d",password:"\u5bc6\u7801",password2:"\u786e\u8ba4\u5bc6\u7801",nick:"\u6635\u79f0",mail:"\u90ae\u7bb1",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Ts(e){var a,t={user:"",password:"",password2:"",nick:"",mail:""},n=e.lang,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(""),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(t),f=Object(H.a)(p,2),b=f[0],g=f[1],h=Rs[n],E=function(e){m(e)},v=function(){m(""),g(t)},y=function(e){v(),r(e)},x=function(e){return function(a){var t=a.target.value;g((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}},k=o.a.createElement(Ne.a,{container:!0},o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:6,md:4},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:h.user,onChange:x("user"),value:b.user,margin:"normal",required:!0,fullWidth:!0})))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:10,md:8},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:h.password,onChange:x("password"),value:b.password,margin:"normal",type:"password",required:!0,fullWidth:!0})))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:10,md:8},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:h.password2,onChange:x("password2"),value:b.password2,margin:"normal",type:"password",required:!0,fullWidth:!0})))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:10,md:8},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:h.nick,onChange:x("nick"),value:b.nick,margin:"normal",fullWidth:!0})))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:10,md:8},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:h.mail,onChange:x("mail"),value:b.mail,margin:"normal",fullWidth:!0})))));return a=u&&""!==u?o.a.createElement(Ve,{xs:12},o.a.createElement(Ke,{message:u,color:"danger"})):o.a.createElement(Ve,{xs:12}),o.a.createElement(Pe.a,{open:l,"aria-labelledby":h.title,maxWidth:"sm",fullWidth:!0},o.a.createElement(Me.a,null,h.title),o.a.createElement(Be.a,null,o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:12},k),a)),o.a.createElement(Fe.a,null,o.a.createElement(ue,{onClick:function(){v(),c()},color:"transparent",autoFocus:!0},h.cancel),o.a.createElement(ue,{onClick:function(){var e=new RegExp("[^\\w-.]");b.user?e.test(b.user)?E("only letter/digit/'-'/'_'/'.' allowed in username"):b.password?b.password2===b.password?function(e,a,t,n,o,l){const r="/users/"+e;var c={password:a};t&&(c.nick=t),n&&(c.mail=n),Ha(r,c,()=>{o(e)},l)}(b.user,b.password,b.nick,b.mail,y,E):E("password mismatch"):E("please input password"):E("must specify user name")},color:"info"},h.confirm)))}var Is={en:{title:"Modify User",user:"Username",nick:"Nickname",mail:"Mail",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u7528\u6237",user:"\u7528\u6237\u540d",nick:"\u6635\u79f0",mail:"\u90ae\u7bb1",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Ds(e){var a={user:"",nick:"",mail:""},t=e.lang,n=e.name,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!1),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(""),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(a),E=Object(H.a)(h,2),v=E[0],y=E[1],x=Is[t],k=function(e){g(e)},C=function(){g(""),y(a),m(!1)},S=function(e){C(),r(e)};o.a.useEffect((function(){if(n&&l&&!u){!function(e,a,t){Wa("/users/"+e,a,t)}(n,(function(e){y({user:n,nick:e.nick?e.nick:"",mail:e.mail?e.mail:""}),m(!0)}),k)}}),[u,l,n]);var j,O,w=function(e){return function(a){var t=a.target.value;y((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}};return j=u?o.a.createElement(Ne.a,{container:!0},o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:6,md:4},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:x.user,value:v.user,margin:"normal",disabled:!0,required:!0,fullWidth:!0})))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:10,md:8},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:x.nick,onChange:w("nick"),value:v.nick,margin:"normal",fullWidth:!0})))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:10,md:8},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:x.mail,onChange:w("mail"),value:v.mail,margin:"normal",fullWidth:!0}))))):o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}}),O=b&&""!==b?o.a.createElement(Ve,{xs:12},o.a.createElement(Ke,{message:b,color:"danger"})):o.a.createElement(Ve,{xs:12}),o.a.createElement(Pe.a,{open:l,"aria-labelledby":x.title,maxWidth:"sm",fullWidth:!0},o.a.createElement(Me.a,null,x.title),o.a.createElement(Be.a,null,o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:12},j),O)),o.a.createElement(Fe.a,null,o.a.createElement(ue,{onClick:function(){C(),c()},color:"transparent",autoFocus:!0},x.cancel),o.a.createElement(ue,{onClick:function(){!function(e,a,t,n,o){const l="/users/"+e;var r={};a&&(r.nick=a),t&&(r.mail=t),Ga(l,r,()=>{n(e)},o)}(v.user,v.nick,v.mail,S,k)},color:"info"},x.confirm)))}var As={en:{title:"Delete User",content:"Are you sure to delete user ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u7528\u6237",content:"\u662f\u5426\u5220\u9664\u7528\u6237 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Ps(e){var a,t=e.lang,n=e.name,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(""),s=Object(H.a)(i,2),u=s[0],m=s[1],d=As[t],p=function(e){m(e)},f=function(e){m(""),r(e)};return a=u&&""!==u?o.a.createElement(Ve,{xs:12},o.a.createElement(Ke,{message:u,color:"danger"})):o.a.createElement(Ve,{xs:12}),o.a.createElement(Pe.a,{open:l,"aria-labelledby":d.title,maxWidth:"xs",fullWidth:!0},o.a.createElement(Me.a,null,d.title),o.a.createElement(Be.a,null,o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:12},d.content+n),a)),o.a.createElement(Fe.a,null,o.a.createElement(ue,{onClick:function(){m(""),c()},color:"transparent",autoFocus:!0},d.cancel),o.a.createElement(ue,{onClick:function(){!function(e,a,t){Va("/users/"+e,()=>{a(e)},t)}(n,f,p)},color:"info"},d.confirm)))}var Fs=Object(d.a)({},jn),Bs=Object(m.a)(Fs),Ms={en:{createButton:"Create New User",name:"User Name",modify:"Modify",delete:"Delete",operates:"Operates",noResource:"No User Available"},cn:{createButton:"\u521b\u5efa\u65b0\u7528\u6237",name:"\u7528\u6237\u540d",modify:"\u4fee\u6539",delete:"\u5220\u9664",operates:"\u64cd\u4f5c",noResource:"\u5c1a\u672a\u521b\u5efa\u7528\u6237"}};function zs(e){var a=Bs(),t=o.a.useState(null),n=Object(H.a)(t,2),l=n[0],r=n[1],c=o.a.useState(!1),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(!1),d=Object(H.a)(m,2),p=d[0],f=d[1],b=o.a.useState(!1),g=Object(H.a)(b,2),h=g[0],E=g[1],v=o.a.useState(""),y=Object(H.a)(v,2),x=y[0],k=y[1],C=o.a.useState("warning"),S=Object(H.a)(C,2),j=S[0],O=S[1],w=o.a.useState(""),_=Object(H.a)(w,2),N=_[0],R=_[1],T=function(){R("")},I=o.a.useCallback((function(e){O("warning"),R(e),setTimeout(T,3e3)}),[O,R]),D=o.a.useCallback((function(){Wa("/users/",r,(function(e){I(e),na()}))}),[I]),A=function(e){O("info"),R(e),Ma(e),setTimeout(T,3e3)},P=function(){E(!1)},F=function(){f(!1)},B=function(){u(!1)};o.a.useEffect((function(){D()}),[D]);var M=ta();if(null===M)return oa();var z,W=e.lang,q=Ms[W];z=null===l?o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}}):0===l.length?o.a.createElement(gn,null,q.noResource):o.a.createElement(Pr,{color:"primary",headers:[q.name,q.operates],rows:l.map((function(e,t){var n=e,l=[o.a.createElement(_n,{key:"modify",label:q.modify,icon:Ns.a,onClick:function(){return function(e){E(!0),k(e)}(n)}})];return n!==M.user&&l.push(o.a.createElement(_n,{key:"delete",label:q.delete,icon:Ht.a,onClick:function(){return function(e){f(!0),k(e)}(n)}})),o.a.createElement(kn.a,{className:a.tableBodyRow,key:t},o.a.createElement(Sn.a,{className:a.tableCell},e),o.a.createElement(Sn.a,{className:a.tableCell},l))}))});var L=[o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){u(!0)}},o.a.createElement(ws.a,null),q.createButton)];return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{display:"flex"},L.map((function(e,a){return o.a.createElement($.a,{key:a,m:1},e)})))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12},o.a.createElement(vi.a,{maxWidth:"sm"},z)),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:j,message:N,open:""!==N,closeNotification:T,close:!0})),o.a.createElement(Ve,null,o.a.createElement(Ts,{lang:W,open:s,onSuccess:function(e){B(),A("user "+e+" created"),D()},onCancel:B})),o.a.createElement(Ve,null,o.a.createElement(Ds,{lang:W,name:x,open:h,onSuccess:function(e){P(),A("user "+e+" modified"),D()},onCancel:P})),o.a.createElement(Ve,null,o.a.createElement(Ps,{lang:W,name:x,open:p,onSuccess:function(e){F(),A("user "+e+" deleted"),D()},onCancel:F})))}var Ws={en:{title:"Add Group",name:"Group Name",display:"Display Name",role:"Roles",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6dfb\u52a0\u7528\u6237\u7ec4",name:"\u7ec4\u540d\u79f0",display:"\u663e\u793a\u540d\u79f0",role:"\u89d2\u8272\u6e05\u5355",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function qs(e){var a={name:"",display:"",checked:new Set},t=e.lang,n=e.open,l=e.onSuccess,r=e.onCancel,c=Ws[t],i=o.a.useState([]),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(!1),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(""),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(a),k=Object(H.a)(x,2),C=k[0],S=k[1],j=function(e){y(e)},O=function(){y(""),S(a),g(!1)},w=function(e){O(),l(e)};o.a.useEffect((function(){if(n&&!b){Ba((function(e){m(e),g(!0)}),j)}}),[b,n]);var _,N,R=function(e){return function(a){var t=a.target.value;S((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}},T=function(e){var a=e.target.value,t=e.target.checked,n=Object(d.a)({},C);t?n.checked.add(a):n.checked.delete(a),S(n)};return _=b?o.a.createElement(Ne.a,{container:!0},o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:6,md:4},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:c.name,onChange:R("name"),value:C.name,margin:"normal",required:!0,fullWidth:!0})))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:10,md:8},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:c.display,onChange:R("display"),value:C.display,margin:"normal",required:!0,fullWidth:!0})))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(we.a,{component:"fieldset",fullWidth:!0},o.a.createElement(_e.a,{component:"legend"},c.role),o.a.createElement(Re.a,null,o.a.createElement(Ne.a,{container:!0},u.map((function(e,a){var t;return t=!!C.checked.has(e),o.a.createElement(Ve,{xs:6,sm:3,key:a},o.a.createElement(Oe.a,{control:o.a.createElement(Te.a,{checked:t,onChange:T,value:e}),label:e}))}))))))))):o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}}),N=v&&""!==v?o.a.createElement(Ve,{xs:12},o.a.createElement(Ke,{message:v,color:"danger"})):o.a.createElement(Ve,{xs:12}),o.a.createElement(Pe.a,{open:n,"aria-labelledby":c.title,maxWidth:"md",fullWidth:!0},o.a.createElement(Me.a,null,c.title),o.a.createElement(Be.a,null,o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:12},_),N)),o.a.createElement(Fe.a,null,o.a.createElement(ue,{onClick:function(){O(),r()},color:"transparent",autoFocus:!0},c.cancel),o.a.createElement(ue,{onClick:function(){if(C.name)if(C.display)if(C.checked&&0!==C.checked.size){var e=[];u.forEach((function(a){C.checked.has(a)&&e.push(a)})),function(e,a,t,n,o){const l="/user_groups/"+e;var r={display:a};t&&(r.role=t),Ha(l,r,()=>{n(e)},o)}(C.name,C.display,e,w,j)}else j("Select at least one role");else j("Display name required");else j("Group name required")},color:"info"},c.confirm)))}var Hs={en:{title:"Modify Group",name:"Group Name",display:"Display Name",role:"Roles",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u7528\u6237\u7ec4",name:"\u7ec4\u540d\u79f0",display:"\u663e\u793a\u540d\u79f0",role:"\u89d2\u8272\u6e05\u5355",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Ls(e){var a={display:"",checked:new Set},t=e.lang,n=e.name,l=e.open,r=e.onSuccess,c=e.onCancel,i=Hs[t],s=o.a.useState([]),u=Object(H.a)(s,2),m=u[0],p=u[1],f=o.a.useState(!1),b=Object(H.a)(f,2),g=b[0],h=b[1],E=o.a.useState(""),v=Object(H.a)(E,2),y=v[0],x=v[1],k=o.a.useState(a),C=Object(H.a)(k,2),S=C[0],j=C[1],O=function(e){x(e)},w=function(){x(""),j(a),h(!1)},_=function(){w(),r(n)};o.a.useEffect((function(){if(n&&l&&!g){var e=[],a=function(a){var t=a.display,n=a.role,o=new Set;n.forEach((function(e){o.add(e)})),j({display:t,checked:o}),p(e),h(!0)};Ba((function(t){e=t,function(e,a,t){Wa("/user_groups/"+e,a,t)}(n,a,O)}),O)}}),[g,l,n]);var N,R,T=function(e){var a=e.target.value,t=e.target.checked,n=Object(d.a)({},S);t?n.checked.add(a):n.checked.delete(a),j(n)};return N=g?o.a.createElement(Ne.a,{container:!0},o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:6,md:4},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:i.name,value:n,margin:"normal",disabled:!0,required:!0,fullWidth:!0})))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:10,md:8},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:i.display,onChange:function(e){return function(a){var t=a.target.value;j((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}}("display"),value:S.display,margin:"normal",required:!0,fullWidth:!0})))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(we.a,{component:"fieldset",fullWidth:!0},o.a.createElement(_e.a,{component:"legend"},i.role),o.a.createElement(Re.a,null,o.a.createElement(Ne.a,{container:!0},m.map((function(e,a){var t;return t=!!S.checked.has(e),o.a.createElement(Ve,{xs:6,sm:3,key:a},o.a.createElement(Oe.a,{control:o.a.createElement(Te.a,{checked:t,onChange:T,value:e}),label:e}))}))))))))):o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}}),R=y&&""!==y?o.a.createElement(Ve,{xs:12},o.a.createElement(Ke,{message:y,color:"danger"})):o.a.createElement(Ve,{xs:12}),o.a.createElement(Pe.a,{open:l,"aria-labelledby":i.title,maxWidth:"md",fullWidth:!0},o.a.createElement(Me.a,null,i.title),o.a.createElement(Be.a,null,o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:12},N),R)),o.a.createElement(Fe.a,null,o.a.createElement(ue,{onClick:function(){w(),c()},color:"transparent",autoFocus:!0},i.cancel),o.a.createElement(ue,{onClick:function(){if(S.checked&&0!==S.checked.size)if(S.display){var e=[];m.forEach((function(a){S.checked.has(a)&&e.push(a)})),function(e,a,t,n,o){const l="/user_groups/"+e;var r={};a&&(r.display=a),t&&(r.role=t),Ga(l,r,()=>{n(e)},o)}(n,S.display,e,_,O)}else O("Display name required");else O("Select at least one role")},color:"info"},i.confirm)))}var Us={en:{title:"Remove Group",content:"Are you sure to remove group ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u7528\u6237\u7ec4",content:"\u662f\u5426\u5220\u9664\u7528\u6237\u7ec4 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Gs(e){var a,t=e.lang,n=e.name,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(""),s=Object(H.a)(i,2),u=s[0],m=s[1],d=Us[t],p=function(e){m(e)},f=function(e){m(""),r(e)};return a=u&&""!==u?o.a.createElement(Ve,{xs:12},o.a.createElement(Ke,{message:u,color:"danger"})):o.a.createElement(Ve,{xs:12}),o.a.createElement(Pe.a,{open:l,"aria-labelledby":d.title,maxWidth:"xs",fullWidth:!0},o.a.createElement(Me.a,null,d.title),o.a.createElement(Be.a,null,o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:12},d.content+n),a)),o.a.createElement(Fe.a,null,o.a.createElement(ue,{onClick:function(){m(""),c()},color:"transparent",autoFocus:!0},d.cancel),o.a.createElement(ue,{onClick:function(){!function(e,a,t){Va("/user_groups/"+e,()=>{a(e)},t)}(n,f,p)},color:"info"},d.confirm)))}var Vs=Object(d.a)({},jn),$s=Object(m.a)(Vs),Zs={en:{createButton:"Add New Group",name:"Group Name",display:"Display Name",modify:"Modify",remove:"Remove",member:"Members",operates:"Operates",noResource:"No Group Available"},cn:{createButton:"\u521b\u5efa\u65b0\u7528\u6237\u7ec4",name:"\u7528\u6237\u7ec4\u540d",display:"\u663e\u793a\u540d\u79f0",modify:"\u4fee\u6539",remove:"\u5220\u9664",member:"\u6210\u5458",operates:"\u64cd\u4f5c",noResource:"\u5c1a\u672a\u521b\u5efa\u7528\u6237\u7ec4"}};function Ys(e){var a=e.lang,t=e.setGroup,n=$s(),l=o.a.useState(null),r=Object(H.a)(l,2),c=r[0],i=r[1],s=o.a.useState(!1),u=Object(H.a)(s,2),m=u[0],d=u[1],p=o.a.useState(!1),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(!1),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(""),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState("warning"),O=Object(H.a)(j,2),w=O[0],_=O[1],N=o.a.useState(""),R=Object(H.a)(N,2),T=R[0],I=R[1],D=function(){I("")},A=o.a.useCallback((function(e){_("warning"),I(e),setTimeout(D,3e3)}),[_,I]),P=o.a.useCallback((function(){Wa("/user_groups/",i,(function(e){A(e),na()}))}),[A]),F=function(e){_("info"),I(e),Ma(e),setTimeout(D,3e3)},B=function(){y(!1)},M=function(){g(!1)},z=function(){d(!1)};o.a.useEffect((function(){P()}),[P]);var W=ta();if(null===W)return oa();var q,L=Zs[a];q=null===c?o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}}):0===c.length?o.a.createElement(gn,null,L.noResource):o.a.createElement(Pr,{color:"primary",headers:[L.name,L.display,L.member,L.operates],rows:c.map((function(e,a){var l=e.name,r=[o.a.createElement(_n,{key:"modify",label:L.modify,icon:Ns.a,onClick:function(){return e=l,y(!0),void S(e);var e}}),o.a.createElement(_n,{key:"member",label:L.member,icon:Es.a,onClick:function(){return t(l)}})];return l!==W.group&&r.push(o.a.createElement(_n,{key:"remove",label:L.remove,icon:Ht.a,onClick:function(){return e=l,g(!0),void S(e);var e}})),o.a.createElement(kn.a,{className:n.tableBodyRow,key:a},o.a.createElement(Sn.a,{className:n.tableCell},l),o.a.createElement(Sn.a,{className:n.tableCell},e.display),o.a.createElement(Sn.a,{className:n.tableCell},e.member.toString()),o.a.createElement(Sn.a,{className:n.tableCell},r))}))});var U=[o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){d(!0)}},o.a.createElement(Dt.a,null),L.createButton)];return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{display:"flex"},U.map((function(e,a){return o.a.createElement($.a,{key:a,m:1},e)})))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12},o.a.createElement(vi.a,{maxWidth:"sm"},q)),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:w,message:T,open:""!==T,closeNotification:D,close:!0})),o.a.createElement(Ve,null,o.a.createElement(qs,{lang:a,open:m,onSuccess:function(e){z(),F("new group "+e+" added"),P()},onCancel:z})),o.a.createElement(Ve,null,o.a.createElement(Ls,{lang:a,name:C,open:v,onSuccess:function(e){B(),F("group "+e+" modified"),P()},onCancel:B})),o.a.createElement(Ve,null,o.a.createElement(Gs,{lang:a,name:C,open:b,onSuccess:function(e){M(),F("group "+e+" removed"),P()},onCancel:M})))}var Js={en:{title:"Add Group Member ",name:"User",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6dfb\u52a0\u65b0\u6210\u5458 ",name:"\u7528\u6237\u540d",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Qs(e){var a,t,n,l={member:""},r=e.lang,c=e.group,i=e.open,s=e.onSuccess,u=e.onCancel,m=o.a.useState(!1),p=Object(H.a)(m,2),f=p[0],b=p[1],g=o.a.useState(""),h=Object(H.a)(g,2),E=h[0],v=h[1],y=o.a.useState(l),x=Object(H.a)(y,2),k=x[0],C=x[1],S=o.a.useState([]),j=Object(H.a)(S,2),O=j[0],w=j[1],_=Js[r],N=function(e){v(e)},R=function(){v(""),C(l),b(!1)},T=function(e){R(),s(e,c)};return o.a.useEffect((function(){if(i&&!f){!function(e,a,t){var n="/user_search/";e&&(n+="?group="+e),Wa(n,a,t)}(null,(function(e){0!==e.length?(w(e),b(!0)):N("no unallocated users available")}),N)}}),[f,i]),a=f?o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:8},o.a.createElement($.a,{m:1,p:2},o.a.createElement(ke.a,{htmlFor:"member"},_.name),o.a.createElement(ye.a,{value:k.member,onChange:(t="member",function(e){var a=e.target.value;C((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},t,a))}))}),inputProps:{name:"member",id:"member"},fullWidth:!0},O.map((function(e){return o.a.createElement(pe.a,{value:e,key:e},e)})))))):o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}}),n=E&&""!==E?o.a.createElement(Ve,{xs:12},o.a.createElement(Ke,{message:E,color:"danger"})):o.a.createElement(Ve,{xs:12}),o.a.createElement(Pe.a,{open:i,"aria-labelledby":_.title,maxWidth:"sm",fullWidth:!0},o.a.createElement(Me.a,null,_.title),o.a.createElement(Be.a,null,o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:12},a),n)),o.a.createElement(Fe.a,null,o.a.createElement(ue,{onClick:function(){R(),u()},color:"transparent",autoFocus:!0},_.cancel),o.a.createElement(ue,{onClick:function(){k.member&&""!==k.member||N("must select an user"),function(e,a,t,n){Ha("/user_groups/"+e+"/members/"+a,{},()=>{t(a,e)},n)}(c,k.member,T,N)},color:"info"},_.confirm)))}var Ks={en:{title:"Remove Group Member",content:"Are you sure to remove member ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u6210\u5458",content:"\u662f\u5426\u5220\u9664\u7528\u6237\u7ec4\u6210\u5458 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Xs(e){var a,t=e.lang,n=e.group,l=e.member,r=e.open,c=e.onSuccess,i=e.onCancel,s=o.a.useState(""),u=Object(H.a)(s,2),m=u[0],d=u[1],p=Ks[t],f=function(e){d(e)},b=function(){d(""),c(l,n)};return a=m&&""!==m?o.a.createElement(Ve,{xs:12},o.a.createElement(Ke,{message:m,color:"danger"})):o.a.createElement(Ve,{xs:12}),o.a.createElement(Pe.a,{open:r,"aria-labelledby":p.title,maxWidth:"xs",fullWidth:!0},o.a.createElement(Me.a,null,p.title),o.a.createElement(Be.a,null,o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:12},p.content+l),a)),o.a.createElement(Fe.a,null,o.a.createElement(ue,{onClick:function(){d(""),i()},color:"transparent",autoFocus:!0},p.cancel),o.a.createElement(ue,{onClick:function(){!function(e,a,t,n){Va("/user_groups/"+e+"/members/"+a,()=>{t(a,e)},n)}(n,l,b,f)},color:"info"},p.confirm)))}var eu=Object(d.a)({},jn),au=Object(m.a)(eu),tu={en:{addButton:"Add Group Member",backButton:"Back",remove:"Remove",member:"Member",operates:"Operates",noResource:"No Member Available"},cn:{addButton:"\u589e\u52a0\u65b0\u6210\u5458",backButton:"\u8fd4\u56de",remove:"\u5220\u9664",member:"\u6210\u5458",operates:"\u64cd\u4f5c",noResource:"\u5c1a\u672a\u6dfb\u52a0\u6210\u5458"}};function nu(e){var a=e.lang,t=e.group,n=e.onBack,l=au(),r=o.a.useState(null),c=Object(H.a)(r,2),i=c[0],s=c[1],u=o.a.useState(!1),m=Object(H.a)(u,2),d=m[0],p=m[1],f=o.a.useState(!1),b=Object(H.a)(f,2),g=b[0],h=b[1],E=o.a.useState(""),v=Object(H.a)(E,2),y=v[0],x=v[1],k=o.a.useState("warning"),C=Object(H.a)(k,2),S=C[0],j=C[1],O=o.a.useState(""),w=Object(H.a)(O,2),_=w[0],N=w[1],R=function(){N("")},T=o.a.useCallback((function(e){j("warning"),N(e),setTimeout(R,3e3)}),[j,N]),I=o.a.useCallback((function(){!function(e,a,t){Wa("/user_groups/"+e+"/members/",a,t)}(t,s,(function(e){T(e),na()}))}),[T,t]),D=function(e){j("info"),N(e),Ma(e),setTimeout(R,3e3)},A=function(){h(!1)},P=function(){p(!1)};if(o.a.useEffect((function(){I()}),[I]),null===ta())return oa();var F,B=tu[a];F=null===i?o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}}):0===i.length?o.a.createElement(gn,null,B.noResource):o.a.createElement(Pr,{color:"primary",headers:[B.member,B.operates],rows:i.map((function(e,a){var t=[o.a.createElement(_n,{key:"remove",label:B.remove,icon:Ht.a,onClick:function(){return a=e,h(!0),void x(a);var a}})];return o.a.createElement(kn.a,{className:l.tableBodyRow,key:a},o.a.createElement(Sn.a,{className:l.tableCell},e),o.a.createElement(Sn.a,{className:l.tableCell},t))}))});var M=[o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:n},o.a.createElement(ul.a,null),B.backButton),o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){p(!0)}},o.a.createElement(Dt.a,null),B.addButton)];return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{display:"flex"},M.map((function(e,a){return o.a.createElement($.a,{key:a,m:1},e)})))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12},o.a.createElement(vi.a,{maxWidth:"sm"},F)),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:S,message:_,open:""!==_,closeNotification:R,close:!0})),o.a.createElement(Ve,null,o.a.createElement(Qs,{lang:a,group:t,open:d,onSuccess:function(e){P(),D("member "+e+" added"),I()},onCancel:P})),o.a.createElement(Ve,null,o.a.createElement(Xs,{lang:a,group:t,member:y,open:g,onSuccess:function(e){A(),D("member "+e+" removed"),I()},onCancel:A})))}var ou={en:{title:"Add Role",name:"Name",menu:"Menus",dashboard:"Dashboard",computePool:"Compute Pools",addressPool:"Address Pools",storagePool:"Storage Pools",instance:"Instances",diskImage:"Disk Image",mediaImage:"Media Image",user:"Users",log:"Logs",visibility:"Resource Visibility",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u65b0\u589e\u89d2\u8272",name:"\u540d\u79f0",menu:"\u53ef\u7528\u83dc\u5355",dashboard:"\u4eea\u8868\u76d8",computePool:"\u8ba1\u7b97\u8d44\u6e90\u6c60",addressPool:"\u5730\u5740\u8d44\u6e90\u6c60",storagePool:"\u5b58\u50a8\u8d44\u6e90\u6c60",instance:"\u4e91\u4e3b\u673a\u5b9e\u4f8b",diskImage:"\u78c1\u76d8\u955c\u50cf",mediaImage:"\u5149\u76d8\u955c\u50cf",user:"\u7528\u6237",log:"\u65e5\u5fd7",visibility:"\u8d44\u6e90\u53ef\u89c1\u6027",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function lu(e){var a,t,n={name:"",checked:new Set},l=e.lang,r=e.open,c=e.onSuccess,i=e.onCancel,s=ou[l],u=ma(l),m=o.a.useState(""),p=Object(H.a)(m,2),f=p[0],b=p[1],g=o.a.useState(n),h=Object(H.a)(g,2),E=h[0],v=h[1],y=function(e){b(e)},x=function(){b(""),v(n)},k=function(e){x(),c(e)},C=function(e){var a=e.target.value,t=e.target.checked,n=Object(d.a)({},E);t?n.checked.add(a):n.checked.delete(a),v(n)},S=o.a.createElement(Ne.a,{container:!0},o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:6,md:4},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:s.name,onChange:(a="name",function(e){var t=e.target.value;v((function(e){return Object(d.a)(Object(d.a)({},e),{},Object(le.a)({},a,t))}))}),value:E.name,margin:"normal",required:!0,fullWidth:!0})))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(we.a,{component:"fieldset",fullWidth:!0},o.a.createElement(_e.a,{component:"legend"},s.menu),o.a.createElement(Re.a,null,o.a.createElement(Ne.a,{container:!0},u.map((function(e,a){var t,n=e.value,l=e.label;return t=!!E.checked.has(n),o.a.createElement(Ve,{xs:6,sm:3,key:a},o.a.createElement(Oe.a,{control:o.a.createElement(Te.a,{checked:t,onChange:C,value:n}),label:l}))})))))))));return t=f&&""!==f?o.a.createElement(Ve,{xs:12},o.a.createElement(Ke,{message:f,color:"danger"})):o.a.createElement(Ve,{xs:12}),o.a.createElement(Pe.a,{open:r,"aria-labelledby":s.title,maxWidth:"md",fullWidth:!0},o.a.createElement(Me.a,null,s.title),o.a.createElement(Be.a,null,o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:12},S),t)),o.a.createElement(Fe.a,null,o.a.createElement(ue,{onClick:function(){x(),i()},color:"transparent",autoFocus:!0},s.cancel),o.a.createElement(ue,{onClick:function(){if(E.name)if(E.checked&&0!==E.checked.size){var e=[];u.forEach((function(a){E.checked.has(a.value)&&e.push(a.value)})),function(e,a,t,n){Ha("/roles/"+e,{menu:a},()=>{t(e)},n)}(E.name,e,k,y)}else y("Select at least one menu item");else y("must specify role name")},color:"info"},s.confirm)))}var ru={en:{title:"Modify Role",name:"Name",menu:"Menus",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u89d2\u8272\u6743\u9650",name:"\u540d\u79f0",menu:"\u53ef\u7528\u83dc\u5355",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function cu(e){var a={checked:new Set},t=e.lang,n=e.name,l=e.open,r=e.onSuccess,c=e.onCancel,i=ru[t],s=ma(t),u=o.a.useState(!1),m=Object(H.a)(u,2),p=m[0],f=m[1],b=o.a.useState(""),g=Object(H.a)(b,2),h=g[0],E=g[1],v=o.a.useState(a),y=Object(H.a)(v,2),x=y[0],k=y[1],C=function(e){E(e)},S=function(){E(""),k(a),f(!1)},j=function(){S(),r(n)};o.a.useEffect((function(){if(n&&l&&!p){!function(e,a,t){Wa("/roles/"+e,a,t)}(n,(function(e){var a=e.menu,t=new Set;a.forEach((function(e){t.add(e)})),k({checked:t}),f(!0)}),C)}}),[p,l,n]);var O,w,_=function(e){var a=e.target.value,t=e.target.checked,n=Object(d.a)({},x);t?n.checked.add(a):n.checked.delete(a),k(n)};return O=p?o.a.createElement(Ne.a,{container:!0},o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12,sm:6,md:4},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:i.name,value:n,margin:"normal",disabled:!0,required:!0,fullWidth:!0})))),o.a.createElement(kc,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(we.a,{component:"fieldset",fullWidth:!0},o.a.createElement(_e.a,{component:"legend"},i.menu),o.a.createElement(Re.a,null,o.a.createElement(Ne.a,{container:!0},s.map((function(e,a){var t,n=e.value,l=e.label;return t=!!x.checked.has(n),o.a.createElement(Ve,{xs:6,sm:3,key:a},o.a.createElement(Oe.a,{control:o.a.createElement(Te.a,{checked:t,onChange:_,value:n}),label:l}))}))))))))):o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}}),w=h&&""!==h?o.a.createElement(Ve,{xs:12},o.a.createElement(Ke,{message:h,color:"danger"})):o.a.createElement(Ve,{xs:12}),o.a.createElement(Pe.a,{open:l,"aria-labelledby":i.title,maxWidth:"md",fullWidth:!0},o.a.createElement(Me.a,null,i.title),o.a.createElement(Be.a,null,o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:12},O),w)),o.a.createElement(Fe.a,null,o.a.createElement(ue,{onClick:function(){S(),c()},color:"transparent",autoFocus:!0},i.cancel),o.a.createElement(ue,{onClick:function(){if(x.checked&&0!==x.checked.size){var e=[];s.forEach((function(a){x.checked.has(a.value)&&e.push(a.value)})),function(e,a,t,n){Ga("/roles/"+e,{menu:a},()=>{t(e)},n)}(n,e,j,C)}else C("Select at least one menu item")},color:"info"},i.confirm)))}var iu={en:{title:"Remove Role",content:"Are you sure to remove role ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u89d2\u8272",content:"\u662f\u5426\u5220\u9664\u89d2\u8272 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function su(e){var a,t=e.lang,n=e.name,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(""),s=Object(H.a)(i,2),u=s[0],m=s[1],d=iu[t],p=function(e){m(e)},f=function(e){m(""),r(e)};return a=u&&""!==u?o.a.createElement(Ve,{xs:12},o.a.createElement(Ke,{message:u,color:"danger"})):o.a.createElement(Ve,{xs:12}),o.a.createElement(Pe.a,{open:l,"aria-labelledby":d.title,maxWidth:"xs",fullWidth:!0},o.a.createElement(Me.a,null,d.title),o.a.createElement(Be.a,null,o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:12},d.content+n),a)),o.a.createElement(Fe.a,null,o.a.createElement(ue,{onClick:function(){m(""),c()},color:"transparent",autoFocus:!0},d.cancel),o.a.createElement(ue,{onClick:function(){!function(e,a,t){Va("/roles/"+e,()=>{a(e)},t)}(n,f,p)},color:"info"},d.confirm)))}var uu=Object(d.a)({},jn),mu=Object(m.a)(uu),du={en:{createButton:"Add New Role",name:"Role Name",modify:"Modify",delete:"Remove",operates:"Operates",noResource:"No Role Available"},cn:{createButton:"\u589e\u52a0\u65b0\u89d2\u8272",name:"\u89d2\u8272\u540d",modify:"\u4fee\u6539",delete:"\u5220\u9664",operates:"\u64cd\u4f5c",noResource:"\u5c1a\u672a\u521b\u5efa\u89d2\u8272"}};function pu(e){var a=mu(),t=o.a.useState(null),n=Object(H.a)(t,2),l=n[0],r=n[1],c=o.a.useState(!1),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(!1),d=Object(H.a)(m,2),p=d[0],f=d[1],b=o.a.useState(!1),g=Object(H.a)(b,2),h=g[0],E=g[1],v=o.a.useState(""),y=Object(H.a)(v,2),x=y[0],k=y[1],C=o.a.useState("warning"),S=Object(H.a)(C,2),j=S[0],O=S[1],w=o.a.useState(""),_=Object(H.a)(w,2),N=_[0],R=_[1],T=function(){R("")},I=o.a.useCallback((function(e){O("warning"),R(e),setTimeout(T,3e3)}),[O,R]),D=o.a.useCallback((function(){Ba(r,(function(e){I(e),na()}))}),[I]),A=function(e){O("info"),R(e),Ma(e),setTimeout(T,3e3)},P=function(){E(!1)},F=function(){f(!1)},B=function(){u(!1)};if(o.a.useEffect((function(){D()}),[D]),null===ta())return oa();var M,z=e.lang,W=du[z];M=null===l?o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}}):0===l.length?o.a.createElement(gn,null,W.noResource):o.a.createElement(Pr,{color:"primary",headers:[W.name,W.operates],rows:l.map((function(e,t){var n=e,l=[o.a.createElement(_n,{key:"modify",label:W.modify,icon:Ns.a,onClick:function(){return function(e){E(!0),k(e)}(n)}}),o.a.createElement(_n,{key:"remove",label:W.delete,icon:Ht.a,onClick:function(){return function(e){f(!0),k(e)}(n)}})];return o.a.createElement(kn.a,{className:a.tableBodyRow,key:t},o.a.createElement(Sn.a,{className:a.tableCell},e),o.a.createElement(Sn.a,{className:a.tableCell},l))}))});var q=[o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){u(!0)}},o.a.createElement(Dt.a,null),W.createButton)];return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{display:"flex"},q.map((function(e,a){return o.a.createElement($.a,{key:a,m:1},e)})))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12},o.a.createElement(vi.a,{maxWidth:"sm"},M)),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:j,message:N,open:""!==N,closeNotification:T,close:!0})),o.a.createElement(Ve,null,o.a.createElement(lu,{lang:z,open:s,onSuccess:function(e){B(),A("role "+e+" added"),D()},onCancel:B})),o.a.createElement(Ve,null,o.a.createElement(cu,{lang:z,name:x,open:h,onSuccess:function(e){P(),A("role "+e+" modified"),D()},onCancel:P})),o.a.createElement(Ve,null,o.a.createElement(su,{lang:z,name:x,open:p,onSuccess:function(e){F(),A("role "+e+" removed"),D()},onCancel:F})))}var fu={en:{title:"Permissions",user:"Users",group:"User Groups",role:"Roles"},cn:{title:"\u6743\u9650\u7ba1\u7406",user:"\u7528\u6237",group:"\u7528\u6237\u7ec4",role:"\u89d2\u8272"}},bu=function(e){var a=e.lang,t=o.a.useState(""),n=Object(H.a)(t,2),l=n[0],r=n[1];return l?o.a.createElement(nu,{lang:a,group:l,onBack:function(){return r("")}}):o.a.createElement(Ys,{lang:a,setGroup:r})};var gu=Object(d.a)({},jn),hu=Object(m.a)(gu),Eu={en:{modify:"Modify",visibility:"Group Resource Visibility",description:"Description",instance:"Instances Visible",instanceDescription:"User can browse instances created by other users in the same group when enabled. Otherwise, an instance is only visible to its creator by default.",disk:"Disk Images Visible",diskDescription:"User can browse disk images created by other users in the same group when enabled. Otherwise, an image is only visible to its creator by default.",media:"Media Images Visible",mediaDescription:"User can browse media images created by other users in the same group when enabled. Otherwise, an image is only visible to its creator by default."},cn:{modify:"\u4fee\u6539",visibility:"\u7ec4\u8d44\u6e90\u53ef\u89c1\u6027",description:"\u63cf\u8ff0",instance:"\u4e91\u4e3b\u673a\u5b9e\u4f8b\u7ec4\u5185\u53ef\u89c1",instanceDescription:"\u52fe\u9009\u540e\uff0c\u7528\u6237\u53ef\u4ee5\u67e5\u770b\u540c\u4e00\u7ec4\u5185\u5176\u4ed6\u7528\u6237\u521b\u5efa\u7684\u4e91\u4e3b\u673a\u5b9e\u4f8b(\u9ed8\u8ba4\u4ec5\u5bf9\u521b\u5efa\u8005\u53ef\u89c1)",disk:"\u78c1\u76d8\u955c\u50cf\u7ec4\u5185\u53ef\u89c1",diskDescription:"\u52fe\u9009\u540e\uff0c\u7528\u6237\u53ef\u4ee5\u67e5\u770b\u540c\u4e00\u7ec4\u5185\u5176\u4ed6\u7528\u6237\u521b\u5efa\u7684\u78c1\u76d8\u955c\u50cf(\u9ed8\u8ba4\u4ec5\u5bf9\u521b\u5efa\u8005\u53ef\u89c1)",media:"\u5149\u76d8\u955c\u50cf\u7ec4\u5185\u53ef\u89c1",mediaDescription:"\u52fe\u9009\u540e\uff0c\u7528\u6237\u53ef\u4ee5\u67e5\u770b\u540c\u4e00\u7ec4\u5185\u5176\u4ed6\u7528\u6237\u521b\u5efa\u7684\u5149\u76d8\u955c\u50cf(\u9ed8\u8ba4\u4ec5\u5bf9\u521b\u5efa\u8005\u53ef\u89c1)"}},vu=function(e){var a=e.checked,t=e.onChange,n=e.label,l=e.description,r=e.classes;return o.a.createElement(kn.a,{className:r.tableBodyRow},o.a.createElement(Sn.a,{className:r.tableCell},o.a.createElement($.a,{display:"flex",alignItems:"center"},o.a.createElement($.a,null,o.a.createElement(Te.a,{checked:a,onChange:t})),o.a.createElement($.a,null,n))),o.a.createElement(Sn.a,{className:r.tableCell},l))};var yu=t(302),xu=t.n(yu),ku={en:{title:"Delete Security Policy",content:"Are you sure to delete security policy ",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u5b89\u5168\u7b56\u7565",content:"\u662f\u5426\u5220\u9664\u5b89\u5168\u7b56\u7565 ",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Cu(e){var a=e.lang,t=e.policyID,n=e.open,l=e.onSuccess,r=e.onCancel,c=o.a.useState(!0),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(""),d=Object(H.a)(m,2),p=d[0],f=d[1],b=ku[a],g=b.title,h=function(e){u(!0),f(e)},E=function(){u(!0),f(""),l(t)},v=b.content+t,y=[{color:"transparent",label:b.cancel,onClick:function(){f(""),r()}},{color:"info",label:b.confirm,onClick:function(){u(!1),function(e,a,t){Va("/security_policy_groups/"+e,()=>{a(e)},t)}(t,E,h)}}];return o.a.createElement(Xe,{size:"xs",open:n,prompt:p,title:g,buttons:y,content:v,operatable:s})}var Su={en:{title:"Create New Security Policy",name:"Name",description:"Description",enable:"Enable",enabled:"Enabled",disabled:"Disabled",global:"Global",yes:"Yes",no:"No",defaultAction:"Default Action",accept:"Accept",reject:"Reject",format:"only letter/digit/'_' allowed",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u521b\u5efa\u65b0\u5b89\u5168\u7b56\u7565",name:"\u540d\u79f0",description:"\u63cf\u8ff0",enable:"\u662f\u5426\u542f\u7528",enabled:"\u5df2\u542f\u7528",disabled:"\u5df2\u7981\u7528",global:"\u5168\u5c40\u53ef\u89c1",yes:"\u662f",no:"\u5426",defaultAction:"\u9ed8\u8ba4\u5904\u7406",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",format:"\u4ec5\u5141\u8bb8\u5b57\u6bcd\u6570\u5b57\u4e0e\u4e0b\u5212\u7ebf_",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function ju(e){var a={name:"",description:"",action:"accept",enabled:!0,global:!1},t=e.lang,n=e.open,l=e.onSuccess,r=e.onCancel,c=o.a.useState(!0),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(""),p=Object(H.a)(m,2),f=p[0],b=p[1],g=o.a.useState(a),h=Object(H.a)(g,2),E=h[0],v=h[1],y=Su[t],x=y.title,k=[{label:y.accept,value:"accept"},{label:y.reject,value:"reject"}],C=function(e){u(!0),b(e)},S=function(){b(""),v(a)},j=function(e){u(!0),S(),l(e)},O=function(e){return function(a){var t=a.target.value;v((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}},w=function(e){return function(a){var t=a.target.checked;v((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}},_=[{type:"text",label:y.name,onChange:O("name"),value:E.name,required:!0,oneRow:!0,xs:8,sm:6,md:4},{type:"radio",label:y.defaultAction,onChange:O("action"),value:E.action,options:k,disabled:!0,oneRow:!0,xs:10,sm:8,md:6},{type:"switch",label:y.enable,onChange:w("enabled"),value:E.enabled,on:y.enabled,off:y.disabled,oneRow:!0,xs:6},{type:"switch",label:y.global,onChange:w("global"),value:E.global,on:y.yes,off:y.no,oneRow:!0,xs:6},{type:"textarea",label:y.description,onChange:O("description"),value:E.description,oneRow:!0,rows:3,xs:10}],N=o.a.createElement(Ae,{inputs:_}),R=[{color:"transparent",label:y.cancel,onClick:function(){S(),r()}},{color:"info",label:y.confirm,onClick:function(){if(u(!1),E.action){var e=new RegExp("[^\\w]");E.name?e.test(E.name)?C(y.format):function(e,a,t,n,o,l,r){var c=ta();if(null===c)return void r("session expired");Ha("/security_policy_groups/",{name:e,description:a,user:c.user,group:c.group,enabled:t,global:n,default_action:o},e=>{let{id:a}=e;l(a)},r)}(E.name,E.description,E.enabled,E.global,E.action,j,C):C("must specify policy name")}else C("must specify action")}}];return o.a.createElement(Xe,{size:"sm",open:n,prompt:f,title:x,buttons:R,content:N,operatable:s})}var Ou={en:{title:"Modify Security Policy",name:"Name",description:"Description",enable:"Enable",enabled:"Enabled",disabled:"Disabled",global:"Global",yes:"Yes",no:"No",defaultAction:"Default Action",accept:"Accept",reject:"Reject",format:"only letter/digit/'_' allowed",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u5b89\u5168\u7b56\u7565",name:"\u540d\u79f0",description:"\u63cf\u8ff0",enable:"\u662f\u5426\u542f\u7528",enabled:"\u5df2\u542f\u7528",disabled:"\u5df2\u7981\u7528",global:"\u5168\u5c40\u53ef\u89c1",yes:"\u662f",no:"\u5426",defaultAction:"\u9ed8\u8ba4\u5904\u7406",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",format:"\u4ec5\u5141\u8bb8\u5b57\u6bcd\u6570\u5b57\u4e0e\u4e0b\u5212\u7ebf_",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function wu(e){var a={name:"",description:"",action:"accept",enabled:!0,global:!1},t=e.lang,n=e.policyID,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!1),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(!0),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(""),E=Object(H.a)(h,2),v=E[0],y=E[1],x=o.a.useState(!1),k=Object(H.a)(x,2),C=k[0],S=k[1],j=o.a.useState(a),O=Object(H.a)(j,2),w=O[0],_=O[1],N=Ou[t],R=N.title,T=[{label:N.accept,value:"accept"},{label:N.reject,value:"reject"}],I=o.a.useCallback((function(e){C&&(g(!0),y(e))}),[C]),D=function(){y(""),_(a),m(!1)},A=function(){g(!0),D(),r(n)},P=function(e){return function(a){var t=a.target.value;_((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}},F=function(e){return function(a){var t=a.target.checked;_((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}};o.a.useEffect((function(){if(n&&l){S(!0);return function(e,a,t){Wa("/security_policy_groups/"+e,a,t)}(n,(function(e){C&&(_({name:e.name,description:e.description,action:e.default_action,enabled:e.enabled,global:e.global}),m(!0))}),I),function(){S(!1)}}}),[C,l,n,I]);var B,M=[{color:"transparent",label:N.cancel,onClick:function(){D(),c()}}];if(u){var z=[{type:"text",label:N.name,onChange:P("name"),value:w.name,required:!0,oneRow:!0,xs:8,sm:6,md:4},{type:"radio",label:N.defaultAction,onChange:P("action"),value:w.action,options:T,disabled:!0,oneRow:!0,xs:10,sm:8,md:6},{type:"switch",label:N.enable,onChange:F("enabled"),value:w.enabled,on:N.enabled,off:N.disabled,oneRow:!0,xs:6},{type:"switch",label:N.global,onChange:F("global"),value:w.global,on:N.yes,off:N.no,oneRow:!0,xs:6},{type:"textarea",label:N.description,onChange:P("description"),value:w.description,oneRow:!0,rows:3,xs:10}];B=o.a.createElement(Ae,{inputs:z}),M.push({color:"info",label:N.confirm,onClick:function(){if(g(!1),w.action){var e=new RegExp("[^\\w]");w.name?e.test(w.name)?I(N.format):function(e,a,t,n,o,l,r,c){const i="/security_policy_groups/"+e;var s=ta();if(null===s)return void c("session expired");Ga(i,{name:a,description:t,user:s.user,group:s.group,enabled:n,global:o,default_action:l},()=>{r(e)},c)}(n,w.name,w.description,w.enabled,w.global,w.action,A,I):I("must specify policy name")}else I("must specify action")}})}else B=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:l,prompt:v,title:R,buttons:M,content:B,operatable:b})}var _u=Object(m.a)({cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),Nu={en:{createButton:"Create New Group",tableTitle:"Security Policy Groups",name:"Name",enable:"Enable",enabled:"Enabled",disabled:"Disabled",global:"Global",yes:"Yes",no:"No",defaultAction:"Default Action",accept:"Accept",reject:"Reject",operates:"Operates",noResource:"No security policy available",modify:"Modify",delete:"Delete",rules:"Rules"},cn:{createButton:"\u521b\u5efa\u65b0\u7b56\u7565\u7ec4",tableTitle:"\u5b89\u5168\u7b56\u7565\u7ec4",name:"\u540d\u79f0",enable:"\u662f\u5426\u542f\u7528",enabled:"\u5df2\u542f\u7528",disabled:"\u5df2\u7981\u7528",global:"\u5168\u5c40\u53ef\u89c1",yes:"\u662f",no:"\u5426",defaultAction:"\u9ed8\u8ba4\u5904\u7406",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u5b89\u5168\u7b56\u7565\u7ec4",modify:"\u4fee\u6539",delete:"\u5220\u9664",rules:"\u89c4\u5219"}};function Ru(e){var a,t=e.lang,n=Nu[t],l=_u(),r=o.a.useState(!1),c=Object(H.a)(r,2),i=c[0],s=c[1],u=o.a.useState(null),m=Object(H.a)(u,2),d=m[0],p=m[1],f=o.a.useState(!1),b=Object(H.a)(f,2),g=b[0],h=b[1],E=o.a.useState(!1),v=Object(H.a)(E,2),y=v[0],x=v[1],k=o.a.useState(!1),C=Object(H.a)(k,2),S=C[0],j=C[1],O=o.a.useState(""),w=Object(H.a)(O,2),_=w[0],N=w[1],R=o.a.useState("warning"),T=Object(H.a)(R,2),I=T[0],D=T[1],A=o.a.useState(""),P=Object(H.a)(A,2),F=P[0],B=P[1],M=function(){B("")},z=o.a.useCallback((function(e){if(i){D("warning"),B(e),setTimeout(M,3e3)}}),[D,B,i]),W=o.a.useCallback((function(){if(i){var e=ta();null!==e?Fa(e.user,e.group,!1,!1,p,z):z("session expired")}}),[z,i]),q=function(e){if(i){D("info"),B(e),Ma(e),setTimeout(M,3e3)}},L=function(){x(!1)},U=function(){j(!1)},G=function(){h(!1)};if(o.a.useEffect((function(){return s(!0),W(),function(){s(!1)}}),[W]),null===d)a=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});else if(0===d.length)a=o.a.createElement($.a,{display:"flex",justifyContent:"center"},o.a.createElement(gn,null,n.noResource));else{var V=[];d.forEach((function(e){var a=[{onClick:function(a){return t=e.id,x(!0),void N(t);var t},icon:Qo.a,label:n.modify},{onClick:function(a){return t=e.id,j(!0),void N(t);var t},icon:Ht.a,label:n.delete},{href:"/admin/security_policies/"+e.id+"/rules/",icon:xu.a,label:n.rules}];V.push(function(e,a){var t=a.map((function(e,a){return o.a.createElement(_n,{label:e.label,icon:e.icon,onClick:e.onClick,href:e.href,key:a})})),l=e.name,r=e.default_action,c=e.enabled,i=e.global;return[l,"accept"===r?n.accept:n.reject,c?n.enabled:n.disabled,i?n.yes:n.no,t]}(e,a))})),a=o.a.createElement(wn,{color:"primary",headers:[n.name,n.defaultAction,n.enable,n.global,n.operates],rows:V})}var Z=[{onClick:function(){h(!0)},icon:Dt.a,label:n.createButton}];return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12},o.a.createElement(an,null,o.a.createElement(Ve,{xs:12,sm:6,md:4},o.a.createElement($.a,{display:"flex"},Z.map((function(e,a){return e.href?o.a.createElement($.a,{p:1,key:a},o.a.createElement(ue,{size:"sm",color:"info",round:!0,href:e.href},o.a.createElement(e.icon),e.label)):o.a.createElement($.a,{p:1,key:a},o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:e.onClick},o.a.createElement(e.icon),e.label))})))))),o.a.createElement(Ve,{xs:12},o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",{className:l.cardTitleWhite},n.tableTitle)),o.a.createElement(fn,null,a))),o.a.createElement(vn,{place:"tr",color:I,message:F,open:""!==F,closeNotification:M,close:!0}),o.a.createElement(ju,{lang:t,open:g,onSuccess:function(e){G(),q("new policy "+e+" created"),W()},onCancel:G}),o.a.createElement(wu,{lang:t,open:y,policyID:_,onSuccess:function(e){L(),q("policy "+e+" modified"),W()},onCancel:L}),o.a.createElement(Cu,{lang:t,open:S,policyID:_,onSuccess:function(e){U(),q("policy "+e+" deleted"),W()},onCancel:U}))}var Tu={en:{title:"Remove Security Policy Rule",content:"Are you sure to remove ",content2:"th rule",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u5220\u9664\u5b89\u5168\u89c4\u5219",content:"\u662f\u5426\u5220\u9664\u7b2c ",content2:"\u4e2a\u5b89\u5168\u89c4\u5219",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Iu(e){var a=e.lang,t=e.open,n=e.policyID,l=e.index,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!0),s=Object(H.a)(i,2),u=s[0],m=s[1],d=o.a.useState(""),p=Object(H.a)(d,2),f=p[0],b=p[1],g=Tu[a],h=g.title,E=function(e){m(!0),b(e)},v=function(){m(!0),b(""),r(n,l)},y=g.content+(l+1)+g.content2,x=[{color:"transparent",label:g.cancel,onClick:function(){b(""),c()}},{color:"info",label:g.confirm,onClick:function(){m(!1),function(e,a,t,n){Va("/security_policy_groups/"+e+"/rules/"+a,()=>{t(e,a)},n)}(n,l,v,E)}}];return o.a.createElement(Xe,{size:"xs",open:t,prompt:f,title:h,buttons:x,content:y,operatable:u})}var Du={en:{title:"Add Security Policy Rule",action:"Action",accept:"Accept",reject:"Reject",protocol:"Protocol",sourceAddress:"Source Address",targetPort:"Target Port",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6dfb\u52a0\u5b89\u5168\u89c4\u5219",action:"\u5904\u7406",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",protocol:"\u534f\u8bae",sourceAddress:"\u6765\u6e90\u5730\u5740",targetPort:"\u76ee\u6807\u7aef\u53e3",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Au(e){var a={action:"accept",protocol:"",port:""},t=e.lang,n=e.policyID,l=e.open,r=e.onSuccess,c=e.onCancel,i=o.a.useState(!0),s=Object(H.a)(i,2),u=s[0],m=s[1],p=o.a.useState(""),f=Object(H.a)(p,2),b=f[0],g=f[1],h=o.a.useState(a),E=Object(H.a)(h,2),v=E[0],y=E[1],x=Du[t],k=x.title,C=[{label:x.accept,value:"accept"},{label:x.reject,value:"reject"}],S=function(e){m(!0),g(e)},j=function(){g(""),y(a)},O=function(e){m(!0),j(),r(e)},w=function(e){return function(a){var t=a.target.value;y((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}},_=[{type:"radio",label:x.action,onChange:w("action"),value:v.action,options:C,required:!0,oneRow:!0,xs:10,sm:8,md:6},{type:"select",label:x.protocol,onChange:w("protocol"),value:v.protocol,options:[{label:"TCP",value:"tcp"},{label:"UDP",value:"udp"}],required:!0,oneRow:!0,xs:8,sm:6,md:4},{type:"text",label:x.targetPort,onChange:w("port"),value:v.port,required:!0,oneRow:!0,xs:8,sm:6,md:4}],N=o.a.createElement(Ae,{inputs:_}),R=[{color:"transparent",label:x.cancel,onClick:function(){j(),c()}},{color:"info",label:x.confirm,onClick:function(){if(m(!1),v.action)if(v.protocol)if(v.port){var e=Number.parseInt(v.port);Number.isNaN(e)?S("invalid target port: "+v.port):function(e,a,t,n,o,l){Ha("/security_policy_groups/"+e+"/rules/",{action:a,protocol:t,to_port:n},o,l)}(n,v.action,v.protocol,e,O,S)}else S("must specify target port");else S("must specify protocol");else S("must specify action")}}];return o.a.createElement(Xe,{size:"sm",open:l,prompt:b,title:k,buttons:R,content:N,operatable:u})}var Pu={en:{title:"Modify Security Policy Rule",action:"Action",accept:"Accept",reject:"Reject",protocol:"Protocol",sourceModifyress:"Source Address",targetPort:"Target Port",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u4fee\u6539\u5b89\u5168\u89c4\u5219",action:"\u5904\u7406",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",protocol:"\u534f\u8bae",sourceModifyress:"\u6765\u6e90\u5730\u5740",targetPort:"\u76ee\u6807\u7aef\u53e3",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Fu(e){var a=e.lang,t=e.policyID,n=e.rule,l=e.open,r=e.onSuccess,c=e.onCancel,i={action:n.action,protocol:n.protocol,port:n.to_port},s=o.a.useState(!0),u=Object(H.a)(s,2),m=u[0],p=u[1],f=o.a.useState(""),b=Object(H.a)(f,2),g=b[0],h=b[1],E=o.a.useState(!1),v=Object(H.a)(E,2),y=v[0],x=v[1],k=o.a.useState(!1),C=Object(H.a)(k,2),S=C[0],j=C[1],O=o.a.useState(i),w=Object(H.a)(O,2),_=w[0],N=w[1],R=Pu[a],T=R.title,I=[{label:R.accept,value:"accept"},{label:R.reject,value:"reject"}],D=o.a.useCallback((function(e){y&&(p(!0),h(e))}),[y]),A=function(){h(""),N(i),j(!1)},P=function(e){p(!0),A(),r(e)},F=function(e){return function(a){var t=a.target.value;N((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}};o.a.useEffect((function(){if(t&&n)return x(!0),N({action:n.action,protocol:n.protocol,port:n.to_port}),j(!0),function(){x(!1)}}),[y,l,t,n,D]);var B,M=[{color:"transparent",label:R.cancel,onClick:function(){A(),c()}}];if(S){var z=[{type:"radio",label:R.action,onChange:F("action"),value:_.action,options:I,required:!0,oneRow:!0,xs:10,sm:8,md:6},{type:"select",label:R.protocol,onChange:F("protocol"),value:_.protocol,options:[{label:"TCP",value:"tcp"},{label:"UDP",value:"udp"}],required:!0,oneRow:!0,xs:8,sm:6,md:4},{type:"text",label:R.targetPort,onChange:F("port"),value:_.port,required:!0,oneRow:!0,xs:8,sm:6,md:4}];B=o.a.createElement(Ae,{inputs:z}),M.push({color:"info",label:R.confirm,onClick:function(){if(p(!1),_.action)if(_.protocol)if(_.port){var e=Number.parseInt(_.port);Number.isNaN(e)?D("invalid target port: "+_.port):function(e,a,t,n,o,l,r){Ga("/security_policy_groups/"+e+"/rules/"+a,{action:t,protocol:n,to_port:o},()=>{l(e,a)},r)}(t,n.index,_.action,_.protocol,e,P,D)}else D("must specify target port");else D("must specify protocol");else D("must specify action")}})}else B=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement(Xe,{size:"sm",open:l,prompt:g,title:T,buttons:M,content:B,operatable:m})}var Bu=Object(d.a)(Object(d.a)({},z),{},{cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),Mu=Object(m.a)(Bu),zu={en:{createButton:"Add New Rule",tableTitle:"Security Policy Rules",rule:"Rule",action:"Action",protocol:"Protocol",sourceAddress:"Source Address",targetPort:"Target Port",accept:"Accept",reject:"Reject",operates:"Operates",noResource:"No security policy available",modify:"Modify",remove:"Remove",moveUp:"Move Up",moveDown:"Move Down",back:"Back"},cn:{createButton:"\u6dfb\u52a0\u65b0\u89c4\u5219",tableTitle:"\u5b89\u5168\u89c4\u5219",rule:"\u89c4\u5219",action:"\u5904\u7406",protocol:"\u534f\u8bae",sourceAddress:"\u6765\u6e90\u5730\u5740",targetPort:"\u76ee\u6807\u7aef\u53e3",accept:"\u63a5\u53d7",reject:"\u62d2\u7edd",operates:"\u64cd\u4f5c",noResource:"\u6ca1\u6709\u5b89\u5168\u7b56\u7565\u7ec4",modify:"\u4fee\u6539",remove:"\u79fb\u9664",moveUp:"\u4e0a\u79fb",moveDown:"\u4e0b\u79fb",back:"\u8fd4\u56de"}};function Wu(e){var a,t=e.match.params.id,n=e.lang,l=zu[n],r=Mu(),c=o.a.useState(!1),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(null),d=Object(H.a)(m,2),p=d[0],f=d[1],b=o.a.useState(!1),g=Object(H.a)(b,2),h=g[0],E=g[1],v=o.a.useState(!1),y=Object(H.a)(v,2),x=y[0],k=y[1],C=o.a.useState(!1),S=Object(H.a)(C,2),j=S[0],O=S[1],w=o.a.useState(""),_=Object(H.a)(w,2),N=_[0],R=_[1],T=o.a.useState("warning"),I=Object(H.a)(T,2),D=I[0],A=I[1],P=o.a.useState(""),F=Object(H.a)(P,2),B=F[0],M=F[1],z=function(){M("")},W=o.a.useCallback((function(e){if(s){A("warning"),M(e),setTimeout(z,3e3)}}),[A,M,s]),q=o.a.useCallback((function(){s&&function(e,a,t){Wa("/security_policy_groups/"+e+"/rules/",a,t)}(t,f,W)}),[t,W,s]),L=function(e){if(s){A("info"),M(e),Ma(e),setTimeout(z,3e3)}},U=function(){k(!1)},G=function(){O(!1)},V=function(){E(!1)},Z=function(e){!function(e,a,t,n){Ga("/security_policy_groups/"+e+"/rules/"+a+"/order",{direction:"up"},()=>{t(e,a)},n)}(t,e.index,(function(e,a){L(a+"th rule moved up"),q()}),W)},Y=function(e){!function(e,a,t,n){Ga("/security_policy_groups/"+e+"/rules/"+a+"/order",{direction:"down"},()=>{t(e,a)},n)}(t,e.index,(function(e,a){L(a+"th rule moved down"),q()}),W)};if(o.a.useEffect((function(){return u(!0),q(),function(){u(!1)}}),[q]),null===p)a=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});else if(0===p.length)a=o.a.createElement($.a,{display:"flex",justifyContent:"center"},o.a.createElement(gn,null,l.noResource));else{var J=[];p.forEach((function(e,a){var t={index:a,action:e.action,protocol:e.protocol,to_port:e.to_port},n=[{onClick:function(e){return function(e){k(!0),R(e)}(t)},icon:Qo.a,label:l.modify},{onClick:function(e){return function(e){O(!0),R(e)}(t)},icon:Ht.a,label:l.remove}];p.length-1!==a&&n.push({onClick:function(e){return Y(t)},icon:os.a,label:l.moveDown}),0!==a&&n.push({onClick:function(e){return Z(t)},icon:rs.a,label:l.moveUp}),J.push(function(e,a,t){var n=t.map((function(e,a){return o.a.createElement(_n,{label:e.label,icon:e.icon,onClick:e.onClick,key:a})})),c=a.action,i=a.protocol,s=a.from_address,u=a.to_port;return[e,"accept"===c?o.a.createElement(Q.a,{title:l.accept,placement:"top"},o.a.createElement(Ft.a,{className:r.successText})):o.a.createElement(Q.a,{title:l.reject,placement:"top"},o.a.createElement(Mt.a,{className:r.dangerText})),i,s,u,n]}(a,e,n))})),a=o.a.createElement(wn,{color:"primary",headers:[l.rule,l.action,l.protocol,l.sourceAddress,l.targetPort,l.operates],rows:J})}var K=[{href:"/admin/security_policies/",icon:ul.a,label:l.back},{onClick:function(){E(!0)},icon:Dt.a,label:l.createButton}];return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12},o.a.createElement(an,null,o.a.createElement(Ve,{xs:12,sm:6,md:4},o.a.createElement($.a,{display:"flex"},K.map((function(e,a){return e.href?o.a.createElement($.a,{p:1,key:a},o.a.createElement(ue,{size:"sm",color:"info",round:!0,href:e.href},o.a.createElement(e.icon),e.label)):o.a.createElement($.a,{p:1,key:a},o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:e.onClick},o.a.createElement(e.icon),e.label))})))))),o.a.createElement(Ve,{xs:12},o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",{className:r.cardTitleWhite},l.tableTitle)),o.a.createElement(fn,null,a))),o.a.createElement(vn,{place:"tr",color:D,message:B,open:""!==B,closeNotification:z,close:!0}),o.a.createElement(Au,{lang:n,open:h,policyID:t,onSuccess:function(){V(),L("new security policy rule added"),q()},onCancel:V}),o.a.createElement(Fu,{lang:n,open:x,policyID:t,rule:N,onSuccess:function(e){U(),L(e+"th rule modified"),q()},onCancel:U}),o.a.createElement(Iu,{lang:n,open:j,policyID:t,index:N.index,onSuccess:function(e,a){G(),L(a+"the rule removed"),q()},onCancel:G}))}var qu={en:{title:"Batch Deleting Log",content1:"Are you sure to delete ",content2:" log(s)",cancel:"Cancel",confirm:"Confirm"},cn:{title:"\u6279\u91cf\u5220\u9664\u65e5\u5fd7",content1:"\u662f\u5426\u5220\u9664 ",content2:" \u6761\u65e5\u5fd7",cancel:"\u53d6\u6d88",confirm:"\u786e\u5b9a"}};function Hu(e){var a,t=e.lang,n=e.targets,l=e.open,r=e.onSuccess,c=e.onCancel,i=n.length,s=o.a.useState(""),u=Object(H.a)(s,2),m=u[0],d=u[1],p=qu[t],f=function(e){d(e)},b=function(){d(""),r(i)};return a=m&&""!==m?o.a.createElement(Ve,{xs:12},o.a.createElement(Ke,{message:m,color:"danger"})):o.a.createElement(Ve,{xs:12}),o.a.createElement(Pe.a,{open:l,"aria-labelledby":p.title,maxWidth:"sm",fullWidth:!0},o.a.createElement(Me.a,null,p.title),o.a.createElement(Be.a,null,o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:12},p.content1+i.toString()+p.content2),a)),o.a.createElement(Fe.a,null,o.a.createElement(ue,{onClick:function(){d(""),c()},color:"transparent",autoFocus:!0},p.cancel),o.a.createElement(ue,{onClick:function(){!function(e,a,t){Za("/logs/",{entries:e},a,t)}(n,b,f)},color:"info"},p.confirm)))}var Lu=Object(d.a)(Object(d.a)({},Qn),{},{cardTitleWhite:{color:"#FFFFFF",marginTop:"0px",minHeight:"auto",fontWeight:"300",fontFamily:"'Roboto', 'Helvetica', 'Arial', sans-serif",marginBottom:"3px",textDecoration:"none","& small":{color:"#777",fontSize:"65%",fontWeight:"400",lineHeight:"1"}}}),Uu=Object(m.a)(Lu),Gu={en:{batchDelete:"Batch Delete",enterBatch:"Enter Batch Mode",exitBatch:"Exit Batch Mode",tableTitle:"Operate Logs",duration:"Log Duration",time:"Timestamp",content:"Content",noResource:"No log available",day:"The Last Day",month:"The Last Month",year:"The Last Year"},cn:{batchDelete:"\u6279\u91cf\u5220\u9664",enterBatch:"\u8fdb\u5165\u6279\u91cf\u6a21\u5f0f",exitBatch:"\u9000\u51fa\u6279\u91cf\u6a21\u5f0f",tableTitle:"\u64cd\u4f5c\u65e5\u5fd7",duration:"\u65e5\u5fd7\u8303\u56f4",time:"\u65e5\u5fd7\u65f6\u95f4",content:"\u5185\u5bb9",noResource:"\u6ca1\u6709\u65e5\u5fd7\u4fe1\u606f",day:"\u6700\u8fd1\u4e00\u5929",month:"\u6700\u8fd1\u4e00\u4e2a\u6708",year:"\u6700\u8fd1\u4e00\u5e74"}},Vu=function(e){var a,t=e.log,n=e.checked,l=e.checkable,r=e.onCheckStatusChanged;return a=l?o.a.createElement($.a,{display:"flex",alignItems:"center"},o.a.createElement($.a,null,o.a.createElement(Te.a,{checked:n,onChange:function(e){var a=e.target.checked;r(a,t.id)},value:t.id})),o.a.createElement($.a,null,t.time)):t.time,o.a.createElement(kn.a,null,o.a.createElement(Sn.a,null,a),o.a.createElement(Sn.a,null,t.content))};var $u,Zu=t(303),Yu=t.n(Zu),Ju=t(304),Qu=t.n(Ju),Ku=t(305),Xu=t.n(Ku),em=t(306),am=t.n(em),tm=t(307),nm=t.n(tm),om=t(309),lm=t.n(om),rm=t(310),cm=t.n(rm),im=t(308),sm=t.n(im),um=[{path:"/dashboard",name:"dashboard",display:{cn:"\u7cfb\u7edf\u4eea\u8868\u76d8",en:"Dashboard"},icon:Yu.a,component:function(e){return o.a.createElement("div",null,o.a.createElement(u.b,{path:"/admin/dashboard",exact:!0,render:function(a){return o.a.createElement(mo,Object(d.a)(Object(d.a)({},a),e))}}),o.a.createElement(u.b,{path:"/admin/dashboard/pools/",exact:!0,render:function(a){return o.a.createElement(So,Object(d.a)(Object(d.a)({},a),e))}}),o.a.createElement(u.b,{path:"/admin/dashboard/pools/:pool",exact:!0,render:function(a){return o.a.createElement(Mo,Object(d.a)(Object(d.a)({},a),e))}}))},layout:"/admin"},{path:"/compute_pools",name:"compute_pool",display:{cn:"\u8ba1\u7b97\u8d44\u6e90\u6c60",en:"Compute Pools"},icon:Qu.a,component:function(e){var a,t=Zo(),n=e.lang,l=Yo[n],r=o.a.useState(!1),c=Object(H.a)(r,2),i=c[0],s=c[1],u=o.a.useState(null),m=Object(H.a)(u,2),p=m[0],f=m[1],b=o.a.useState(!1),g=Object(H.a)(b,2),h=g[0],E=g[1],v=o.a.useState(!1),y=Object(H.a)(v,2),x=y[0],k=y[1],C=o.a.useState(!1),S=Object(H.a)(C,2),j=S[0],O=S[1],w=o.a.useState(""),_=Object(H.a)(w,2),N=_[0],R=_[1],T=o.a.useState("warning"),I=Object(H.a)(T,2),D=I[0],A=I[1],P=o.a.useState(""),F=Object(H.a)(P,2),B=F[0],M=F[1],z=function(){M("")},W=o.a.useCallback((function(e){if(i){A("warning"),M(e),setTimeout(z,3e3)}}),[A,M,i]),q=o.a.useCallback((function(){if(i){da(f,(function(e){i&&W(e)}))}}),[W,i]),L=function(e){if(i){A("info"),M(e),Ma(e),setTimeout(z,3e3)}},U=function(){k(!1)},G=function(){O(!1)},V=function(){E(!1)};if(o.a.useEffect((function(){return s(!0),q(),function(){s(!1)}}),[q]),null===p)a=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});else if(0===p.length)a=o.a.createElement($.a,{display:"flex",justifyContent:"center"},o.a.createElement(gn,null,l.noPools));else{var Z=[];p.forEach((function(e){var a,n,r,c=[{label:l.cells,icon:Wo.a,href:"/admin/compute_cells/?pool="+e.name},{label:l.instances,icon:Wt.a,href:"/admin/instances/range/?pool="+e.name},{onClick:function(a){return t=e.name,k(!0),void R(t);var t},icon:Ut.a,label:l.modify},{onClick:function(a){return t=e.name,O(!0),void R(t);var t},icon:Ht.a,label:l.delete}],i=e.name,s=e.cells,u=e.network,m=e.storage,p=e.enabled,f=e.failover;p?(a=l.enabled,n=o.a.createElement(Q.a,{title:a,placement:"top"},o.a.createElement(Ft.a,{className:t.successText})),c=[{label:l.disable,icon:Mt.a}].concat(c)):(a=l.disabled,n=o.a.createElement(Q.a,{title:a,placement:"top"},o.a.createElement(Mt.a,{className:t.warningText})),c=[{label:l.enable,icon:Ft.a}].concat(c));r=f?l.on:l.off;var b=c.map((function(e,a){return o.a.createElement(_n,Object(d.a)(Object(d.a)({},e),{},{key:a}))})),g=[i,s,u||l.noAddressPool,m||l.localStorage,r,n,b];Z.push(g)})),a=o.a.createElement(wn,{color:"primary",headers:[l.name,l.cells,l.network,l.storage,l.failover,l.status,l.operates],rows:Z})}return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement(At.a,{separator:"\u203a","aria-label":"breadcrumb"},o.a.createElement(X.a,{color:"textPrimary"},l.computePools))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12,sm:12,md:12},o.a.createElement(an,null,o.a.createElement(Ve,{xs:3,sm:3,md:3},o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){E(!0)}},o.a.createElement(Dt.a,null),l.createButton)))),o.a.createElement(Ve,{xs:12,sm:12,md:12},o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",{className:t.cardTitleWhite},l.tableTitle)),o.a.createElement(fn,null,a))),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:D,message:B,open:""!==B,closeNotification:z,close:!0})),o.a.createElement(Ve,null,o.a.createElement(Uo,{lang:n,open:h,onSuccess:function(e){V(),L("pool "+e+" created"),q()},onCancel:V})),o.a.createElement(Ve,null,o.a.createElement(Vo,{lang:n,open:x,pool:N,onSuccess:function(e){U(),L("pool "+e+" modified"),q()},onCancel:U})),o.a.createElement(Ve,null,o.a.createElement(Ho,{lang:n,open:j,pool:N,onSuccess:function(e){G(),L("pool "+e+" deleted"),q()},onCancel:G})))},layout:"/admin"},{path:"/address_pools",name:"address_pool",display:{cn:"\u5730\u5740\u6c60",en:"Address Pools"},icon:Xu.a,component:function(e){return o.a.createElement("div",null,o.a.createElement(u.b,{path:"/admin/address_pools",exact:!0,render:function(){return o.a.createElement(il,e)}}),o.a.createElement(u.b,{path:"/admin/address_pools/:pool",exact:!0,render:function(a){return o.a.createElement(vl,Object(d.a)(Object(d.a)({},e),a))}}),o.a.createElement(u.b,{path:"/admin/address_pools/:pool/:type/ranges/:start",render:function(a){return o.a.createElement(kl,Object(d.a)(Object(d.a)({},e),a))}}))},layout:"/admin"},{path:"/storage_pools",name:"storage_pool",display:{cn:"\u5b58\u50a8\u6c60",en:"Storage Pools"},icon:Kt.a,component:function(e){var a,t=e.lang,n=Rl[t],l=Nl(),r=o.a.useState(!1),c=Object(H.a)(r,2),i=c[0],s=c[1],u=o.a.useState(null),m=Object(H.a)(u,2),d=m[0],p=m[1],f=o.a.useState(!1),b=Object(H.a)(f,2),g=b[0],h=b[1],E=o.a.useState(!1),v=Object(H.a)(E,2),y=v[0],x=v[1],k=o.a.useState(!1),C=Object(H.a)(k,2),S=C[0],j=C[1],O=o.a.useState(""),w=Object(H.a)(O,2),_=w[0],N=w[1],R=o.a.useState("warning"),T=Object(H.a)(R,2),I=T[0],D=T[1],A=o.a.useState(""),P=Object(H.a)(A,2),F=P[0],B=P[1],M=function(){B("")},z=o.a.useCallback((function(e){if(i){D("warning"),B(e),setTimeout(M,3e3)}}),[D,B,i]),W=o.a.useCallback((function(){if(i){ba(p,(function(e){i&&z(e)}))}}),[z,i]),q=function(e){if(i){D("info"),B(e),Ma(e),setTimeout(M,3e3)}},L=function(){x(!1)},U=function(){j(!1)},G=function(){h(!1)};if(o.a.useEffect((function(){return s(!0),W(),function(){s(!1)}}),[W]),null===d)a=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});else if(0===d.length)a=o.a.createElement($.a,{display:"flex",justifyContent:"center"},o.a.createElement(gn,null,n.noResource));else{var V=[];d.forEach((function(e){var a=[{onClick:function(a){return t=e.name,x(!0),void N(t);var t},icon:Ut.a,label:n.modify},{onClick:function(a){return t=e.name,j(!0),void N(t);var t},icon:Ht.a,label:n.delete}];V.push(function(e,a){var t=a.map((function(e,a){return o.a.createElement(_n,{label:e.label,icon:e.icon,onClick:e.onClick,key:a})}));return[e.name,e.type,e.host,e.target,t]}(e,a))})),a=o.a.createElement(wn,{color:"primary",headers:[n.name,n.type,n.host,n.target,n.operates],rows:V})}return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12,sm:12,md:12},o.a.createElement(an,null,o.a.createElement(Ve,{xs:3,sm:3,md:3},o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){h(!0)}},o.a.createElement(Dt.a,null),n.createButton)))),o.a.createElement(Ve,{xs:12,sm:12,md:12},o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",{className:l.cardTitleWhite},n.tableTitle)),o.a.createElement(fn,null,a))),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:I,message:F,open:""!==F,closeNotification:M,close:!0})),o.a.createElement(Ve,null,o.a.createElement(Ol,{lang:t,open:g,onSuccess:function(e){G(),q("pool "+e+" created"),W()},onCancel:G})),o.a.createElement(Ve,null,o.a.createElement(_l,{lang:t,open:y,pool:_,onSuccess:function(e){L(),q("pool "+e+" modified"),W()},onCancel:L})),o.a.createElement(Ve,null,o.a.createElement(Sl,{lang:t,open:S,pool:_,onSuccess:function(e){U(),q("pool "+e+" deleted"),W()},onCancel:U})))},layout:"/admin"},{path:"/instances",name:"instance",display:{cn:"\u4e91\u4e3b\u673a",en:"Instances"},icon:am.a,component:function(e){return o.a.createElement("div",null,o.a.createElement(u.b,{path:"/admin/instances",exact:!0,render:function(){return o.a.createElement(ts,e)}}),o.a.createElement(u.b,{path:"/admin/instances/range/",render:function(){return o.a.createElement(Uc,e)}}),o.a.createElement(u.b,{path:"/admin/instances/status/:id",render:function(a){return o.a.createElement(Jc,Object(d.a)(Object(d.a)({},a),e))}}),o.a.createElement(u.b,{path:"/admin/instances/snapshots/:id",render:function(a){return o.a.createElement(Ei,Object(d.a)(Object(d.a)({},a),e))}}),o.a.createElement(u.b,{path:"/admin/instances/details/:id",render:function(a){return o.a.createElement(Qi,Object(d.a)(Object(d.a)({},a),e))}}),o.a.createElement(u.b,{path:"/admin/instances/policies/:id",render:function(a){return o.a.createElement(gs,Object(d.a)(Object(d.a)({},a),e))}}))},layout:"/admin"},{path:"/disk_images",name:"image",display:{cn:"\u78c1\u76d8\u955c\u50cf",en:"Disk Images"},icon:nm.a,component:function(e){var a,t=e.lang,n=or[t],l=o.a.useState(!1),r=Object(H.a)(l,2),c=r[0],i=r[1],s=o.a.useState(null),u=Object(H.a)(s,2),m=u[0],p=u[1],f=o.a.useState(!1),b=Object(H.a)(f,2),g=b[0],h=b[1],E=o.a.useState(!1),v=Object(H.a)(E,2),y=v[0],x=v[1],k=o.a.useState(!1),C=Object(H.a)(k,2),S=C[0],j=C[1],O=o.a.useState(!1),w=Object(H.a)(O,2),_=w[0],N=w[1],R=o.a.useState(!1),T=Object(H.a)(R,2),I=T[0],D=T[1],A=o.a.useState(""),P=Object(H.a)(A,2),F=P[0],B=P[1],M=o.a.useState("warning"),z=Object(H.a)(M,2),W=z[0],q=z[1],L=o.a.useState(""),U=Object(H.a)(L,2),G=U[0],V=U[1],Z=function(){V("")},Y=o.a.useCallback((function(e){if(c){q("warning"),V(e),setTimeout(Z,3e3)}}),[q,V,c]),J=function(e){q("info"),V(e),Ma(e),setTimeout(Z,3e3)},Q=o.a.useCallback((function(){if(c){_a((function(e){c&&p(e||[])}),(function(e){c&&Y(e)}))}}),[Y,c]),K=function(){j(!1)},ee=function(){N(!1)},ae=function(){h(!1)},te=function(){x(!1)},ne=function(){D(!1)};return o.a.useEffect((function(){i(!0),Q();var e=setInterval((function(){Q()}),5e3);return function(){i(!1),clearInterval(e)}}),[Q]),a=null===m?o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}}):0===m.length?o.a.createElement($.a,{display:"flex",justifyContent:"center"},o.a.createElement(gn,null,n.noResource)):o.a.createElement(an,null,m.map((function(e,a){var t=[{label:n.modify,icon:Ut.a,onClick:function(){return a=e.id,j(!0),void B(a);var a}},{label:n.download,icon:$l.a,onClick:function(){var a,t=(a=e.id,ua+"/disk_images/"+a+"/file/");window.location.href=t}},{label:n.delete,icon:Ht.a,onClick:function(){return a=e.id,N(!0),void B(a);var a}}],l=function(e,a,t,n){var l=e.name,r=e.size,c=e.tags,i=e.description,s=e.create_time,u=e.modify_time,m=e.id,p=la(r),f=a.map((function(e,a){return o.a.createElement(_n,Object(d.a)(Object(d.a)({},e),{},{key:a}))}));return o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",null,l),o.a.createElement("span",null,m),o.a.createElement($.a,{display:"flex",alignItems:"center"},o.a.createElement($.a,{m:1},p),c?c.map((function(e){return o.a.createElement($.a,{m:0,p:1,key:e},o.a.createElement(Pl.a,{label:e}))})):o.a.createElement($.a,null))),o.a.createElement(fn,null,o.a.createElement(X.a,{variant:"body1",component:"p",noWrap:!0},i),o.a.createElement("p",null,t+": "+s),o.a.createElement("p",null,n+": "+u),f))}(e,t,n.createTime,n.modifyTime);return o.a.createElement(Ve,{xs:12,sm:6,md:4,key:a},l)}))),o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement(an,null,o.a.createElement(Ve,{xs:12,sm:6,md:4},o.a.createElement($.a,{display:"flex"},o.a.createElement($.a,{p:1},o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){h(!0)}},o.a.createElement(Il.a,null),n.uploadButton)),o.a.createElement($.a,{p:1},o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){x(!0)}},o.a.createElement(Qo.a,null),n.buildButton)),o.a.createElement($.a,{p:1},o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){D(!0)}},o.a.createElement(Al.a,null),n.syncButton)))))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12,sm:12,md:12},a),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:W,message:G,open:""!==G,closeNotification:Z,close:!0})),o.a.createElement(Ve,null,o.a.createElement(Ql,{lang:t,open:g,onSuccess:function(e){ae(),J("new image "+e+" uploaded"),Q()},onCancel:ae})),o.a.createElement(Ve,null,o.a.createElement(ar,{lang:t,open:y,onSuccess:function(e){te(),J("new image "+e+" built"),Q()},onCancel:te})),o.a.createElement(Ve,null,o.a.createElement(Xl,{lang:t,imageID:F,open:S,onSuccess:function(e){K(),J("image "+e+" modified"),Q()},onCancel:K})),o.a.createElement(Ve,null,o.a.createElement(Yl,{lang:t,imageID:F,open:_,onSuccess:function(e){ee(),J("image "+e+" deleted"),Q()},onCancel:ee})),o.a.createElement(Ve,null,o.a.createElement(nr,{lang:t,open:I,onSuccess:function(){ne(),J("all disk images synchronized"),Q()},onCancel:ne})))},layout:"/admin"},{path:"/media_images",name:"media",display:{cn:"\u5149\u76d8\u955c\u50cf",en:"Media Images"},icon:mc.a,component:function(e){var a,t=e.lang,n=Gl[t],l=o.a.useState(!1),r=Object(H.a)(l,2),c=r[0],i=r[1],s=o.a.useState(null),u=Object(H.a)(s,2),m=u[0],p=u[1],f=o.a.useState(!1),b=Object(H.a)(f,2),g=b[0],h=b[1],E=o.a.useState(!1),v=Object(H.a)(E,2),y=v[0],x=v[1],k=o.a.useState(!1),C=Object(H.a)(k,2),S=C[0],j=C[1],O=o.a.useState(!1),w=Object(H.a)(O,2),_=w[0],N=w[1],R=o.a.useState(""),T=Object(H.a)(R,2),I=T[0],D=T[1],A=o.a.useState("warning"),P=Object(H.a)(A,2),F=P[0],B=P[1],M=o.a.useState(""),z=Object(H.a)(M,2),W=z[0],q=z[1],L=function(){q("")},U=o.a.useCallback((function(e){if(c){B("warning"),q(e),setTimeout(L,3e3)}}),[B,q,c]),G=function(e){B("info"),q(e),Ma(e),setTimeout(L,3e3)},V=o.a.useCallback((function(){if(c){Oa((function(e){c&&p(e||[])}),(function(e){c&&U(e)}))}}),[U,c]),Z=function(){x(!1)},Y=function(){j(!1)},J=function(){h(!1)},Q=function(){N(!1)};return o.a.useEffect((function(){i(!0),V();var e=setInterval((function(){V()}),5e3);return function(){i(!1),clearInterval(e)}}),[V]),a=null===m?o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}}):0===m.length?o.a.createElement($.a,{display:"flex",justifyContent:"center"},o.a.createElement(gn,null,n.noResource)):o.a.createElement(an,null,m.map((function(e,a){var t=[{label:n.modify,icon:Ut.a,onClick:function(){return a=e.id,x(!0),void D(a);var a}},{label:n.delete,icon:Ht.a,onClick:function(){return a=e.id,j(!0),void D(a);var a}}],l=function(e,a,t,n){var l=e.name,r=e.size,c=e.tags,i=e.description,s=e.create_time,u=e.modify_time,m=e.id,p=la(r),f=a.map((function(e,a){return o.a.createElement(_n,Object(d.a)(Object(d.a)({},e),{},{key:a}))}));return o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",null,l),o.a.createElement("span",null,m),o.a.createElement($.a,{display:"flex",alignItems:"center"},o.a.createElement($.a,{m:1},p),c?c.map((function(e){return o.a.createElement($.a,{m:0,p:1,key:e},o.a.createElement(Pl.a,{label:e}))})):o.a.createElement($.a,null))),o.a.createElement(fn,null,o.a.createElement(X.a,{variant:"body1",component:"p",noWrap:!0},i),o.a.createElement("p",null,t+": "+s),o.a.createElement("p",null,n+": "+u),f))}(e,t,n.createTime,n.modifyTime);return o.a.createElement(Ve,{xs:12,sm:6,md:4,key:a},l)}))),o.a.createElement(an,null,o.a.createElement(Ve,{xs:12,sm:12,md:12},o.a.createElement(an,null,o.a.createElement(Ve,{xs:12,sm:6,md:4},o.a.createElement($.a,{display:"flex"},o.a.createElement($.a,{p:1},o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){h(!0)}},o.a.createElement(Il.a,null),n.uploadButton)),o.a.createElement($.a,{p:1},o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){N(!0)}},o.a.createElement(Al.a,null),n.syncButton)))))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12,sm:12,md:12},a),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:F,message:W,open:""!==W,closeNotification:L,close:!0})),o.a.createElement(Ve,null,o.a.createElement(Wl,{lang:t,open:g,onSuccess:function(e){J(),G("new image "+e+" uploaded"),V()},onCancel:J})),o.a.createElement(Ve,null,o.a.createElement(Hl,{lang:t,imageID:I,open:y,onSuccess:function(e){Z(),G("image "+e+" modified"),V()},onCancel:Z})),o.a.createElement(Ve,null,o.a.createElement(Bl,{lang:t,imageID:I,open:S,onSuccess:function(e){Y(),G("image "+e+" deleted"),V()},onCancel:Y})),o.a.createElement(Ve,null,o.a.createElement(Ul,{lang:t,open:_,onSuccess:function(){Q(),G("all media images synchronized"),V()},onCancel:Q})))},layout:"/admin"},{path:"/system_templates",name:"templates",display:{cn:"\u7cfb\u7edf\u6a21\u677f",en:"System Templates"},icon:sm.a,component:function(e){var a,t=e.lang,n=Or[t],l=jr(),r=o.a.useState(!1),c=Object(H.a)(r,2),i=c[0],s=c[1],u=o.a.useState(null),m=Object(H.a)(u,2),d=m[0],p=m[1],f=o.a.useState(!1),b=Object(H.a)(f,2),g=b[0],h=b[1],E=o.a.useState(!1),v=Object(H.a)(E,2),y=v[0],x=v[1],k=o.a.useState(!1),C=Object(H.a)(k,2),S=C[0],j=C[1],O=o.a.useState(""),w=Object(H.a)(O,2),_=w[0],N=w[1],R=o.a.useState("warning"),T=Object(H.a)(R,2),I=T[0],D=T[1],A=o.a.useState(""),P=Object(H.a)(A,2),F=P[0],B=P[1],M=function(){B("")},z=o.a.useCallback((function(e){if(i){D("warning"),B(e),setTimeout(M,3e3)}}),[D,B,i]),W=o.a.useCallback((function(){i&&Pa(p,z)}),[z,i]),q=function(e){if(i){D("info"),B(e),Ma(e),setTimeout(M,3e3)}},L=function(){x(!1)},U=function(){j(!1)},G=function(){h(!1)};if(o.a.useEffect((function(){return s(!0),W(),function(){s(!1)}}),[W]),null===d)a=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});else if(0===d.length)a=o.a.createElement($.a,{display:"flex",justifyContent:"center"},o.a.createElement(gn,null,n.noResource));else{var V=[];d.forEach((function(e){var a=[{onClick:function(a){return t=e.id,x(!0),void N(t);var t},icon:Ut.a,label:n.detail},{onClick:function(a){return t=e.id,j(!0),void N(t);var t},icon:Ht.a,label:n.delete}];V.push(function(e,a){var t=a.map((function(e,a){return o.a.createElement(_n,{label:e.label,icon:e.icon,onClick:e.onClick,key:a})}));return[e.id,e.name,e.operating_system,e.created_time,e.modified_time,t]}(e,a))})),a=o.a.createElement(wn,{color:"primary",headers:["ID",n.name,n.os,n.createdTime,n.modifiedTime,n.operates],rows:V})}return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12,sm:12,md:12},o.a.createElement(an,null,o.a.createElement(Ve,{xs:3,sm:3,md:3},o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){h(!0)}},o.a.createElement(Dt.a,null),n.createButton)))),o.a.createElement(Ve,{xs:12,sm:12,md:12},o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",{className:l.cardTitleWhite},n.tableTitle)),o.a.createElement(fn,null,a))),o.a.createElement(vn,{place:"tr",color:I,message:F,open:""!==F,closeNotification:M,close:!0}),o.a.createElement(br,{lang:t,open:g,onSuccess:function(e){G(),q("new template "+e+" created"),W()},onCancel:G}),o.a.createElement(Sr,{lang:t,open:y,templateID:_,onSuccess:function(e){L(),q("template "+e+" modified"),W()},onCancel:L}),o.a.createElement(rr,{lang:t,open:S,templateID:_,onSuccess:function(e){U(),q("template "+e+" deleted"),W()},onCancel:U}))},layout:"/admin"},{path:"/security_policies",name:"policies",display:{cn:"\u5b89\u5168\u7b56\u7565\u7ec4",en:"Security Policies"},icon:Qr.a,component:function(e){return o.a.createElement("div",null,o.a.createElement(u.b,{path:"/admin/security_policies/",exact:!0,render:function(){return o.a.createElement(Ru,e)}}),o.a.createElement(u.b,{path:"/admin/security_policies/:id/rules/",render:function(a){return o.a.createElement(Wu,Object(d.a)(Object(d.a)({},a),e))}}))},layout:"/admin"},{path:"/users",name:"user",display:{cn:"\u7528\u6237\u7ba1\u7406",en:"User Management"},icon:lm.a,component:function(e){var a=e.lang,t=fu[a];return o.a.createElement(js,{title:t.title,headerColor:"primary",tabs:[{tabName:t.user,tabIcon:de.a,tabContent:o.a.createElement(zs,{lang:a})},{tabName:t.group,tabIcon:Es.a,tabContent:o.a.createElement(bu,{lang:a})},{tabName:t.role,tabIcon:ys.a,tabContent:o.a.createElement(pu,{lang:a})}]})},layout:"/admin"},{path:"/logs",name:"log",display:{cn:"\u64cd\u4f5c\u65e5\u5fd7",en:"Operate Logs"},icon:cm.a,component:function(e){var a=Uu(),t=o.a.useState(null),n=Object(H.a)(t,2),l=n[0],r=n[1],c=o.a.useState(new Map),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(!1),p=Object(H.a)(m,2),f=p[0],b=p[1],g=o.a.useState({offset:0,duration:"last-day"}),h=Object(H.a)(g,2),E=h[0],v=h[1],y=o.a.useState({current:0,total:0}),x=Object(H.a)(y,2),k=x[0],C=x[1],S=o.a.useState(!1),j=Object(H.a)(S,2),O=j[0],w=j[1],_=o.a.useState("warning"),N=Object(H.a)(_,2),R=N[0],T=N[1],I=o.a.useState(""),D=Object(H.a)(I,2),A=D[0],P=D[1],F=function(){P("")},B=o.a.useCallback((function(e){T("warning"),P(e),setTimeout(F,3e3)}),[T,P]),M=function(e){var a=function(e){return e<10?"0"+e.toString():e.toString()};return e.getFullYear()+"-"+a(e.getMonth()+1)+"-"+a(e.getDate())+" "+a(e.getHours())+":"+a(e.getMinutes())+":"+a(e.getSeconds())},z=o.a.useCallback((function(){var e=new Date,a=new Date(e);switch(E.duration){case"last-day":a.setDate(e.getDate()-1);break;case"last-month":a.setMonth(e.getMonth()-1);break;case"last-year":a.setFullYear(e.getFullYear()-1);break;default:return void B("invalid duration: "+E.duration)}!function(e,a,t,n,o,l){var r="/logs/?limit="+e;a&&(r+="&start="+a),t&&(r+="&after="+t),n&&(r+="&before="+n),Wa(r,e=>{let t=0;a&&(t=a),o({...e,offset:t})},l)}(10,E.offset,M(a),M(e),(function(e){var a,t,n=e.logs,o=e.total,l=e.offset,c=new Map(s),i=!1;if(n){r(n);var m=[];c.forEach((function(e,a){n.some((function(e){return e.id===a}))||m.push(a)})),n.forEach((function(e){var a=e.id;c.has(a)||(c.set(a,!1),i||(i=!0))})),0!==m.length&&m.forEach((function(e){c.delete(e),i||(i=!0)}))}else r([]),0!==c.size&&(c.clear(),i=!0);(i&&u(new Map(c)),0!==o)&&(a=l<10?0:Math.floor(l/10),t=0===o%10?o/10:Math.ceil(o/10),C({current:a,total:t}))}),(function(e){B(e)}))}),[E,s,B]),W=function(){w(!1)},q=function(e,a){var t=new Map(s);t.set(a,e),u(t)};if(o.a.useEffect((function(){z()}),[z]),null===ta())return oa();var L,U=e.lang,G=Gu[U];if(l)if(0===l.length)L=o.a.createElement(gn,null,G.noResource);else{var V;V=f?o.a.createElement($.a,{display:"flex",alignItems:"center"},o.a.createElement($.a,null,o.a.createElement(Te.a,{onChange:function(e){var a,t=e.target.checked,n=new Map,o=Object(wr.a)(s.keys());try{for(o.s();!(a=o.n()).done;){var l=a.value;n.set(l,t)}}catch(r){o.e(r)}finally{o.f()}u(n)}})),o.a.createElement($.a,null,G.time)):G.time,L=o.a.createElement(Pr,{color:"primary",headers:[V,G.content],rows:l.map((function(e){var a=e.id;return o.a.createElement(Vu,{key:a,log:e,checked:!(!s||!s.has(a))&&s.get(a),checkable:f,onCheckStatusChanged:q})}))})}else L=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});var Z=[];f?Z.push(o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){w(!0)}},o.a.createElement(Ht.a,null),G.batchDelete),o.a.createElement(ue,{size:"sm",color:"rose",round:!0,onClick:function(){b(!1)}},o.a.createElement(Dr.a,null),G.exitBatch)):Z.push(o.a.createElement(ue,{size:"sm",color:"info",round:!0,onClick:function(){var e,a=new Map,t=Object(wr.a)(s.keys());try{for(t.s();!(e=t.n()).done;){var n=e.value;a.set(n,!1)}}catch(o){t.e(o)}finally{t.f()}u(a),b(!0)}},o.a.createElement(Xo.a,null),G.enterBatch));var Y,Q=[{label:G.day,value:"last-day"},{label:G.month,value:"last-month"},{label:G.year,value:"last-year"}],K=o.a.createElement($.a,{m:0,pt:2},o.a.createElement(we.a,{component:"fieldset",fullWidth:!0},o.a.createElement(_e.a,{component:"legend"},G.duration),o.a.createElement(je.a,{name:"duration",value:E.duration,onChange:function(e){var a=e.target.value;v({offset:0,duration:a}),z()},row:!0},o.a.createElement($.a,{display:"flex",alignItems:"center"},Q.map((function(e,a){return o.a.createElement($.a,{key:a},o.a.createElement(Oe.a,{value:e.value,control:o.a.createElement(Se.a,null),label:e.label}))})))))),ee=[];if(s&&(s.forEach((function(e,a){e&&ee.push(a)})),ee.sort()),k.total>1){for(var ae=[],te=function(){var e=ne;ne===k.current?ae.push(o.a.createElement(X.a,null,e+1)):ae.push(o.a.createElement(J.a,{href:"#",underline:"none",onClick:function(){return function(e){var a=10*e;v((function(e){return Object(d.a)(Object(d.a)({},e),{},{offset:a})}))}(e)}},e+1))},ne=0;ne<k.total;ne++)te();Y=o.a.createElement($.a,{display:"flex",alignItems:"center",justifyContent:"center"},ae.map((function(e,a){return o.a.createElement($.a,{key:a,m:1,p:0},e)})))}else Y=o.a.createElement("div",null);return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},K))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{mt:3,mb:3},o.a.createElement(ve.a,null))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{display:"flex"},Z.map((function(e,a){return o.a.createElement($.a,{key:a,m:1},e)})))),o.a.createElement(Ve,{xs:12,sm:12,md:12},o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",{className:a.cardTitleWhite},G.tableTitle)),o.a.createElement(fn,null,L,Y))),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:R,message:A,open:""!==A,closeNotification:F,close:!0})),o.a.createElement(Ve,null,o.a.createElement(Hu,{lang:U,open:O,targets:O?ee:[],onSuccess:function(e){var a;W(),a=e.toString()+" log(s) deleted",T("info"),P(a),setTimeout(F,3e3),Ma(e.toString()+" log(s) deleted"),z()},onCancel:W})))},layout:"/admin"},{path:"/visibilities",name:"visibility",display:{cn:"\u8d44\u6e90\u53ef\u89c1\u6027",en:"Resource Visibilities"},icon:dl.a,component:function(e){var a,t=e.lang,n=Eu[t],l=hu(),r=o.a.useState(!1),c=Object(H.a)(r,2),i=c[0],s=c[1],u=o.a.useState({instanceVisible:!1,diskImageVisible:!1,mediaImageVisible:!1}),m=Object(H.a)(u,2),p=m[0],f=m[1],b=o.a.useState("warning"),g=Object(H.a)(b,2),h=g[0],E=g[1],v=o.a.useState(""),y=Object(H.a)(v,2),x=y[0],k=y[1],C=function(){k("")},S=o.a.useCallback((function(e){E("warning"),k(e),setTimeout(C,3e3)}),[E,k]),j=o.a.useCallback((function(){Wa("/resource_visibilities/",(function(e){var a={};e.instance_visible&&(a.instanceVisible=!0),e.disk_image_visible&&(a.diskImageVisible=!0),e.media_image_visible&&(a.mediaImageVisible=!0),f(a),s(!0)}),(function(e){S(e)}))}),[S]),O=function(e){return function(a){var t=a.target.checked;f((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}};if(o.a.useEffect((function(){j()}),[j]),null===ta())return oa();a=i?o.a.createElement(Pr,{color:"primary",headers:[n.visibility,n.visibility],rows:[o.a.createElement(vu,{key:"instance",checked:p.instanceVisible,onChange:O("instanceVisible"),label:n.instance,description:n.instanceDescription,classes:l}),o.a.createElement(vu,{key:"disk",checked:p.diskImageVisible,onChange:O("diskImageVisible"),label:n.disk,description:n.diskDescription,classes:l}),o.a.createElement(vu,{key:"media",checked:p.mediaImageVisible,onChange:O("mediaImageVisible"),label:n.media,description:n.mediaDescription,classes:l})]}):o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});var w=[o.a.createElement(ue,{key:"modify",color:"info",onClick:function(){!function(e,a,t,n,o){var l={};e&&(l.instance_visible=e),a&&(l.disk_image_visible=a),t&&(l.media_image_visible=t),Ga("/resource_visibilities/",l,n,o)}(p.instanceVisible,p.diskImageVisible,p.mediaImageVisible,(function(){var e;e="Visibilities updated",E("info"),k(e),setTimeout(C,3e3),Ma("Visibilities updated")}),S)}},o.a.createElement(Qo.a,null),n.modify)];return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},a),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{display:"flex"},w.map((function(e,a){return o.a.createElement($.a,{key:a,m:1},e)})))),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:h,message:x,open:""!==x,closeNotification:C,close:!0})))},layout:"/admin"}],mm=function(e){var a;return{wrapper:{position:"relative",top:"0",height:"100vh"},mainPanel:Object(d.a)(Object(d.a)((a={},Object(le.a)(a,e.breakpoints.up("md"),{width:"calc(100% - ".concat(260,"px)")}),Object(le.a)(a,"overflow","auto"),Object(le.a)(a,"position","relative"),Object(le.a)(a,"float","right"),a),f),{},{maxHeight:"100%",width:"100%",overflowScrolling:"touch"}),content:{marginTop:"70px",padding:"30px 15px",minHeight:"calc(100vh - 123px)"},container:b,map:{marginTop:"70px"}}},dm=t(311),pm=t.n(dm),fm=t(312),bm=t.n(fm),gm=["routes"],hm=["lang","setLang"];function Em(e){var a=e.routes,t=Object(L.a)(e,gm),n=a[0],l=n.layout+n.path;return o.a.createElement(u.d,null,a.map((function(e,a){return"/admin"===e.layout?o.a.createElement(u.b,{path:e.layout+e.path,render:function(){return o.a.createElement(e.component,t)},key:a}):null})),o.a.createElement(u.b,{path:"/admin/compute_cells/",render:function(){return o.a.createElement(Vn,t)}}),o.a.createElement(u.a,{from:"/admin",to:l}))}var vm=Object(m.a)(mm);function ym(e){var a=e.lang,t=e.setLang,n=Object(L.a)(e,hm),l=vm(),r=o.a.createRef(),c=o.a.useState(!1),i=Object(H.a)(c,2),s=i[0],u=i[1],m=function(){u(!s)},d=function(){window.innerWidth>=960&&u(!1)};o.a.useEffect((function(){return navigator.platform.indexOf("Win")>-1&&($u=new U.a(r.current,{suppressScrollX:!0,suppressScrollY:!1}),document.body.style.overflow="hidden"),window.addEventListener("resize",d),function(){navigator.platform.indexOf("Win")>-1&&$u.destroy(),window.removeEventListener("resize",d)}}),[r]);var p=ta();if(null===p)return oa();var f=p.menu,b=[];return um.forEach((function(e){f.some((function(a){return a===e.name}))&&b.push(e)})),o.a.createElement("div",{className:l.wrapper},o.a.createElement(Rt,Object.assign({routes:b,logoText:"Nano Portal",logo:bm.a,image:pm.a,handleDrawerToggle:m,open:s,color:"blue",lang:a},n)),o.a.createElement("div",{className:l.mainPanel,ref:r},o.a.createElement(xt,Object.assign({routes:b,handleDrawerToggle:m,lang:a,setLang:t},n)),o.a.createElement("div",{className:l.content},o.a.createElement("div",{className:l.container},o.a.createElement(Em,{lang:a,routes:b})))))}var xm=t(194),km=t.n(xm),Cm=t(193),Sm=t.n(Cm);const jm=km()(()=>({background:{backgroundImage:"url("+Sm.a+")",height:"100vh"}})),Om={cn:{title:"Nano\u7ba1\u7406\u95e8\u6237",user:"\u7528\u6237\u540d",password:"\u5bc6\u7801",login:"\u767b\u5f55"},en:{title:"Nano Web Portal",user:"User",password:"Password",login:"Login"}};function wm(e){const{lang:a,setLang:t}=e,n=Om[a],l=jm(),[r,c]=o.a.useState({user:"",password:"",nonce:"stub",type:"manager"}),[i,s]=o.a.useState(""),[m,d]=o.a.useState(!0),[p,f]=o.a.useState(!1),[b,g]=o.a.useState(!1),h=o.a.useCallback(e=>{s(e),setTimeout(()=>{s("")},5e3)},[s]),E=e=>a=>{var t=a.target.value;c(a=>({...a,[e]:t}))},v=o.a.useCallback(e=>{h(e)},[h]),y=e=>{s(e)},x=e=>{!function(e){localStorage.setItem("nano-session-data",JSON.stringify(e))}({id:e.session,timeout:e.timeout,menu:e.menu,address:e.address,user:r.user,group:e.group,nonce:r.nonce,type:r.type}),b||(g(!0),Ma("login success"))},k=()=>{var e,a,t,n;e=r.user,a=r.password,t=x,n=v,Ua("/sessions/",{user:e,password:a,nonce:(()=>{for(var e="",a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",t=0;t<16;t++)e+=a.charAt(Math.floor(Math.random()*a.length));return e})()},t,n)};let C,S;if(o.a.useEffect(()=>{if(p)return;za(e=>{e.ready||d(!1),f(!0)},y)},[p,v]),p){if(!m)return o.a.createElement(u.a,{to:"/initial"});if(b){const e="previous";var j=new URLSearchParams(window.location.search);return j.has(e)?o.a.createElement(u.a,{to:decodeURIComponent(j.get(e))}):o.a.createElement(u.a,{to:"/admin"})}{let e;e=i?o.a.createElement(Ve,{xs:12},o.a.createElement(Ke,{message:i,color:"danger"})):o.a.createElement(Ve,{xs:12}),S=o.a.createElement(ue,{color:"info",onClick:k},n.login),C=o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:n.user,onChange:E("user"),value:r.user,margin:"normal",required:!0,fullWidth:!0}))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:n.password,onChange:E("password"),value:r.password,margin:"normal",type:"password",required:!0,fullWidth:!0}))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{alignItems:"center",display:"flex",m:1},S,o.a.createElement($.a,{flexGrow:1}),o.a.createElement(ht,{lang:a,setLang:t}))),o.a.createElement(Ve,{xs:12},e))}}else if(i){let e;e="en"===a?"System check failed, Please check backend service status\r\n".concat(i):"\u7cfb\u7edf\u68c0\u6d4b\u5931\u8d25\uff0c\u8bf7\u68c0\u67e5\u540e\u53f0\u670d\u52a1\u72b6\u6001\r\n".concat(i),C=o.a.createElement(Ve,{xs:12},o.a.createElement(Ke,{message:e,color:"warning"}))}else C=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}});return o.a.createElement($.a,{component:"div",className:l.background},o.a.createElement(vi.a,{maxWidth:"lg"},o.a.createElement(Ne.a,{container:!0,justifyContent:"center"},o.a.createElement(Ne.a,{item:!0,xs:10,sm:6,md:4},o.a.createElement($.a,{mt:20,p:0},o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",{className:l.cardTitleWhite},n.title)),o.a.createElement(fn,null,C)))))))}var _m=km()((function(){return Object(d.a)(Object(d.a)({},Qn),{},{background:{backgroundImage:"url("+Sm.a+")",height:"100vh"}})})),Nm={cn:{welcome:"\u6b22\u8fce\u4f7f\u7528Nano\u4e91\u5e73\u53f0",description:"\u8bf7\u8bbe\u5b9a\u7ba1\u7406\u5458\u8d26\u53f7\u53ca\u5bc6\u7801\uff0c\u5f00\u59cb\u521d\u59cb\u5316\u7cfb\u7edf",user:"\u9ed8\u8ba4\u7ba1\u7406\u5458\u5e10\u53f7",password:"\u5bc6\u7801",password2:"\u786e\u8ba4\u5bc6\u7801",initial:"\u521d\u59cb\u5316",confirm:"\u786e\u8ba4",success:"\u7cfb\u7edf\u521d\u59cb\u5316\u6210\u529f\uff0c\u70b9\u51fb\u8fdb\u5165\u767b\u5f55\u9875\u9762"},en:{welcome:"Welcome to Nano",description:"Please set up a new admin account",user:"Super Admin Name",password:"Password",password2:"Confirm Password",initial:"Initial System",confirm:"Confirm",success:"System initialed, click to login"}};function Rm(e){var a,t,n,l=0,r=1,c=2,i=3,s=_m(),m=e.lang,p=e.setLang,f=Nm[m],b=o.a.useState({user:"",password:"",password2:""}),g=Object(H.a)(b,2),h=g[0],E=g[1],v=o.a.useState(""),y=Object(H.a)(v,2),x=y[0],k=y[1],C=o.a.useState(l),S=Object(H.a)(C,2),j=S[0],O=S[1],w=function(e){return function(a){var t=a.target.value;E((function(a){return Object(d.a)(Object(d.a)({},a),{},Object(le.a)({},e,t))}))}},_=o.a.useCallback((function(e){k(e),setTimeout((function(){k("")}),5e3)}),[]),N=function(){k(""),O(c)},R=o.a.useCallback((function(){O(i)}),[i]);switch(o.a.useEffect((function(){za((function(e){e.ready?R():O(r)}),_)}),[r,_,R]),a=x&&""!==x?o.a.createElement(Ve,{xs:12},o.a.createElement(Ke,{message:x,color:"danger"})):o.a.createElement(Ve,{xs:12}),j){case r:n=o.a.createElement(ue,{color:"info",onClick:function(){var e=new RegExp("[^\\w-.]");if(h.user)if(e.test(h.user))_("only letter/digit/'-'/'_'/'.' allowed in username");else if(h.password)if(h.password2===h.password){var a=[];ma(m).forEach((function(e){a.push(e.value)})),function(e,a,t,n,o,l,r,c){var i={user:e,password:o,menu:l};a&&(i.group=a),t&&(i.display=t),n&&(i.role=n),Ua("/system/",i,r,c)}(h.user,null,null,null,h.password,a,N,_)}else _("password mismatch");else _("please input password");else _("must specify user name")}},f.initial),t=o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{justifyContent:"center",display:"flex"},o.a.createElement(X.a,{className:s.cardTitle},f.description))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:f.user,onChange:w("user"),value:h.user,margin:"normal",required:!0,fullWidth:!0}))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:f.password,onChange:w("password"),value:h.password,margin:"normal",type:"password",required:!0,fullWidth:!0}))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{m:0,pt:2},o.a.createElement(Ce.a,{label:f.password2,onChange:w("password2"),value:h.password2,margin:"normal",type:"password",required:!0,fullWidth:!0}))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{alignItems:"center",display:"flex",m:1},n,o.a.createElement($.a,{flexGrow:1}),o.a.createElement(ht,{lang:m,setLang:p}))),o.a.createElement(Ve,{xs:12},a));break;case c:n=o.a.createElement(ue,{color:"info",onClick:function(){R()}},f.confirm),t=o.a.createElement(Ne.a,{container:!0},o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{justifyContent:"center",display:"flex"},o.a.createElement(X.a,{variant:"body1",component:"span",className:s.cardTitle},f.success))),o.a.createElement(Ve,{xs:12},o.a.createElement($.a,{justifyContent:"center",display:"flex"},n)));break;case i:return o.a.createElement(u.a,{to:"/login"});default:t=o.a.createElement("div",null,o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}}),a)}return o.a.createElement($.a,{component:"div",className:s.background},o.a.createElement(vi.a,{maxWidth:"lg"},o.a.createElement(Ne.a,{container:!0,justify:"center"},o.a.createElement(Ne.a,{item:!0,xs:12,sm:8,md:4},o.a.createElement($.a,{mt:20,p:0},o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},o.a.createElement("h4",{className:s.cardTitleWhite},f.welcome)),o.a.createElement(fn,null,t)))))))}var Tm=t(314),Im=t.n(Tm),Dm=t(313),Am=t.n(Dm);function Pm(e){var a=e.url,t=e.password,n=e.callback,l=e.onFocusChanged,r=o.a.createRef(),c=o.a.useState(null),i=Object(H.a)(c,2),s=i[0],u=i[1],m=o.a.useState(!1),d=Object(H.a)(m,2),p=d[0],f=d[1],b=o.a.useState(!1),g=Object(H.a)(b,2),h=g[0],E=g[1],v=function(){p&&s&&(s.focus(),l(!0))};return o.a.useEffect((function(){if(r&&r.current&&a&&t){if(f(!0),!h){var e={credentials:{password:t},clipViewport:!0,focusOnClick:!1,qualityLevel:8},n=new Am.a(r.current,a,e);u(n),E(!0)}return function(){f(!1)}}}),[r,t,a,h]),n.onEmergency=function(){p&&s&&s.sendCtrlAltDel()},o.a.createElement("div",{ref:r,onMouseOver:v,onMouseOut:function(){p&&s&&(s.blur(),l(!1))},onMouseDown:function(e){e.preventDefault(),v()}})}var Fm={en:{instance:"Instance",sendKeys:"Send Ctrl+Alt+Delete",stop:"Stop Instance",reboot:"Reboot Instance",reset:"Reset Instance",insertMedia:"Insert Media",ejectMedia:"Eject Media",activated:"The input is already redirected, move out the mouse to release control",deactivated:"Move the mouse to the screen to control the instance"},cn:{instance:"\u4e91\u4e3b\u673a",sendKeys:"\u53d1\u9001 Ctrl+Alt+Delete",stop:"\u505c\u6b62\u4e91\u4e3b\u673a",reboot:"\u91cd\u542f\u4e91\u4e3b\u673a",reset:"\u5f3a\u5236\u91cd\u542f\u4e91\u4e3b\u673a",insertMedia:"\u63d2\u5165\u5149\u76d8\u955c\u50cf",ejectMedia:"\u5f39\u51fa\u5149\u76d8\u955c\u50cf",activated:"\u8f93\u5165\u5df2\u91cd\u5b9a\u5411\u5230\u4e91\u4e3b\u673a\uff0c\u9f20\u6807\u79bb\u5f00\u753b\u9762\u89e3\u9664\u63a7\u5236",deactivated:"\u9f20\u6807\u79fb\u52a8\u5230\u76d1\u63a7\u753b\u9762\u5f00\u59cb\u63a7\u5236\u4e91\u4e3b\u673a"}};function Bm(e){var a,t,n=e.match.params.id,l=e.lang,r=o.a.useState(null),c=Object(H.a)(r,2),i=c[0],s=c[1],u=o.a.useState(!1),m=Object(H.a)(u,2),d=m[0],p=m[1],f=o.a.useState(!1),b=Object(H.a)(f,2),g=b[0],h=b[1],E=o.a.useState(!1),v=Object(H.a)(E,2),y=v[0],x=v[1],k=o.a.useState(!1),C=Object(H.a)(k,2),S=C[0],j=C[1],O=o.a.useState(!1),w=Object(H.a)(O,2),_=w[0],N=w[1],R=o.a.useState("warning"),T=Object(H.a)(R,2),I=T[0],D=T[1],A=o.a.useState(""),P=Object(H.a)(A,2),F=P[0],B=P[1],M=Fm[l],z=function(){B("")},W=o.a.useCallback((function(e){if(g){D("warning"),B(e),setTimeout(z,3e3)}}),[g,D,B]),q=function(e){if(g){D("info"),B(e),Ma(e),setTimeout(z,3e3)}},L=o.a.useCallback((function(e){W(e)}),[W]),U=function(){N(!1)};if(o.a.useEffect((function(){if(n){if(h(!0),!S&&!y){j(!0);va(n,(function(e){!function(e,a,t){Ha("/monitor_channels/",{guest:e},e=>{a(e.id,e.password)},t)}(n,(function(a,t){var n={name:e.name,pool:e.pool,cell:e.cell,channel:a,password:t,delegate:{}};s(n),x(!0),j(!1)}),L)}),L)}return function(){h(!1)}}}),[n,L,y,S]),y){var G=function(e){var a=ua+"/monitor_channels/"+e;if(a.startsWith("ws://"))return a;if(a.startsWith("http://"))return a.replace("http://","ws://");if(a.startsWith("https://"))return a.replace("https://","ws://");var t="ws://"+window.location.hostname;return window.location.port&&(t+=":"+window.location.port),t+a}(i.channel);a=o.a.createElement(Pm,{url:G,password:i.password,callback:i.delegate,onFocusChanged:function(e){p(e)}});var V=[{tips:M.sendKeys,icon:Im.a,handler:function(){i&&i.delegate&&i.delegate.onEmergency&&i.delegate.onEmergency()}},{tips:M.insertMedia,icon:lc.a,handler:function(){N(!0)}},{tips:M.ejectMedia,icon:cc.a,handler:function(){xa(n,(function(){q("media of instance "+i.name+" ejected")}),L)}},{tips:M.stop,icon:Nr.a,handler:function(){ka(n,(function(){q("instance "+i.name+" stopped")}),L)}},{tips:M.reboot,icon:ac.a,handler:function(){Ca(n,(function(){q("instance "+i.name+" reboot")}),L)}},{tips:M.reset,icon:nc.a,handler:function(){Sa(n,(function(){q("instance "+i.name+" reset")}),L)}}];t=o.a.createElement($.a,{display:"flex",alignItems:"center"},o.a.createElement($.a,{flexGrow:1,fontWeight:"fontWeightBold",letterSpacing:10},o.a.createElement(X.a,{component:"span"},M.instance+": "+i.name+"  ("+(d?M.activated:M.deactivated)+")")),V.map((function(e,a){return o.a.createElement($.a,{key:a},o.a.createElement(Q.a,{title:e.tips,placement:"top"},o.a.createElement(K.a,{color:"inherit",onClick:e.handler},o.a.createElement(e.icon))))})))}else a=o.a.createElement(Tt.a,{variant:"rect",style:{height:"10rem"}}),t=o.a.createElement($.a,null);return o.a.createElement(an,null,o.a.createElement(Ve,{xs:12},o.a.createElement(ln,null,o.a.createElement(un,{color:"primary"},t),o.a.createElement(fn,null,a))),o.a.createElement(Ve,null,o.a.createElement(vn,{place:"tr",color:I,message:F,open:""!==F,closeNotification:z,close:!0})),o.a.createElement(Ve,null,o.a.createElement(_c,{lang:l,instanceID:n,open:_,onSuccess:function(){U(),q("media insert into instance "+i.name)},onCancel:U})))}t(517);class Mm extends o.a.Component{constructor(e){super(e),this.state={hasError:!1,error:null,errorStack:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e.message,errorStack:e.stack}}render(){return this.state.hasError?o.a.createElement(q,null,this.state.error):this.props.children}}const zm=Object(s.a)(),Wm=Object(c.a)({palette:{primary:{light:h[1],main:h[0]},secondary:{light:x[1],main:x[0]},error:{light:v[1],main:v[0]}}});function qm(e){const[a,t]=o.a.useState(function(){var e=localStorage.getItem("nano-language-data");if(!e||0===e.length)return"cn";var a=JSON.parse(e);return a.lang?a.lang:"cn"}());return o.a.createElement(i.a,{theme:Wm},o.a.createElement(Mm,null,o.a.createElement(u.c,{history:zm},o.a.createElement(u.d,null,o.a.createElement(u.b,{path:"/admin",render:e=>o.a.createElement(ym,{lang:a,setLang:t})}),o.a.createElement(u.b,{path:"/login",render:e=>o.a.createElement(wm,{lang:a,setLang:t})}),o.a.createElement(u.b,{path:"/initial",render:e=>o.a.createElement(Rm,{lang:a,setLang:t})}),o.a.createElement(u.b,{path:"/monitor/:id",render:e=>o.a.createElement(Bm,Object.assign({lang:a},e))}),o.a.createElement(u.a,{from:"/",to:"/login"})))))}r.a.render(o.a.createElement(qm,null),document.getElementById("root"))},94:function(e){e.exports=JSON.parse('{"name":"nano-portal","version":"1.4.0","description":"Project-Nano manage portal","service":{"host":"","port":5870,"debug":false},"private":false,"dependencies":{"@material-ui/core":"^4.11.0","@material-ui/icons":"^4.11.0","@material-ui/lab":"4.0.0-alpha.61","@novnc/novnc":"^1.2.0","axios":"^0.19.2","chart.js":"^2.9.4","chartjs-plugin-streaming":"^1.8.0","classnames":"^2.2.6","dateformat":"^3.0.3","history":"^4.10.1","perfect-scrollbar":"^1.5.0","prop-types":"^15.7.2","react":"^16.14.0","react-chartjs-2":"^2.11.1","react-dom":"^16.14.0","react-router-dom":"^5.2.0","react-scripts":"^3.4.4","react-swipeable-views":"^0.13.9","typeface-roboto":"^0.0.75"},"scripts":{"start":"react-scripts start","build":"react-scripts build","test":"react-scripts test --env=jsdom","eject":"react-scripts eject","install:clean":"rm -rf node_modules/ && rm -rf package-lock.json && npm install && npm start","lint:check":"eslint . --ext=js,jsx;  exit 0","lint:fix":"eslint . --ext=js,jsx --fix;  exit 0","build-package-css":"cp src/assets/css/material-dashboard-react.css dist/material-dashboard-react.css","build-package":"npm run build-package-css && babel src --out-dir dist"},"repository":{"type":"git","url":"git+https://github.com/project-nano"},"keywords":[],"author":"Akumas <<EMAIL>> (https://nanos.cloud/)","license":"MIT","bugs":{"url":"https://github.com/project-nano/releases/issues"},"homepage":"https://nanos.cloud/","optionalDependencies":{"@types/googlemaps":"3.37.3","@types/markerclustererplus":"2.1.33","ajv":"6.10.2","typescript":"3.5.3"},"devDependencies":{"eslint-config-prettier":"6.0.0","eslint-plugin-prettier":"3.1.0","gulp":"4.0.2","gulp-append-prepend":"1.0.8","prettier":"1.18.2"},"browserslist":{"production":[">0.2%","not dead","not op_mini all"],"development":[]}}')}},[[343,1,2]]]);
//# sourceMappingURL=main.851f2e9f.chunk.js.map